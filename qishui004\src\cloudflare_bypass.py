"""
Cloudflare绕过模块
处理Cloudflare 5S盾和Turnstile验证
"""

import time
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class CloudflareBypass:
    """Cloudflare绕过处理类"""
    
    def __init__(self, max_wait_time: int = 30):
        """
        初始化Cloudflare绕过器
        
        Args:
            max_wait_time: 最大等待时间（秒）
        """
        self.max_wait_time = max_wait_time
        
    def wait_for_5s_shield(self, get_page_info_func, get_page_text_func) -> bool:
        """
        等待Cloudflare 5S盾挑战完成
        
        Args:
            get_page_info_func: 获取页面信息的函数
            get_page_text_func: 获取页面文本的函数
            
        Returns:
            bool: 是否成功通过挑战
        """
        logger.info("等待Cloudflare 5S盾挑战完成...")
        
        start_time = time.time()
        while time.time() - start_time < self.max_wait_time:
            try:
                # 获取页面信息
                page_info = get_page_info_func()
                if not page_info:
                    time.sleep(2)
                    continue
                
                title = page_info.get("title", "").lower()
                url = page_info.get("url", "")
                
                # 检查是否还在挑战页面
                if self._is_cloudflare_challenge(title, url, get_page_text_func):
                    logger.info("仍在Cloudflare 5S盾挑战页面，继续等待...")
                    time.sleep(2)
                    continue
                else:
                    logger.info("Cloudflare 5S盾挑战已通过")
                    return True
                    
            except Exception as e:
                logger.error(f"检查Cloudflare挑战状态时出错: {e}")
                time.sleep(1)
                
        logger.error("Cloudflare 5S盾挑战超时")
        return False
    
    def _is_cloudflare_challenge(self, title: str, url: str, get_page_text_func) -> bool:
        """
        检查当前页面是否为Cloudflare挑战页面
        
        Args:
            title: 页面标题
            url: 页面URL
            get_page_text_func: 获取页面文本的函数
            
        Returns:
            bool: 是否为挑战页面
        """
        try:
            # 检查页面标题特征
            challenge_indicators = [
                "checking your browser",
                "cloudflare",
                "ddos protection",
                "security check",
                "please wait",
                "请稍候"
            ]
            
            for indicator in challenge_indicators:
                if indicator in title:
                    return True
            
            # 检查页面内容
            try:
                page_text = get_page_text_func()
                if page_text:
                    page_text = page_text.lower()
                    for indicator in challenge_indicators:
                        if indicator in page_text:
                            return True
            except Exception:
                pass
                
            return False
            
        except Exception as e:
            logger.error(f"检查Cloudflare挑战页面时出错: {e}")
            return False
    
    def handle_turnstile_challenge(self, run_js_func, get_elements_func) -> bool:
        """
        处理Cloudflare Turnstile验证
        
        Args:
            run_js_func: 执行JavaScript的函数
            get_elements_func: 获取页面元素的函数
            
        Returns:
            bool: 是否成功处理Turnstile
        """
        logger.info("处理Cloudflare Turnstile验证...")
        
        try:
            # 查找Turnstile元素
            turnstile_selectors = [
                'iframe[src*="turnstile"]',
                '[data-sitekey]',
                '.cf-turnstile',
                '#cf-turnstile',
                'iframe[src*="cloudflare"]'
            ]
            
            turnstile_found = False
            for selector in turnstile_selectors:
                try:
                    js_code = f"return document.querySelector('{selector}') !== null;"
                    result = run_js_func(js_code)
                    if result:
                        turnstile_found = True
                        logger.info(f"找到Turnstile元素: {selector}")
                        break
                except Exception:
                    continue
            
            if not turnstile_found:
                logger.info("未找到Turnstile元素，可能不需要验证")
                return True
            
            # 等待Turnstile完成
            start_time = time.time()
            while time.time() - start_time < self.max_wait_time:
                try:
                    if self._is_turnstile_completed(run_js_func):
                        logger.info("Turnstile验证已完成")
                        return True
                    
                    time.sleep(1)
                    
                except Exception as e:
                    logger.error(f"检查Turnstile状态时出错: {e}")
                    time.sleep(1)
            
            logger.error("Turnstile验证超时")
            return False
            
        except Exception as e:
            logger.error(f"处理Turnstile验证时出错: {e}")
            return False
    
    def _is_turnstile_completed(self, run_js_func) -> bool:
        """
        检查Turnstile验证是否已完成
        
        Args:
            run_js_func: 执行JavaScript的函数
            
        Returns:
            bool: 是否已完成验证
        """
        try:
            # 检查Turnstile token
            js_code = """
            var tokenInputs = document.querySelectorAll('input[name*="turnstile"], input[name*="cf-turnstile"]');
            for (var i = 0; i < tokenInputs.length; i++) {
                if (tokenInputs[i].value && tokenInputs[i].value.length > 0) {
                    return true;
                }
            }
            
            var turnstileElements = document.querySelectorAll('.cf-turnstile, [data-sitekey]');
            for (var i = 0; i < turnstileElements.length; i++) {
                var element = turnstileElements[i];
                if (element.classList.contains('success') || 
                    element.getAttribute('data-callback') || 
                    element.querySelector('input[value]')) {
                    return true;
                }
            }
            
            return false;
            """
            
            result = run_js_func(js_code)
            return bool(result)
            
        except Exception as e:
            logger.error(f"检查Turnstile完成状态时出错: {e}")
            return False
    
    def get_turnstile_token(self, run_js_func) -> Optional[str]:
        """
        获取Turnstile验证token
        
        Args:
            run_js_func: 执行JavaScript的函数
            
        Returns:
            Optional[str]: Turnstile token，如果获取失败返回None
        """
        try:
            js_code = """
            var tokenInputs = document.querySelectorAll('input[name*="turnstile"], input[name*="cf-turnstile"]');
            for (var i = 0; i < tokenInputs.length; i++) {
                if (tokenInputs[i].value && tokenInputs[i].value.length > 0) {
                    return tokenInputs[i].value;
                }
            }
            return null;
            """
            
            token = run_js_func(js_code)
            if token:
                logger.info(f"获取到Turnstile token: {token[:20]}...")
                return token
            
            logger.warning("未能获取Turnstile token")
            return None
            
        except Exception as e:
            logger.error(f"获取Turnstile token时出错: {e}")
            return None
    
    def wait_for_page_load(self, get_page_info_func, expected_url_part: str = None, timeout: int = 10) -> bool:
        """
        等待页面加载完成
        
        Args:
            get_page_info_func: 获取页面信息的函数
            expected_url_part: 期望的URL部分
            timeout: 超时时间
            
        Returns:
            bool: 是否加载完成
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                page_info = get_page_info_func()
                if page_info:
                    url = page_info.get("url", "")
                    title = page_info.get("title", "")
                    
                    # 检查是否不再是加载页面
                    if title and title != "请稍候..." and "loading" not in title.lower():
                        if expected_url_part:
                            if expected_url_part in url:
                                return True
                        else:
                            return True
                
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"等待页面加载时出错: {e}")
                time.sleep(0.5)
        
        return False
