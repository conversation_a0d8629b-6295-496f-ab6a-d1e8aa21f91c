#!/usr/bin/env python3
"""
测试CF验证集成功能
"""

import time
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockTab:
    """模拟浏览器标签页"""
    def __init__(self):
        self.cf_passed = False
        self.attempt_count = 0
        self.click_count = 0
        
    @property
    def html(self):
        # 模拟CF页面内容，前12次检测返回CF内容，之后返回正常页面
        if self.attempt_count < 12:
            return "Cloudflare checking your browser please wait"
        else:
            return "Welcome to POPPI settlement claim form"
    
    @property 
    def url(self):
        if self.attempt_count < 12:
            return "https://veritaconnect.com/cf-challenge"
        else:
            return "https://veritaconnect.com/poppisettlement/Claimant/Unknown"
    
    def ele(self, xpath, timeout=1):
        # 模拟CF元素检测
        if self.attempt_count < 12 and 'cf-' in xpath:
            return True  # 模拟找到CF元素
        return None
    
    def wait(self, seconds):
        print(f"    等待 {seconds} 秒...")
        time.sleep(0.1)  # 实际测试时缩短等待时间
        self.attempt_count += 1
    
    @property
    def actions(self):
        return MockActions(self)

class MockActions:
    """模拟浏览器动作"""
    def __init__(self, tab):
        self.tab = tab
    
    def move_to(self, coords):
        print(f"    移动鼠标到坐标: {coords}")
        return self
    
    def click(self):
        self.tab.click_count += 1
        print(f"    执行点击操作 (第 {self.tab.click_count} 次点击)")
        return self

def mock_check_next_step_elements(tab, expected_elements):
    """模拟检查下一步元素"""
    # 如果CF已通过，返回True表示找到了下一步元素
    return tab.attempt_count >= 12

def test_cf_integration():
    """测试CF验证集成功能"""
    print("=" * 60)
    print("测试CF验证集成功能")
    print("=" * 60)
    
    # 导入CF检查器
    try:
        from src.cf_check import CloudflareChecker
        print("✅ 成功导入CF检查器")
    except ImportError as e:
        print(f"❌ 导入CF检查器失败: {e}")
        return False
    
    # 创建模拟环境
    mock_tab = MockTab()
    cf_checker = CloudflareChecker(logger)
    
    # 模拟期望的下一步元素
    expected_elements = {
        "申请表单": ["input[type='radio']", "form", ".claim-form"]
    }
    
    print("\n=== 测试场景 ===")
    print("模拟访问POPPI settlement网站")
    print("前12次检测遇到CF盾，第13次检测通过")
    print("每5次检测点击一次坐标 (208, 289)")
    
    # 执行CF验证
    start_time = time.time()
    
    # 模拟handle_cloudflare_shield函数的逻辑
    print("\n开始处理Cloudflare 5秒盾...")
    
    # 首先检查下一步元素是否已经存在
    if mock_check_next_step_elements(mock_tab, expected_elements):
        print(" 下一步元素已存在，跳过CF盾处理")
        result = True
    else:
        print("使用智能5秒盾检测方法...")
        result = cf_checker.pass_cf_check_with_5s_shield(mock_tab, max_attempts=20, click_interval=5)
        
        if result:
            print(" ✅ CF盾处理成功")
            
            # 最终检查下一步元素是否出现
            if mock_check_next_step_elements(mock_tab, expected_elements):
                print(" ✅ 下一步元素已出现，确认CF盾处理成功")
            else:
                print(" ⚠️ CF盾处理完成但下一步元素未出现，可能需要额外等待")
                time.sleep(0.5)  # 模拟额外等待
                result = mock_check_next_step_elements(mock_tab, expected_elements)
        else:
            print(" ❌ CF盾处理失败")
    
    end_time = time.time()
    
    print(f"\n=== 测试结果 ===")
    print(f"验证结果: {'✅ 成功' if result else '❌ 失败'}")
    print(f"总检测次数: {mock_tab.attempt_count}")
    print(f"总点击次数: {mock_tab.click_count}")
    print(f"测试耗时: {end_time - start_time:.2f} 秒")
    
    if result and mock_tab.click_count > 0:
        print("\n🎉 CF验证集成功能测试通过！")
        print("✅ 成功模拟了5秒盾检测和智能点击")
        print("✅ 正确识别了CF验证完成状态")
        print("✅ 成功检测到下一步元素出现")
        return True
    else:
        print("\n❌ CF验证集成功能测试失败！")
        return False

if __name__ == "__main__":
    success = test_cf_integration()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！CF验证集成功能正常工作")
        print("现在可以在实际环境中使用新的CF处理逻辑")
    else:
        print("⚠️  测试失败，请检查代码逻辑")
    print("=" * 60)
