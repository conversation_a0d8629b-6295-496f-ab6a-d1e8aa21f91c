"""
多线程管理模块
提供线程池管理和任务队列功能
"""

import threading
import queue
import time
import logging
from typing import Callable, Any, Dict, List, Optional
from concurrent.futures import ThreadPoolExecutor, Future

logger = logging.getLogger(__name__)


class ThreadManager:
    """多线程管理类"""
    
    def __init__(self, max_workers: int = 5):
        """
        初始化线程管理器
        
        Args:
            max_workers: 最大工作线程数
        """
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.task_queue = queue.Queue()
        self.results = {}
        self.running_tasks = {}
        self.task_counter = 0
        self.lock = threading.Lock()
        
    def submit_task(self, func: Callable, *args, **kwargs) -> str:
        """
        提交任务到线程池
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            str: 任务ID
        """
        with self.lock:
            self.task_counter += 1
            task_id = f"task_{self.task_counter}"
        
        future = self.executor.submit(func, *args, **kwargs)
        self.running_tasks[task_id] = future
        
        logger.info(f"提交任务: {task_id}")
        return task_id
    
    def get_task_result(self, task_id: str, timeout: Optional[float] = None) -> Any:
        """
        获取任务结果
        
        Args:
            task_id: 任务ID
            timeout: 超时时间
            
        Returns:
            Any: 任务结果
        """
        if task_id not in self.running_tasks:
            raise ValueError(f"任务不存在: {task_id}")
        
        future = self.running_tasks[task_id]
        try:
            result = future.result(timeout=timeout)
            self.results[task_id] = result
            return result
        except Exception as e:
            logger.error(f"获取任务结果失败: {task_id}, 错误: {e}")
            raise
    
    def is_task_done(self, task_id: str) -> bool:
        """
        检查任务是否完成
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否完成
        """
        if task_id not in self.running_tasks:
            return False
        
        return self.running_tasks[task_id].done()
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        if task_id not in self.running_tasks:
            return False
        
        return self.running_tasks[task_id].cancel()
    
    def get_all_task_status(self) -> Dict[str, str]:
        """
        获取所有任务状态
        
        Returns:
            Dict[str, str]: 任务状态字典
        """
        status = {}
        for task_id, future in self.running_tasks.items():
            if future.done():
                if future.cancelled():
                    status[task_id] = "cancelled"
                elif future.exception():
                    status[task_id] = "failed"
                else:
                    status[task_id] = "completed"
            else:
                status[task_id] = "running"
        
        return status
    
    def wait_for_all_tasks(self, timeout: Optional[float] = None) -> bool:
        """
        等待所有任务完成
        
        Args:
            timeout: 超时时间
            
        Returns:
            bool: 是否所有任务都完成
        """
        try:
            futures = list(self.running_tasks.values())
            for future in futures:
                future.result(timeout=timeout)
            return True
        except Exception as e:
            logger.error(f"等待任务完成时出错: {e}")
            return False
    
    def shutdown(self, wait: bool = True):
        """
        关闭线程池
        
        Args:
            wait: 是否等待正在执行的任务完成
        """
        self.executor.shutdown(wait=wait)
        logger.info("线程池已关闭")


class TaskQueue:
    """任务队列管理类"""
    
    def __init__(self, max_size: int = 100):
        """
        初始化任务队列
        
        Args:
            max_size: 队列最大大小
        """
        self.queue = queue.Queue(maxsize=max_size)
        self.processing = False
        self.worker_thread = None
        self.results = {}
        self.lock = threading.Lock()
        
    def add_task(self, task_data: Dict[str, Any]) -> bool:
        """
        添加任务到队列
        
        Args:
            task_data: 任务数据
            
        Returns:
            bool: 是否成功添加
        """
        try:
            self.queue.put(task_data, block=False)
            logger.info(f"任务已添加到队列: {task_data.get('id', 'unknown')}")
            return True
        except queue.Full:
            logger.error("任务队列已满")
            return False
    
    def start_processing(self, processor_func: Callable):
        """
        开始处理队列中的任务
        
        Args:
            processor_func: 任务处理函数
        """
        if self.processing:
            logger.warning("任务处理已在进行中")
            return
        
        self.processing = True
        self.worker_thread = threading.Thread(
            target=self._process_tasks,
            args=(processor_func,),
            daemon=True
        )
        self.worker_thread.start()
        logger.info("开始处理任务队列")
    
    def _process_tasks(self, processor_func: Callable):
        """
        处理任务的内部方法
        
        Args:
            processor_func: 任务处理函数
        """
        while self.processing:
            try:
                task_data = self.queue.get(timeout=1)
                task_id = task_data.get('id', 'unknown')
                
                logger.info(f"开始处理任务: {task_id}")
                try:
                    result = processor_func(task_data)
                    with self.lock:
                        self.results[task_id] = {
                            "status": "completed",
                            "result": result,
                            "timestamp": time.time()
                        }
                    logger.info(f"任务处理完成: {task_id}")
                except Exception as e:
                    logger.error(f"任务处理失败: {task_id}, 错误: {e}")
                    with self.lock:
                        self.results[task_id] = {
                            "status": "failed",
                            "error": str(e),
                            "timestamp": time.time()
                        }
                finally:
                    self.queue.task_done()
                    
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"处理任务时出错: {e}")
    
    def stop_processing(self):
        """停止处理任务"""
        self.processing = False
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
        logger.info("任务处理已停止")
    
    def get_task_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict[str, Any]]: 任务结果
        """
        with self.lock:
            return self.results.get(task_id)
    
    def get_queue_size(self) -> int:
        """获取队列大小"""
        return self.queue.qsize()
    
    def is_empty(self) -> bool:
        """检查队列是否为空"""
        return self.queue.empty()
    
    def clear_results(self):
        """清空结果缓存"""
        with self.lock:
            self.results.clear()
        logger.info("结果缓存已清空")
