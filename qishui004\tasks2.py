#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import requests
import json
import threading
from DrissionPage import ChromiumOptions, Chromium
from src.email_code_manager import get_email_verification_code, test_email_api
from src.file_handler import file_handler
from src.phone_manager import get_phone_number, get_phone_verification_code, release_phone_number

def smart_type_text(tab, element, text):
    """
    智能输入函数 - 参考1.84版本

    Args:
        tab: DrissionPage tab对象
        element: 输入元素
        text: 要输入的文本
    """
    try:
        # 使用常规输入方式
        element.clear()
        element.input(text)
        print(f"✅ 智能输入成功: {text}")
    except Exception as e:
        print(f"❌ 智能输入失败: {e}")
        raise

# 简单的窗口锁管理器
class SimpleWindowLockManager:
    def __init__(self):
        self.locks = {}
        self.lock = threading.Lock()

    def acquire_window_lock(self, thread_id, operation, timeout=30):
        """获取窗口锁"""
        with self.lock:
            if operation not in self.locks:
                self.locks[operation] = thread_id
                print(f"🔒 线程 {thread_id} 获取 {operation} 锁成功")
                return True
            else:
                print(f"❌ 线程 {thread_id} 获取 {operation} 锁失败，已被线程 {self.locks[operation]} 占用")
                return False

    def release_window_lock(self, thread_id, operation):
        """释放窗口锁"""
        with self.lock:
            if operation in self.locks and self.locks[operation] == thread_id:
                del self.locks[operation]
                print(f"🔓 线程 {thread_id} 释放 {operation} 锁成功")
                return True
            else:
                print(f"⚠️ 线程 {thread_id} 释放 {operation} 锁失败")
                return False

    def is_window_locked(self, thread_id, operation):
        """检查窗口是否被锁定"""
        with self.lock:
            return operation in self.locks and self.locks[operation] == thread_id

# 全局窗口锁管理器
window_lock_manager = SimpleWindowLockManager()

# 州简写到全称的映射表
STATE_MAPPING = {
    'AL': 'Alabama',
    'AK': 'Alaska', 
    'AZ': 'Arizona',
    'AR': 'Arkansas',
    'CA': 'California',
    'CO': 'Colorado',
    'CT': 'Connecticut',
    'DE': 'Delaware',
    'DC': 'District Of Columbia',
    'FL': 'Florida',
    'GA': 'Georgia',
    'HI': 'Hawaii',
    'ID': 'Idaho',
    'IL': 'Illinois',
    'IN': 'Indiana',
    'IA': 'Iowa',
    'KS': 'Kansas',
    'KY': 'Kentucky',
    'LA': 'Louisiana',
    'ME': 'Maine',
    'MD': 'Maryland',
    'MA': 'Massachusetts',
    'MI': 'Michigan',
    'MN': 'Minnesota',
    'MS': 'Mississippi',
    'MO': 'Missouri',
    'MT': 'Montana',
    'NE': 'Nebraska',
    'NV': 'Nevada',
    'NH': 'New Hampshire',
    'NJ': 'New Jersey',
    'NM': 'New Mexico',
    'NY': 'New York',
    'NC': 'North Carolina',
    'ND': 'North Dakota',
    'OH': 'Ohio',
    'OK': 'Oklahoma',
    'OR': 'Oregon',
    'PA': 'Pennsylvania',
    'RI': 'Rhode Island',
    'SC': 'South Carolina',
    'SD': 'South Dakota',
    'TN': 'Tennessee',
    'TX': 'Texas',
    'UT': 'Utah',
    'VT': 'Vermont',
    'VA': 'Virginia',
    'WA': 'Washington',
    'WV': 'West Virginia',
    'WI': 'Wisconsin',
    'WY': 'Wyoming'
}

def read_data_file(filename):
    """读取数据文件并解析"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        data_list = []
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:  # 跳过空行
                continue
                
            parts = line.split('----')
            if len(parts) >= 10:
                data = {
                    'first_name': parts[0].strip(),
                    'last_name': parts[1].strip(),
                    'street_address': parts[2].strip(),
                    'city': parts[3].strip(),
                    'state': parts[4].strip(),
                    'zip_code': parts[5].strip(),
                    'number1': parts[6].strip(),
                    'number2': parts[7].strip(),
                    'phone': parts[8].strip(),
                    'email': parts[9].strip(),
                    'line_number': line_num
                }
                
                # 验证州代码
                if data['state'] not in STATE_MAPPING:
                    print(f"警告: 第{line_num}行的州代码 '{data['state']}' 不在映射表中")
                    continue
                    
                data_list.append(data)
            else:
                print(f"警告: 第{line_num}行数据格式不正确，跳过")
                
        return data_list
    except Exception as e:
        print(f"读取数据文件失败: {e}")
        return []

def connect_bit_browser():
    """连接比特浏览器"""
    try:
        # 检查比特浏览器服务
        print("检查比特浏览器服务...")
        headers = {'Content-Type': 'application/json'}
        response = requests.post("http://127.0.0.1:54345/browser/list", headers=headers, timeout=5)
        print("比特浏览器服务连接正常")
        
        # 获取浏览器列表
        list_data = {"page": 0, "pageSize": 100}
        response = requests.post("http://127.0.0.1:54345/browser/list", 
                               data=json.dumps(list_data), 
                               headers=headers)
        
        response_json = response.json()
        if 'data' in response_json:
            browsers = response_json['data']['list']
        else:
            browsers = response_json
            
        if not browsers:
            print("没有找到浏览器，请先创建浏览器")
            return None, None
        
        # 使用第一个浏览器
        browser_id = browsers[0]['id']
        print(f"使用浏览器ID: {browser_id}")
        
        # 打开浏览器
        open_data = {"id": browser_id}
        open_response = requests.post(f"http://127.0.0.1:54345/browser/open", 
                                    data=json.dumps(open_data), 
                                    headers=headers)
        
        if open_response.status_code != 200:
            print(f"打开浏览器失败: {open_response.text}")
            return None, None
        
        open_response_json = open_response.json()
        if 'data' not in open_response_json:
            print("打开浏览器失败: 响应中没有data字段")
            return None, None
        
        driver = open_response_json['data']['driver']
        http = open_response_json['data']['http']
        print(f"获取到调试端口信息: {http}")
        
        # 等待浏览器启动
        time.sleep(3)
        
        # 使用DrissionPage连接
        co = ChromiumOptions()
        co.set_browser_path(driver)
        co.set_address(http)
        
        browser = Chromium(co)
        tab = browser.latest_tab
        
        return browser, tab
        
    except Exception as e:
        print(f"连接比特浏览器失败: {e}")
        return None, None

def handle_payment_selection(tab, data):
    """处理 Payment Selection 部分 - 参考1.84版本的逻辑"""
    try:
        print("开始处理 Payment Selection...")

        # 等待页面稳定
        time.sleep(3)

        # 查找 GET A PREPAID MASTERCARD 按钮
        print("查找 GET A PREPAID MASTERCARD 按钮...")

        # 首先检查是否有 iframe
        iframes = tab.eles('t:iframe')
        print(f"找到 {len(iframes)} 个 iframe")

        prepaid_button_found = False

        # 在所有 iframe 中查找按钮，特别关注 digitaldisbursements.com
        for i, iframe in enumerate(iframes):
            try:
                # 检查iframe的src属性
                src = iframe.attr('src') or ''
                print(f"检查第 {i+1} 个 iframe: {src[:100]}...")

                # 如果是 digitaldisbursements.com 的iframe，重点检查
                if 'digitaldisbursements.com' in src.lower():
                    print(f"*** 找到支付相关 iframe (digitaldisbursements): {src[:100]}... ***")

                    # 按优先级排序的选择器列表（参考1.84版本）
                    button_selectors = [
                        'x://*[@data-test="lnkPaymentMethodEMasterCard"]',
                        'x://button[@data-test="lnkPaymentMethodEMasterCard"]',
                        'x://*[contains(@class, "MuiButtonBase-root") and @data-test="lnkPaymentMethodEMasterCard"]',
                        'x://button[contains(text(), "GET A PREPAID MASTERCARD")]',
                        'x://*[contains(@class, "MuiButton-label-135") and contains(text(), "GET A PREPAID MASTERCARD")]',
                        'x://span[@class="MuiButton-label-135" and contains(text(), "GET A PREPAID MASTER")]',
                        'x://button[contains(text(), "GET A PREPAID MASTER")]',
                        'x://*[contains(@class, "MuiButton-root") and contains(text(), "PREPAID MASTER")]'
                    ]

                    for selector_idx, selector in enumerate(button_selectors):
                        try:
                            button = iframe.ele(selector, timeout=2)
                            if button:
                                button_text = button.text or ""
                                button_id = button.attr('id') or ""
                                button_class = button.attr('class') or ""
                                data_test = button.attr('data-test') or ""

                                print(f"在第 {i+1} 个 iframe 中找到按钮:")
                                print(f"  选择器: {selector}")
                                print(f"  文本: {button_text}")
                                print(f"  data-test: {data_test}")

                                # 验证按钮有效性
                                text_valid = any(text in button_text for text in [
                                    "GET A PREPAID MASTERCARD",
                                    "GET A PREPAID MASTER"
                                ])
                                data_test_valid = "lnkPaymentMethodEMasterCard" in data_test

                                if text_valid or data_test_valid:
                                    print(f"验证通过，点击按钮...")
                                    button.click()
                                    time.sleep(3)
                                    prepaid_button_found = True

                                    # 进入验证流程
                                    print("按钮点击成功，进入验证流程...")
                                    return handle_verification_flow(tab, iframe, data)
                                else:
                                    print(f"按钮验证失败，继续查找...")

                        except Exception as e:
                            print(f"选择器 {selector} 查找失败: {e}")
                            continue
                else:
                    # 对于非 digitaldisbursements.com 的iframe，也简单检查一下
                    try:
                        button = iframe.ele('x://button[contains(text(), "GET A PREPAID")]', timeout=0.5)
                        if button:
                            print(f"在第 {i+1} 个非支付iframe中也找到了相关按钮")
                            button_text = button.text or ""
                            if "PREPAID" in button_text:
                                print(f"点击按钮: {button_text}")
                                button.click()
                                time.sleep(3)
                                prepaid_button_found = True
                                return handle_verification_flow(tab, iframe, data)
                    except:
                        pass

            except Exception as e:
                print(f"检查第 {i+1} 个 iframe 时出错: {e}")
                continue

        if not prepaid_button_found:
            print("未找到 GET A PREPAID MASTERCARD 按钮，可能已自动跳转")
            return True  # 继续处理其他部分

    except Exception as e:
        print(f"处理 Payment Selection 时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def handle_verification_flow(tab, payment_iframe, data):
    """处理验证流程：智能邮件--邮件验证码--手机号码--短信 (参考1.84版本)"""
    print("🧠 启动智能验证流程（线性流程模式）")
    print("📋 线性流程：邮件输入 → 邮件验证码（可选）→ 手机号码（条件性）→ 手机验证码 → close")

    # 🔧 获取窗口锁，覆盖整个验证流程
    thread_id = str(threading.get_ident())
    if not window_lock_manager.acquire_window_lock(thread_id, "verification_flow", timeout=30):
        print(f"❌ 无法获取验证流程锁，可能其他窗口正在操作")
        return False

    try:
        # 等待页面稳定
        time.sleep(3)

        # 步骤1：邮件输入（必须步骤）
        print("🔥 步骤1：邮件输入（必须步骤）")
        if not handle_email_input_step(tab, data['email']):
            print("❌ 邮箱输入失败")
            return False

        # 调用智能验证流程处理
        result = smart_verification_flow(tab, data['email'], data)

        if result.get('success'):
            print("✅ 智能验证流程完成")
            completed_steps = result.get('completed_steps', [])
            print(f"完成的步骤: {completed_steps}")

            # 🔧 验证流程完成后解锁
            window_lock_manager.release_window_lock(thread_id, "verification_flow")
            print(f"🔓 验证流程完成后释放验证流程锁")
            return True
        else:
            error_msg = result.get('error', '未知错误')
            print(f"❌ 智能验证流程失败: {error_msg}")
            return False

    except Exception as e:
        print(f"❌ 智能验证流程异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 🔧 确保异常情况下也释放锁
        try:
            if window_lock_manager.is_window_locked(thread_id, "verification_flow"):
                window_lock_manager.release_window_lock(thread_id, "verification_flow")
                print(f"🔓 异常情况下释放验证流程锁")
        except Exception as lock_error:
            print(f"⚠️ 释放锁时出错: {lock_error}")

def check_verification_state(tab):
    """检查验证页面状态"""
    try:
        state = {
            'needs_email_verification': False,
            'needs_phone_input': False,
            'verification_complete': False
        }

        # 检查邮件验证码输入框
        email_code_selectors = [
            'x://input[contains(@name, "code")]',
            'x://input[contains(@id, "code")]',
            'x://input[contains(@placeholder, "code")]',
            'x://input[contains(@placeholder, "verification")]'
        ]

        for selector in email_code_selectors:
            try:
                if tab.ele(selector, timeout=1):
                    state['needs_email_verification'] = True
                    break
            except:
                continue

        # 检查手机号输入框
        phone_selectors = [
            'x://input[@type="tel"]',
            'x://input[contains(@name, "phone")]',
            'x://input[contains(@id, "phone")]',
            'x://input[contains(@placeholder, "phone")]'
        ]

        for selector in phone_selectors:
            try:
                if tab.ele(selector, timeout=1):
                    state['needs_phone_input'] = True
                    break
            except:
                continue

        # 检查完成按钮
        completion_selectors = [
            'x://button[contains(text(), "Close")]',
            'x://button[contains(text(), "Done")]',
            'x://button[contains(text(), "Complete")]'
        ]

        for selector in completion_selectors:
            try:
                if tab.ele(selector, timeout=1):
                    state['verification_complete'] = True
                    break
            except:
                continue

        return state

    except Exception as e:
        print(f"检查验证状态失败: {e}")
        return {'needs_email_verification': False, 'needs_phone_input': False, 'verification_complete': False}

def handle_email_input_step(tab, email):
    """处理邮箱输入步骤 - 参考1.84版本的iframe遍历逻辑"""
    try:
        print(f"输入邮箱: {email}")

        # 获取所有iframe
        iframes = tab.eles('t:iframe')
        print(f"找到 {len(iframes)} 个iframe")

        # 从第3个iframe开始检查（跳过前两个，参考1.84版本）
        for i, iframe in enumerate(iframes[2:], start=3):
            try:
                print(f"正在检查第 {i} 个iframe的邮件输入框")

                if not iframe:
                    print(f"第 {i} 个iframe无效，跳过")
                    continue

                # 使用1.84版本的元素特征查找邮箱输入框
                email_input = None
                next_button = None

                # 方法1：查找带有特定aria-label的输入框
                try:
                    email_input = iframe.ele('x://input[@aria-label="Your email"]', timeout=3)
                    if email_input:
                        print(f"在第 {i} 个iframe中找到aria-label邮件输入框")
                except:
                    pass

                # 方法2：查找包含特定文本的邮箱输入框
                if not email_input:
                    try:
                        email_text_elements = iframe.eles('x://*[contains(text(), "What email address do you want to use for the delivery of your Prepaid Mastercard payment?")]', timeout=2)
                        if email_text_elements:
                            print(f"在第 {i} 个iframe中找到邮箱提示文本")
                            # 如果找到文本，再查找附近的输入框
                            email_input = iframe.ele('x://input[@type="text" or @type="email"]', timeout=2)
                    except:
                        pass

                # 方法3：通用邮箱输入框查找
                if not email_input:
                    try:
                        email_selectors = [
                            'x://input[@type="email"]',
                            'x://input[contains(@name, "email")]',
                            'x://input[contains(@id, "email")]',
                            'x://input[contains(@placeholder, "email")]',
                            'x://input[@type="text"]'
                        ]

                        for selector in email_selectors:
                            try:
                                email_input = iframe.ele(selector, timeout=1)
                                if email_input:
                                    print(f"在第 {i} 个iframe中找到邮件输入框: {selector}")
                                    break
                            except:
                                continue
                    except:
                        pass

                # 查找NEXT按钮
                try:
                    next_button = iframe.ele('x://span[@class="MuiButton-label-252"][contains(text(), "NEXT")]', timeout=3)
                    if not next_button:
                        # 尝试其他NEXT按钮选择器
                        next_selectors = [
                            'x://button[contains(text(), "NEXT")]',
                            'x://span[contains(text(), "NEXT")]',
                            'x://button[contains(text(), "Next")]',
                            'x://button[contains(text(), "Continue")]'
                        ]
                        for selector in next_selectors:
                            try:
                                next_button = iframe.ele(selector, timeout=1)
                                if next_button:
                                    print(f"在第 {i} 个iframe中找到NEXT按钮: {selector}")
                                    break
                            except:
                                continue
                except:
                    pass

                # 如果找到了邮箱输入框和NEXT按钮
                if email_input and next_button:
                    print(f"在第 {i} 个iframe中找到邮件输入框和NEXT按钮")

                    # 输入邮箱
                    try:
                        email_input.clear()
                        email_input.input(email)
                        print(f"📧 邮件地址填写成功: {email}")

                        # 点击NEXT按钮
                        next_button.click()
                        print("✅ 点击NEXT按钮成功")
                        time.sleep(5)

                        return True
                    except Exception as input_error:
                        print(f"输入邮箱或点击按钮失败: {input_error}")
                        continue
                else:
                    print(f"第 {i} 个iframe中未找到完整的邮箱输入元素")

            except Exception as iframe_e:
                print(f"检查第 {i} 个iframe时出错: {iframe_e}")
                continue

        print("❌ 未在任何iframe中找到邮件输入框")
        return False

    except Exception as e:
        print(f"❌ 邮箱输入步骤失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_verification_state(tab, context=None):
    """
    检查当前页面的验证状态 - 参考1.84版本的智能上下文感知检测

    Args:
        tab: DrissionPage tab对象
        context: 上下文信息，包含刚完成的步骤

    Returns:
        dict: 包含各种状态标志的字典
    """
    state = {
        'needs_email_verification': False,
        'needs_phone_input': False,
        'needs_phone_verification': False,
        'verification_complete': False,
        'page_info': ''
    }

    # 上下文感知：记录刚完成的步骤
    just_completed_email_input = context and context.get('just_completed_email_input', False)
    just_completed_phone_input = context and context.get('just_completed_phone_input', False)

    try:
        # 检查所有iframe
        iframes = tab.eles('t:iframe')
        print(f"🔍 检查页面状态，找到 {len(iframes)} 个iframe")
        if context:
            print(f"🎯 上下文信息: 刚完成邮件输入={just_completed_email_input}, 刚完成手机输入={just_completed_phone_input}")

        for i, iframe in enumerate(iframes[2:], start=3):
            try:
                # 优先级1：检查手机号输入框（最高优先级）- 根据截图，这是当前状态
                phone_input_selectors = [
                    'x://input[@type="tel"]',
                    'x://input[contains(@placeholder, "phone") or contains(@placeholder, "Phone")]',
                    'x://input[contains(@placeholder, "mobile") or contains(@placeholder, "Mobile")]',
                    'x://input[contains(@name, "phone")]',
                    'x://input[contains(@aria-label, "phone")]'
                ]

                for selector in phone_input_selectors:
                    try:
                        phone_input = iframe(selector, timeout=1)
                        if phone_input:
                            print(f"🎯 在第{i}个iframe检测到手机号输入框: {selector}")
                            # 检查页面内容确认是手机号输入
                            iframe_text = iframe.html.lower() if iframe else ""
                            phone_keywords = ['phone', 'mobile', 'verification code', 'virtual mastercard', 'payment details']
                            if any(keyword in iframe_text for keyword in phone_keywords):
                                print(f"✅ 确认为手机号输入页面，关键词匹配")
                                state['needs_phone_input'] = True
                                state['page_info'] = f'第{i}个iframe检测到手机号输入需求'
                                return state
                    except:
                        continue

                # 优先级2：上下文感知的手机验证码检测
                if just_completed_phone_input:
                    print("🎯 上下文感知：刚完成手机号输入，优先检测手机验证码")

                    phone_code_selectors = [
                        'x://input[@aria-label="Verification code"]',
                        'x://input[@data-test="divVerificationCode"]',
                        'x://input[contains(@class, "MuiInputBase-input-241")]',
                        'x://input[contains(@class, "MuiInput-input-229")]',
                        'x://input[@type="text" and (contains(@placeholder, "验证码") or contains(@placeholder, "code"))]'
                    ]

                    for selector in phone_code_selectors:
                        try:
                            phone_code_input = iframe(selector, timeout=1)
                            if phone_code_input:
                                print(f"✅ 上下文优先检测：发现手机验证码输入框，选择器: {selector}")
                                state['needs_phone_verification'] = True
                                state['page_info'] = f'第{i}个iframe检测到手机验证码输入框（上下文优先）'
                                return state
                        except:
                            continue

                # 优先级3：检查邮件验证码输入框（常规优先级）
                email_code_input = iframe('x://input[@data-test="divVerificationCode" or contains(@class, "MuiInputBase-input-241") or contains(@class, "MuiInput-input-229")]', timeout=1)

                if email_code_input:
                    # 上下文感知：如果刚完成手机号输入，不应该检测邮箱验证码
                    if just_completed_phone_input:
                        print("⚠️ 上下文感知：刚完成手机号输入，将此输入框视为手机验证码而非邮箱验证码")
                        state['needs_phone_verification'] = True
                        state['page_info'] = f'第{i}个iframe检测到手机验证码输入框（上下文强制识别）'
                        return state
                    else:
                        # 检查是否真的是邮件验证码
                        iframe_text = iframe.html.lower() if iframe else ""
                        email_indicators = ['email', 'mail', 'address', '@', 'prepaid', 'mastercard']
                        phone_indicators = ['phone', 'mobile', 'cell', 'sms', '+1', 'telephone']

                        email_score = sum(1 for indicator in email_indicators if indicator in iframe_text)
                        phone_score = sum(1 for indicator in phone_indicators if indicator in iframe_text)

                        print(f"🔍 验证码类型判断 - 邮件相关度:{email_score}, 手机相关度:{phone_score}")

                        # 如果邮件相关度更高，或者有明确的邮件特征，认为是邮件验证码
                        if email_score >= phone_score or any(indicator in iframe_text for indicator in ['email', 'mail', '@']):
                            state['needs_email_verification'] = True
                            state['page_info'] = f'第{i}个iframe检测到邮箱验证码输入框（已确认）'
                            return state
                        else:
                            state['needs_phone_verification'] = True
                            state['page_info'] = f'第{i}个iframe检测到手机验证码输入框（内容判断）'
                            return state

                # 检查完成按钮 - 增强检测
                close_selectors = [
                    'x://span[contains(@class, "MuiTouchRipple-root")]',
                    'x://button[contains(text(), "Close")]',
                    'x://button[contains(text(), "close")]',
                    'x://button[contains(text(), "CLOSE")]',
                    'x://button[contains(text(), "Complete")]',
                    'x://button[contains(text(), "Finish")]',
                    'x://button[contains(text(), "Done")]',
                    'x://*[contains(@class, "close") or contains(@class, "Close")]'
                ]

                for close_selector in close_selectors:
                    try:
                        close_button = iframe(close_selector, timeout=1)
                        if close_button:
                            button_text = close_button.text or ""
                            print(f"🎯 在第{i}个iframe检测到完成按钮: {close_selector}")
                            print(f"   按钮文本: '{button_text}'")
                            state['verification_complete'] = True
                            state['page_info'] = f'第{i}个iframe检测到完成按钮: {button_text}'
                            return state
                    except:
                        continue

            except Exception as e:
                continue

        print("⚠️ 未检测到明确的页面状态")
        return state

    except Exception as e:
        print(f"❌ 检查页面状态时出错: {e}")
        return state


def smart_verification_flow(tab, email, data):
    """
    智能验证流程处理 - 参考1.84版本的智能处理方案

    正确的线性流程：
    1. 邮件输入（必须步骤）
    2. 检查邮件验证码（可能有，可能没有）
       - 如果有邮件验证码 → 执行邮件验证码流程 → 检查close按钮 → 如果有close就结束流程
       - 如果没有邮件验证码 → 直接跳到手机号码步骤
    3. 手机号码输入（只在没有close按钮时执行）
    4. 手机验证码
    5. close按钮（最终完成）

    Args:
        tab: DrissionPage tab对象
        email: 邮箱地址
        data: 包含手机号等信息的数据字典

    Returns:
        dict: 包含处理结果和相关数据
    """
    print("🧠 启动智能验证流程（线性流程模式）")
    print("📋 线性流程：邮件输入 → 邮件验证码（可选）→ 手机号码（条件性）→ 手机验证码 → close")

    try:
        # 步骤1：邮件输入（已在调用前完成）
        print("✅ 邮箱输入已完成，等待页面响应...")
        time.sleep(3)  # 等待页面加载

        # 步骤2：检查邮件验证码（可能有，可能没有）
        print("🔍 步骤2：检查是否需要邮件验证码...")

        # 设置上下文：刚完成邮件输入
        context = {'just_completed_email_input': True, 'just_completed_phone_input': False}

        has_email_verification = False
        max_email_check_attempts = 5

        for attempt in range(max_email_check_attempts):
            print(f"🔍 第{attempt+1}次检查邮件验证码需求...")

            page_state = check_verification_state(tab, context)
            if page_state['needs_email_verification']:
                has_email_verification = True
                print("📧 检测到邮件验证码需求，处理邮件验证码...")
                break
            elif page_state['needs_phone_input']:
                print("📱 检测到手机号输入需求，跳过邮件验证码")
                break
            elif page_state['verification_complete']:
                print("✅ 检测到流程已完成，可能邮件输入后直接完成")
                return {'success': True, 'completed_steps': ['email_input', 'direct_complete']}
            else:
                print(f"⏳ 页面状态不明确，等待加载... ({attempt+1}/{max_email_check_attempts})")
                time.sleep(2)

        # 处理邮件验证码（如果有）
        if has_email_verification:
            print("🔥 步骤2a：执行邮件验证码流程")
            email_verification_result = handle_email_verification_step(tab, email, data)

            if not email_verification_result:
                print("❌ 邮箱验证码处理失败")
                return {'success': False, 'error': '邮箱验证码处理失败'}

            # 检查是否需要跳过手机号流程
            if isinstance(email_verification_result, dict) and email_verification_result.get('skip_phone_flow'):
                print("🎉 邮件验证完成后检测到close按钮，跳过手机号流程")
                if email_verification_result.get('submit_triggered'):
                    print("✅ Submit按钮已触发，流程完成")
                    return {
                        'success': True,
                        'phone_value': None,
                        'completed_steps': ['email_input', 'email_verification', 'close_button_skip', 'submit_triggered'],
                        'submit_triggered': True,
                        'claim_id': email_verification_result.get('claim_id')
                    }
                else:
                    print("⚠️ Submit按钮未触发，但close按钮已点击")
                    return {
                        'success': True,
                        'phone_value': None,
                        'completed_steps': ['email_input', 'email_verification', 'close_button_skip'],
                        'submit_triggered': False
                    }

            # 检查是否有close按钮（跳过手机号流程）
            print("🔍 检查是否可以跳过手机号流程...")
            time.sleep(2)

            # 更新上下文：刚完成邮件验证码
            context = {'just_completed_email_input': False, 'just_completed_phone_input': False}
            page_state = check_verification_state(tab, context)
            if page_state['verification_complete']:
                print("🎉 邮件验证码完成后检测到close按钮，验证流程完成")
                print("🔥 现在需要触发最终的submit按钮提交表单...")

                # 触发最终的submit按钮
                submit_result = trigger_final_submit_button(tab)
                if submit_result['success']:
                    print("✅ 最终submit按钮触发成功")
                    return {
                        'success': True,
                        'phone_value': None,
                        'completed_steps': ['email_input', 'email_verification', 'close_button_skip', 'final_submit'],
                        'submit_triggered': True
                    }
                else:
                    print("⚠️ 最终submit按钮触发失败，但验证流程已完成")
                    return {
                        'success': True,
                        'phone_value': None,
                        'completed_steps': ['email_input', 'email_verification', 'close_button_skip'],
                        'submit_triggered': False
                    }

            print("✅ 邮件验证码处理完成，继续手机号流程")
        else:
            print("ℹ️ 没有邮件验证码步骤，直接进入手机号流程")

        # 步骤3：手机号码输入（只在没有close按钮时执行）
        print("📱 步骤3：手机号码输入...")

        # 确保页面状态稳定
        time.sleep(2)

        # 检查是否需要手机号输入 - 使用正确的上下文
        needs_phone_input = False
        max_phone_check_attempts = 3
        context = {'just_completed_email_input': True, 'just_completed_phone_input': False}

        for attempt in range(max_phone_check_attempts):
            print(f"🔍 第{attempt+1}次检查手机号输入需求...")

            page_state = check_verification_state(tab, context)
            if page_state['needs_phone_input']:
                needs_phone_input = True
                print("📱 检测到手机号输入需求")
                break
            elif page_state['verification_complete']:
                print("✅ 检测到流程已完成，可能邮件验证码后直接完成")
                return {'success': True, 'completed_steps': ['email_input', 'email_verification', 'direct_complete']}
            else:
                print(f"⏳ 页面状态不明确，等待加载... ({attempt+1}/{max_phone_check_attempts})")
                time.sleep(2)

        if needs_phone_input:
            phone_result = handle_phone_input_step(tab, data)
            if not phone_result.get('success'):
                print("❌ 手机号输入失败")
                return {'success': False, 'error': '手机号输入失败'}

            phone_value = phone_result.get('phone_value')
            print(f"✅ 手机号输入完成: {phone_value}")

            # 步骤4：手机验证码
            print("📲 步骤4：手机验证码...")
            if not handle_phone_verification_step(tab, phone_value):
                print("❌ 手机验证码处理失败")
                return {'success': False, 'error': '手机验证码处理失败'}

            print("✅ 手机验证码处理完成")

            # 步骤5：完成步骤
            print("🏁 步骤5：完成步骤...")
            completion_result = handle_completion_step(tab)

            if completion_result:
                print("✅ 验证流程全部完成")
                return {
                    'success': True,
                    'phone_value': phone_value,
                    'completed_steps': ['email_input', 'phone_input', 'phone_verification', 'completion']
                }
            else:
                print("❌ 完成步骤处理失败")
                return {'success': False, 'error': '完成步骤处理失败'}
        else:
            print("ℹ️ 不需要手机号输入，流程可能已完成")
            return {'success': True, 'completed_steps': ['email_input', 'no_phone_needed']}

    except Exception as e:
        print(f"❌ 智能验证流程异常: {e}")
        return {'success': False, 'error': f'智能验证流程异常: {e}'}


def handle_email_verification_step(tab, email, data=None):
    """处理邮件验证码步骤 - 参考1.84版本的iframe遍历逻辑"""
    try:
        print("📧 开始处理邮箱验证码")

        # 等待验证码输入框出现
        time.sleep(5)

        # 查找邮箱验证码输入框
        iframes = tab.eles('t:iframe')
        for i, iframe in enumerate(iframes[2:], start=3):
            try:
                print(f"正在第 {i} 个iframe中查找邮件验证码输入框...")

                # 方法1：使用1.84版本的主要特征查找（data-test 和 class 特征）
                email_code_input = None
                try:
                    email_code_input = iframe.ele('x://input[@data-test="divVerificationCode"]', timeout=2)
                    if email_code_input:
                        print(f"在第 {i} 个iframe中找到data-test验证码输入框")
                except:
                    pass

                # 方法2：使用class特征查找
                if not email_code_input:
                    try:
                        email_code_input = iframe.ele('x://input[@class="MuiInputBase-input-1 MuiInput-input-2"]', timeout=2)
                        if email_code_input:
                            print(f"在第 {i} 个iframe中找到class验证码输入框")
                    except:
                        pass

                # 方法3：通用验证码输入框查找
                if not email_code_input:
                    try:
                        code_selectors = [
                            'x://input[contains(@name, "code")]',
                            'x://input[contains(@id, "code")]',
                            'x://input[contains(@placeholder, "code")]',
                            'x://input[contains(@placeholder, "verification")]',
                            'x://input[@type="text"][contains(@maxlength, "6")]'
                        ]

                        for selector in code_selectors:
                            try:
                                email_code_input = iframe.ele(selector, timeout=1)
                                if email_code_input:
                                    print(f"在第 {i} 个iframe中找到验证码输入框: {selector}")
                                    break
                            except:
                                continue
                    except:
                        pass

                if email_code_input:
                    print(f"在第 {i} 个iframe中找到邮件验证码输入框")
                    print(f"📧 开始自动获取邮箱 {email} 的最新验证码...")
                    print("🎯 注意：将确保获取最新验证码，避免读取旧验证码")

                    # 自动获取最新邮件验证码（确保不是旧的）
                    email_verification_code = get_email_verification_code(email, max_wait_time=40)

                    if email_verification_code:
                        print(f"✅ 成功获取邮件验证码: {email_verification_code}")

                        # 输入验证码 - 使用智能输入函数
                        try:
                            smart_type_text(tab, email_code_input, email_verification_code)
                            print(f"📧 邮件验证码填写成功: {email_verification_code}")
                            time.sleep(2)  # 等待输入完成

                            # 🔓 邮箱验证码输入完成后释放锁
                            thread_id = str(threading.get_ident())
                            if window_lock_manager.is_window_locked(thread_id, "verification_flow"):
                                window_lock_manager.release_window_lock(thread_id, "verification_flow")
                                print(f"🔓 邮箱验证码输入完成，释放验证流程锁")

                            # 查找并点击确认按钮 - 参考1.84版本的完整选择器
                            confirm_button = None
                            confirm_selectors = [
                                # 1.84版本的主要选择器
                                'x://button[contains(text(), "确认") or contains(text(), "验证") or contains(text(), "VERIFY") or contains(text(), "CONFIRM") or contains(@class, "MuiButton")]',
                                'x://span[@class="MuiButton-label-252"][contains(text(), "VERIFY")]',
                                'x://button[contains(text(), "VERIFY")]',
                                'x://button[contains(text(), "Verify")]',
                                'x://button[contains(text(), "Continue")]',
                                'x://button[contains(text(), "Next")]',
                                'x://button[contains(text(), "Submit")]',
                                'x://button[contains(text(), "Confirm")]',
                                # 通用按钮选择器
                                'x://button[@type="submit"]',
                                'x://input[@type="submit"]',
                                'x://*[@role="button" and contains(text(), "VERIFY")]',
                                'x://*[contains(@class, "MuiButton") and contains(text(), "VERIFY")]',
                                # 更宽泛的选择器
                                'x://button[contains(@class, "MuiButton")]',
                                'x://*[contains(@class, "MuiButtonBase")]'
                            ]

                            print(f"🔍 在第 {i} 个iframe中查找确认按钮...")
                            for selector_idx, selector in enumerate(confirm_selectors):
                                try:
                                    confirm_button = iframe.ele(selector, timeout=2)
                                    if confirm_button:
                                        button_text = confirm_button.text or ""
                                        button_class = confirm_button.attr('class') or ""
                                        print(f"✅ 找到确认按钮 (选择器{selector_idx+1}): {selector}")
                                        print(f"   按钮文本: '{button_text}'")
                                        print(f"   按钮类名: '{button_class}'")
                                        break
                                except Exception as e:
                                    continue

                            if confirm_button:
                                try:
                                    print("🔘 点击邮件验证码确认按钮...")
                                    confirm_button.click()
                                    print("✅ 确认按钮点击成功")
                                    print("⏳ 等待5秒，页面可能会稍微卡顿...")
                                    time.sleep(5)  # 等待页面响应，解决页面卡顿问题

                                    # 检查是否有close按钮（表示流程完成）
                                    print("🔍 检查是否出现close按钮...")
                                    close_selectors = [
                                        'x://button[contains(text(), "Close")]',
                                        'x://button[contains(text(), "close")]',
                                        'x://button[contains(text(), "CLOSE")]',
                                        'x://span[contains(@class, "MuiTouchRipple-root")]',
                                        'x://*[contains(@class, "close") or contains(@class, "Close")]'
                                    ]

                                    close_button_found = False
                                    for close_selector in close_selectors:
                                        try:
                                            close_btn = iframe.ele(close_selector, timeout=2)
                                            if close_btn:
                                                print(f"🎉 检测到close按钮: {close_selector}")
                                                print("✅ 邮件验证码完成后流程结束，跳过手机号流程")
                                                close_button_found = True
                                                break
                                        except:
                                            continue

                                    if close_button_found:
                                        print("🔥 检测到close按钮，现在点击close按钮...")

                                        # 点击close按钮
                                        try:
                                            for close_selector in close_selectors:
                                                try:
                                                    close_btn = iframe.ele(close_selector, timeout=2)
                                                    if close_btn:
                                                        print(f"🔘 点击close按钮: {close_selector}")
                                                        close_btn.click()
                                                        print("✅ close按钮点击成功")
                                                        time.sleep(2)  # 等待页面响应
                                                        break
                                                except:
                                                    continue
                                        except Exception as e:
                                            print(f"❌ 点击close按钮失败: {e}")

                                        # 输入完整姓名并提交
                                        print("📝 现在输入完整姓名并提交表单...")
                                        submit_result = handle_full_name_and_submit(tab, data)
                                        return submit_result  # 直接返回完整的结果
                                    else:
                                        print("ℹ️ 未发现close按钮，继续正常流程")
                                        return True

                                except Exception as click_error:
                                    print(f"❌ 点击确认按钮时出错: {click_error}")
                                    print("ℹ️ 但验证码已输入，继续流程")
                                    return True
                            else:
                                print("⚠️ 未找到确认按钮，但验证码已输入")
                                print("💡 可能验证码会自动验证，等待3秒...")
                                time.sleep(3)

                                # 再次检查是否有close按钮
                                print("🔍 检查是否自动出现close按钮...")
                                try:
                                    close_btn = iframe.ele('x://button[contains(text(), "Close")] | x://span[contains(@class, "MuiTouchRipple-root")]', timeout=3)
                                    if close_btn:
                                        print("🎉 自动检测到close按钮，邮件验证后流程完成")
                                        print("� 点击close按钮...")

                                        # 点击close按钮
                                        try:
                                            close_btn.click()
                                            print("✅ close按钮点击成功")
                                            time.sleep(2)
                                        except Exception as e:
                                            print(f"❌ 点击close按钮失败: {e}")

                                        # 输入完整姓名并提交
                                        print("📝 现在输入完整姓名并提交表单...")
                                        submit_result = handle_full_name_and_submit(tab, data)
                                        return submit_result  # 直接返回完整的结果
                                except:
                                    pass

                                print("ℹ️ 继续正常流程")
                                return True
                        except Exception as input_error:
                            print(f"输入验证码时出错: {input_error}")
                            return False
                    else:
                        print("⚠️ 40秒内未收到邮件验证码，任务失败")
                        print("📧 邮件验证码获取失败，可能原因：")
                        print("   1. 邮件服务器延迟")
                        print("   2. 垃圾邮件过滤")
                        print("   3. 邮件API服务异常")
                        return False

            except Exception as iframe_e:
                print(f"检查第 {i} 个iframe时出错: {iframe_e}")
                continue

        print("未在任何iframe中找到邮件验证码输入框")
        return True  # 可能不需要验证码

    except Exception as e:
        print(f"邮件验证码步骤失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def handle_phone_input_step(tab, data):
    """处理手机号输入步骤 - 使用API获取真实手机号码"""
    try:
        print("🔥 重要修正：不使用文本中的手机号码，而是通过API获取真实手机号码")
        print("📱 开始通过API获取真实手机号码...")

        # 通过API获取真实手机号码
        api_phone_number = get_phone_number()
        if not api_phone_number:
            print("❌ 无法通过API获取手机号码")
            return {'success': False, 'error': '无法获取API手机号码'}

        print(f"✅ 成功获取API手机号码: {api_phone_number}")
        print(f"⚠️ 忽略文本中的手机号码: {data.get('phone', '无')}")

        # 首先尝试在主页面查找
        print("🔍 在主页面查找手机号输入框...")
        main_page_result = try_phone_input_in_context(tab, api_phone_number, "主页面")
        if main_page_result['success']:
            # 获取手机验证码
            return handle_phone_verification(tab, api_phone_number, main_page_result)

        # 在所有iframe中查找手机号输入框
        iframes = tab.eles('t:iframe')
        print(f"🔍 在 {len(iframes)} 个iframe中查找手机号输入框...")

        for i, iframe in enumerate(iframes):
            try:
                print(f"🔍 检查第 {i+1} 个iframe...")

                # 检查iframe的src属性
                src = iframe.attr('src') or ''
                print(f"   iframe源: {src[:100]}...")

                result = try_phone_input_in_context(iframe, api_phone_number, f"第{i+1}个iframe")
                if result['success']:
                    print(f"✅ 在第 {i+1} 个iframe中成功输入手机号")
                    # 获取手机验证码
                    return handle_phone_verification(tab, api_phone_number, result)

            except Exception as e:
                print(f"⚠️ 检查第 {i+1} 个iframe时出错: {e}")
                continue

        print("❌ 在所有iframe中都未找到手机号输入框")
        return {'success': False, 'error': '未找到手机号输入框'}

    except Exception as e:
        print(f"❌ 手机号输入步骤失败: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}


def handle_full_name_and_submit(tab, data):
    """
    处理完整姓名输入和表单提交
    当检测到close按钮并点击后，需要输入完整姓名并提交表单
    """
    try:
        print("📝 开始处理完整姓名输入和表单提交...")

        # 等待页面响应
        time.sleep(3)

        # 构建完整姓名
        full_name = f"{data['first_name']} {data['last_name']}"
        print(f"📝 完整姓名: {full_name}")

        # 直接查找signature输入框（不需要遍历iframe）
        print("🎯 直接查找signature输入框...")
        signature_selectors = [
            '//*[@id="signature"]',
            'x://*[@id="signature"]',
            '#signature',
            'input#signature'
        ]

        full_name_input = None
        for selector in signature_selectors:
            try:
                print(f"🔍 尝试选择器: {selector}")
                if selector.startswith('//') or selector.startswith('x://'):
                    input_elem = tab.ele(selector, timeout=3)
                else:
                    input_elem = tab.ele(selector, timeout=3)

                if input_elem:
                    print(f"✅ 找到signature输入框: {selector}")
                    full_name_input = input_elem
                    break
            except Exception as e:
                print(f"⚠️ 选择器 {selector} 失败: {e}")
                continue

        if not full_name_input:
            print("❌ 未找到完整姓名输入框")
            return {'success': False, 'error': '未找到完整姓名输入框'}

        # 输入完整姓名
        try:
            print(f"📝 输入完整姓名: {full_name}")
            full_name_input.clear()
            full_name_input.input(full_name)
            print("✅ 完整姓名输入成功")
            time.sleep(2)
        except Exception as e:
            print(f"❌ 输入完整姓名失败: {e}")
            return {'success': False, 'error': f'输入完整姓名失败: {e}'}

        # 直接查找Submit按钮（不需要遍历iframe）
        print("🎯 直接查找Submit按钮...")
        submit_selectors = [
            'x://input[@id="submit-claim"]',
            '//*[@id="submit-claim"]',
            'x://button[contains(text(), "Submit")]',
            'x://input[@type="submit"]',
            'x://button[@type="submit"]',
            '#submit-claim',
            'input#submit-claim'
        ]

        submit_button = None
        for selector in submit_selectors:
            try:
                print(f"🔍 尝试Submit选择器: {selector}")
                if selector.startswith('//') or selector.startswith('x://'):
                    btn = tab.ele(selector, timeout=3)
                else:
                    btn = tab.ele(selector, timeout=3)

                if btn:
                    print(f"✅ 找到Submit按钮: {selector}")
                    submit_button = btn
                    break
            except Exception as e:
                print(f"⚠️ Submit选择器 {selector} 失败: {e}")
                continue

        if not submit_button:
            print("❌ 未找到Submit按钮")
            return {'success': False, 'error': '未找到Submit按钮'}

        # 点击Submit按钮
        try:
            print("🔘 点击Submit按钮...")
            submit_button.click()
            print("✅ Submit按钮点击成功")

            # 等待页面响应
            print("⏳ 等待页面响应...")
            time.sleep(8)  # 增加等待时间，确保页面完全加载

            # 打印返回的数据包和HTML代码
            print("📊 打印Submit提交后的响应数据...")
            print_submit_response_data(tab)

            # 检查页面响应和提取Claim ID
            print("🔍 检查页面响应并提取Claim ID...")
            claim_result = check_submission_result_and_extract_claim_id(tab)

            if claim_result['success']:
                print(f"🎉 表单提交成功！Claim ID: {claim_result.get('claim_id', 'N/A')}")
                return {
                    'success': True,
                    'claim_id': claim_result.get('claim_id'),
                    'message': '表单提交成功',
                    'skip_phone_flow': True,
                    'submit_triggered': True
                }
            else:
                print(f"⚠️ 表单提交状态不明确: {claim_result.get('error', '未知错误')}")
                return {
                    'success': True,  # 仍然认为成功，因为Submit按钮点击了
                    'claim_id': None,
                    'message': '表单已提交，但状态不明确',
                    'skip_phone_flow': True,
                    'submit_triggered': True
                }

        except Exception as e:
            print(f"❌ 点击Submit按钮失败: {e}")
            return {'success': False, 'error': f'点击Submit按钮失败: {e}'}

    except Exception as e:
        print(f"❌ 处理完整姓名和提交失败: {e}")
        return {'success': False, 'error': f'处理完整姓名和提交失败: {e}'}


def print_submit_response_data(tab):
    """
    打印Submit提交后的响应数据包和HTML代码
    """
    try:
        print("=" * 80)
        print("📊 Submit提交后的响应数据分析")
        print("=" * 80)

        # 1. 获取当前页面URL
        try:
            current_url = tab.url
            print(f"🌐 当前页面URL: {current_url}")
        except Exception as e:
            print(f"⚠️ 获取URL失败: {e}")

        # 2. 获取页面标题
        try:
            page_title = tab.title
            print(f"📄 页面标题: {page_title}")
        except Exception as e:
            print(f"⚠️ 获取页面标题失败: {e}")

        # 3. 获取页面HTML内容
        try:
            html_content = tab.html
            print(f"📝 HTML内容长度: {len(html_content)} 字符")

            # 保存完整HTML到文件
            timestamp = int(time.time())
            html_filename = f"submit_response_{timestamp}.html"
            with open(html_filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"💾 完整HTML已保存到: {html_filename}")

            # 打印HTML的关键部分
            print("\n📋 HTML关键内容片段:")
            print("-" * 50)

            # 查找成功标志
            success_keywords = ['success', 'submitted', 'thank you', 'confirmation', 'claim id', 'reference']
            found_keywords = []
            for keyword in success_keywords:
                if keyword.lower() in html_content.lower():
                    found_keywords.append(keyword)

            if found_keywords:
                print(f"✅ 发现成功标志: {', '.join(found_keywords)}")
            else:
                print("⚠️ 未发现明显的成功标志")

            # 打印HTML的前500字符
            print(f"\n📄 HTML前500字符:")
            print(html_content[:500])
            print("...")

            # 打印HTML的后500字符
            print(f"\n📄 HTML后500字符:")
            print("...")
            print(html_content[-500:])

        except Exception as e:
            print(f"❌ 获取HTML内容失败: {e}")

        # 4. 获取页面cookies
        try:
            # 通过JavaScript获取cookies
            cookies_js = """
                return document.cookie;
            """
            cookies = tab.run_js(cookies_js)
            print(f"\n🍪 页面Cookies:")
            if cookies:
                cookie_pairs = cookies.split(';')
                for cookie in cookie_pairs:
                    cookie = cookie.strip()
                    if cookie:
                        print(f"   {cookie}")
            else:
                print("   无cookies")

        except Exception as e:
            print(f"⚠️ 获取cookies失败: {e}")

        # 5. 获取localStorage和sessionStorage
        try:
            storage_js = """
                return {
                    localStorage: JSON.stringify(localStorage),
                    sessionStorage: JSON.stringify(sessionStorage)
                };
            """
            storage_data = tab.run_js(storage_js)
            print(f"\n💾 浏览器存储:")
            print(f"   localStorage: {storage_data.get('localStorage', 'N/A')}")
            print(f"   sessionStorage: {storage_data.get('sessionStorage', 'N/A')}")

        except Exception as e:
            print(f"⚠️ 获取浏览器存储失败: {e}")

        # 6. 检查页面中的表单
        try:
            forms_js = """
                var forms = document.forms;
                var formData = [];
                for (var i = 0; i < forms.length; i++) {
                    var form = forms[i];
                    formData.push({
                        action: form.action,
                        method: form.method,
                        id: form.id,
                        className: form.className,
                        elementCount: form.elements.length
                    });
                }
                return formData;
            """
            forms_data = tab.run_js(forms_js)
            print(f"\n📝 页面表单信息:")
            if forms_data and len(forms_data) > 0:
                for i, form in enumerate(forms_data):
                    print(f"   表单{i+1}: action={form.get('action')}, method={form.get('method')}, elements={form.get('elementCount')}")
            else:
                print("   无表单")

        except Exception as e:
            print(f"⚠️ 获取表单信息失败: {e}")

        # 7. 检查页面中的iframe
        try:
            iframes_js = """
                var iframes = document.querySelectorAll('iframe');
                var iframeData = [];
                for (var i = 0; i < iframes.length; i++) {
                    var iframe = iframes[i];
                    iframeData.push({
                        src: iframe.src,
                        id: iframe.id,
                        className: iframe.className,
                        width: iframe.width,
                        height: iframe.height
                    });
                }
                return iframeData;
            """
            iframes_data = tab.run_js(iframes_js)
            print(f"\n🖼️ 页面iframe信息:")
            if iframes_data and len(iframes_data) > 0:
                for i, iframe in enumerate(iframes_data):
                    print(f"   iframe{i+1}: src={iframe.get('src')}, id={iframe.get('id')}")
            else:
                print("   无iframe")

        except Exception as e:
            print(f"⚠️ 获取iframe信息失败: {e}")

        print("=" * 80)
        print("📊 响应数据分析完成")
        print("=" * 80)

    except Exception as e:
        print(f"❌ 打印响应数据失败: {e}")
        import traceback
        traceback.print_exc()


def check_submission_result_and_extract_claim_id(tab):
    """
    检查表单提交结果并提取Claim ID
    Submit按钮点击成功后，检查页面响应并提取Claim ID
    """
    try:
        print("🔍 检查表单提交结果...")

        # 增加等待时间，确保页面完全加载
        print("⏳ 等待页面完全加载...")
        time.sleep(5)  # 从3秒增加到5秒

        # 多次尝试获取页面内容，确保获取完整
        page_text = None
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                page_text = tab.html
                if page_text and len(page_text) > 100:  # 确保页面内容足够长
                    print(f"✅ 第{attempt+1}次尝试成功获取页面内容，长度: {len(page_text)}")
                    break
                else:
                    print(f"⚠️ 第{attempt+1}次尝试页面内容过短，等待后重试...")
                    time.sleep(2)
            except Exception as e:
                print(f"⚠️ 第{attempt+1}次获取页面内容失败: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    return {'success': False, 'error': f'获取页面内容失败: {e}'}
        
        if not page_text:
            print("⚠️ 无法获取页面内容")
            return {'success': False, 'error': '无法获取页面内容'}

        # 打印页面内容的前500个字符用于调试
        print(f"🔍 页面内容预览: {page_text[:500]}...")

        # 检查成功提交的标志
        success_indicators = [
            'thank you',
            'success',
            'submitted',
            'confirmation',
            'claim id',
            'reference number',
            'your claim has been',
            'successfully submitted',
            'claim number',
            'file claim'  # 添加这个作为可能的成功标志
        ]

        page_text_lower = page_text.lower()
        has_success_indicator = any(indicator in page_text_lower for indicator in success_indicators)

        if has_success_indicator:
            print("✅ 检测到成功提交标志")
        else:
            print("⚠️ 未检测到明确的成功提交标志")

        # 尝试提取Claim ID
        claim_id = extract_claim_id_from_page_content(page_text)

        if claim_id:
            print(f"🎯 成功提取Claim ID: {claim_id}")
            return {
                'success': True,
                'claim_id': claim_id,
                'has_success_indicator': has_success_indicator
            }
        elif has_success_indicator:
            print("✅ 检测到成功标志，但未找到具体Claim ID")
            # 如果检测到成功标志但没有Claim ID，返回一个标记
            return {
                'success': True,
                'claim_id': 'SUBMITTED_NO_ID',  # 改为更明确的标记
                'has_success_indicator': True
            }
        else:
            print("⚠️ 未检测到成功标志，也未找到Claim ID")
            return {
                'success': False,
                'error': '未检测到成功提交标志',
                'has_success_indicator': False
            }

    except Exception as e:
        print(f"❌ 检查提交结果失败: {e}")
        return {'success': False, 'error': f'检查提交结果失败: {e}'}


def extract_claim_id_from_page_content(page_content):
    """
    从页面内容中提取Claim ID
    """
    try:
        import re

        # 打印页面内容用于调试
        print(f"🔍 开始分析页面内容，长度: {len(page_content)}")
        print(f"🔍 页面内容前1000字符: {page_content[:1000]}...")

        # 多种Claim ID模式，按优先级排序
        patterns = [
            # 最具体的模式 - VNB格式
            r'Your\s*claim\s*has\s*been\s*submitted\.\s*Your\s*Claim\s*ID\s*is:\s*(VNB-[0-9]{8,12})',
            r'Claim\s*ID\s*is:\s*(VNB-[0-9]{8,12})',
            r'Your\s*Claim\s*ID\s*is:\s*(VNB-[0-9]{8,12})',
            
            # 通用Claim ID模式
            r'Claim\s*(?:ID|Number)?\s*:?\s*([A-Z0-9-]{6,})',
            r'Reference\s*(?:ID|Number)?\s*:?\s*([A-Z0-9-]{6,})',
            r'Confirmation\s*(?:ID|Number)?\s*:?\s*([A-Z0-9-]{6,})',
            r'Your\s*claim\s*(?:ID|number)?\s*is\s*:?\s*([A-Z0-9-]{6,})',
            
            # VNB格式模式
            r'(VNB-[0-9]{8,12})',
            r'([A-Z]{2,3}-\d{6,})',  # 格式如 VNB-123456
            
            # 其他可能的ID格式
            r'([A-Z0-9]{8,})',       # 8位以上的字母数字组合
            r'ID\s*:?\s*([A-Z0-9-]{6,})',
            r'Number\s*:?\s*([A-Z0-9-]{6,})'
        ]

        for i, pattern in enumerate(patterns):
            print(f"🔍 尝试模式 {i+1}: {pattern}")
            matches = re.findall(pattern, page_content, re.IGNORECASE)
            if matches:
                print(f"✅ 模式 {i+1} 找到 {len(matches)} 个匹配")
                for j, match in enumerate(matches):
                    # 过滤掉一些明显不是Claim ID的内容
                    if len(match) >= 6 and not match.lower() in ['submit', 'button', 'input', 'form', 'file claim']:
                        print(f"🎯 找到潜在Claim ID: {match} (模式 {i+1}, 匹配 {j+1})")
                        return match
                    else:
                        print(f"❌ 过滤掉无效匹配: {match}")

        print("⚠️ 未在页面内容中找到有效的Claim ID")
        return None

    except Exception as e:
        print(f"❌ 提取Claim ID失败: {e}")
        return None


def handle_phone_verification(tab, phone_number, input_result):
    """
    处理手机验证码获取和输入

    Args:
        tab: 浏览器标签页
        phone_number: API获取的手机号码
        input_result: 手机号输入的结果

    Returns:
        dict: 处理结果
    """
    try:
        print(f"📱 开始获取手机验证码，手机号: {phone_number}")

        # 等待验证码发送
        print("⏳ 等待验证码发送...")
        time.sleep(5)

        # 通过API获取验证码
        verification_result = get_phone_verification_code(phone_number, timeout=60)
        if not verification_result:
            print("❌ 无法获取手机验证码")
            # 释放手机号码
            release_phone_number(phone_number)
            return {'success': False, 'error': '无法获取手机验证码'}

        print(f"✅ 成功获取手机验证码: {verification_result}")

        # 查找验证码输入框
        verification_selectors = [
            'input[name*="verification"]',
            'input[name*="code"]',
            'input[name*="sms"]',
            'input[placeholder*="verification"]',
            'input[placeholder*="code"]',
            'input[placeholder*="验证码"]',
            'input[type="text"][maxlength="6"]',
            'input[type="text"][maxlength="4"]'
        ]

        verification_input = None
        for selector in verification_selectors:
            try:
                verification_input = tab.ele(selector, timeout=2)
                if verification_input:
                    print(f"✅ 找到验证码输入框: {selector}")
                    break
            except:
                continue

        if not verification_input:
            print("❌ 未找到验证码输入框")
            release_phone_number(phone_number)
            return {'success': False, 'error': '未找到验证码输入框'}

        # 输入验证码
        print(f"📝 输入验证码: {verification_result}")
        verification_input.clear()
        verification_input.input(verification_result)
        time.sleep(2)

        print("✅ 手机验证码输入完成")

        # 成功后释放手机号码
        release_phone_number(phone_number)

        # 🔓 手机验证码输入完成后释放锁
        thread_id = str(threading.get_ident())
        if window_lock_manager.is_window_locked(thread_id, "verification_flow"):
            window_lock_manager.release_window_lock(thread_id, "verification_flow")
            print(f"🔓 手机验证码输入完成，释放验证流程锁")

        return {
            'success': True,
            'phone_number': phone_number,
            'verification_code': verification_result
        }

    except Exception as e:
        print(f"❌ 手机验证码处理失败: {e}")
        # 出错时释放手机号码
        try:
            release_phone_number(phone_number)
        except:
            pass
        return {'success': False, 'error': f'手机验证码处理失败: {e}'}


def try_phone_input_in_context(context, phone_number, context_name):
    """在指定上下文（主页面或iframe）中尝试输入手机号"""
    try:
        print(f"🔍 在{context_name}中查找手机号输入框...")

        # 扩展的手机号输入框选择器
        phone_selectors = [
            'x://input[@type="tel"]',
            'x://input[contains(@name, "phone")]',
            'x://input[contains(@id, "phone")]',
            'x://input[contains(@placeholder, "phone")]',
            'x://input[contains(@placeholder, "Phone")]',
            'x://input[contains(@placeholder, "mobile")]',
            'x://input[contains(@placeholder, "Mobile")]',
            'x://input[contains(@aria-label, "phone")]',
            'x://input[contains(@aria-label, "Phone")]',
            'x://input[contains(@class, "phone")]',
            'x://input[@type="text" and contains(@placeholder, "number")]'
        ]

        phone_input = None
        used_selector = None

        for selector_idx, selector in enumerate(phone_selectors):
            try:
                phone_input = context.ele(selector, timeout=2)
                if phone_input:
                    used_selector = selector
                    print(f"✅ 在{context_name}中找到手机号输入框")
                    print(f"   选择器: {selector}")
                    print(f"   元素属性: id='{phone_input.attr('id')}', name='{phone_input.attr('name')}', placeholder='{phone_input.attr('placeholder')}'")
                    break
            except Exception as e:
                continue

        if not phone_input:
            print(f"⚠️ 在{context_name}中未找到手机号输入框")
            return {'success': False}

        # 强化的输入机制
        print(f"📝 开始输入手机号: {phone_number}")

        # 方法1：标准输入
        try:
            print("🔄 尝试方法1：标准清除和输入")
            phone_input.clear()
            time.sleep(0.5)
            phone_input.input(phone_number)
            time.sleep(1)

            # 验证输入是否成功
            current_value = phone_input.attr('value') or phone_input.text or ""
            print(f"   输入后的值: '{current_value}'")

            if phone_number in current_value or current_value in phone_number:
                print("✅ 方法1成功：标准输入")
                input_success = True
            else:
                print("⚠️ 方法1失败：值不匹配")
                input_success = False
        except Exception as e:
            print(f"⚠️ 方法1失败: {e}")
            input_success = False

        # 方法2：JavaScript输入（如果方法1失败）
        if not input_success:
            try:
                print("🔄 尝试方法2：JavaScript输入")
                # 获取元素的唯一标识
                element_id = phone_input.attr('id')
                element_name = phone_input.attr('name')
                element_class = phone_input.attr('class')

                if element_id:
                    js_code = f"""
                    var element = document.getElementById('{element_id}');
                    if (element) {{
                        element.value = '{phone_number}';
                        element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    }}
                    """
                elif element_name:
                    js_code = f"""
                    var element = document.querySelector('input[name="{element_name}"]');
                    if (element) {{
                        element.value = '{phone_number}';
                        element.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        element.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    }}
                    """
                else:
                    js_code = f"""
                    var elements = document.querySelectorAll('input[type="tel"], input[placeholder*="phone"], input[placeholder*="Phone"]');
                    for (var i = 0; i < elements.length; i++) {{
                        elements[i].value = '{phone_number}';
                        elements[i].dispatchEvent(new Event('input', {{ bubbles: true }}));
                        elements[i].dispatchEvent(new Event('change', {{ bubbles: true }}));
                    }}
                    """

                # 在正确的上下文中执行JavaScript
                if hasattr(context, 'run_js'):
                    context.run_js(js_code)
                else:
                    # 如果是iframe，需要在主tab中执行
                    context.run_js(js_code)

                time.sleep(1)

                # 再次验证
                current_value = phone_input.attr('value') or phone_input.text or ""
                print(f"   JavaScript输入后的值: '{current_value}'")

                if phone_number in current_value or current_value in phone_number:
                    print("✅ 方法2成功：JavaScript输入")
                    input_success = True
                else:
                    print("⚠️ 方法2失败：值不匹配")
                    input_success = False

            except Exception as e:
                print(f"⚠️ 方法2失败: {e}")
                input_success = False

        # 方法3：模拟键盘输入（如果前两种方法都失败）
        if not input_success:
            try:
                print("🔄 尝试方法3：模拟键盘输入")
                phone_input.click()  # 先点击获得焦点
                time.sleep(0.5)

                # 清除现有内容
                phone_input.key('ctrl+a')
                time.sleep(0.2)
                phone_input.key('delete')
                time.sleep(0.5)

                # 逐个输入字符
                for char in phone_number:
                    phone_input.key(char)
                    time.sleep(0.1)

                time.sleep(1)

                # 验证
                current_value = phone_input.attr('value') or phone_input.text or ""
                print(f"   键盘输入后的值: '{current_value}'")

                if phone_number in current_value or current_value in phone_number:
                    print("✅ 方法3成功：键盘输入")
                    input_success = True
                else:
                    print("⚠️ 方法3失败：值不匹配")
                    input_success = False

            except Exception as e:
                print(f"⚠️ 方法3失败: {e}")
                input_success = False

        if input_success:
            print(f"✅ 手机号输入成功: {phone_number}")

            # 查找并点击发送/继续按钮
            print("🔍 查找发送/继续按钮...")
            send_btn_selectors = [
                'x://button[contains(text(), "Send")]',
                'x://button[contains(text(), "SEND")]',
                'x://button[contains(text(), "Continue")]',
                'x://button[contains(text(), "CONTINUE")]',
                'x://button[contains(text(), "Next")]',
                'x://button[contains(text(), "NEXT")]',
                'x://button[contains(text(), "Submit")]',
                'x://button[contains(text(), "SUBMIT")]',
                'x://input[@type="submit"]',
                'x://button[@type="submit"]'
            ]

            button_clicked = False
            for btn_selector in send_btn_selectors:
                try:
                    btn = context.ele(btn_selector, timeout=2)
                    if btn:
                        btn_text = btn.text or ""
                        print(f"✅ 找到按钮: '{btn_text}' ({btn_selector})")
                        btn.click()
                        print(f"🔘 点击按钮: {btn_text}")
                        print("⏳ 等待5秒，页面可能会稍微卡顿...")
                        time.sleep(5)  # 等待页面响应，解决页面卡顿问题
                        button_clicked = True
                        break
                except Exception as e:
                    continue

            if not button_clicked:
                print("⚠️ 未找到发送按钮，但手机号已输入")

            return {'success': True, 'phone_value': phone_number}
        else:
            print(f"❌ 所有输入方法都失败了")
            return {'success': False}

    except Exception as e:
        print(f"❌ 在{context_name}中输入手机号时出错: {e}")
        return {'success': False}

def handle_phone_verification_step(tab, phone_value):
    """处理手机验证码步骤"""
    try:
        print("处理手机验证码...")

        # 等待验证码输入框出现
        time.sleep(5)

        sms_code_selectors = [
            'x://input[contains(@name, "sms")]',
            'x://input[contains(@name, "code")]',
            'x://input[contains(@id, "sms")]',
            'x://input[contains(@placeholder, "SMS")]'
        ]

        sms_code_input = None
        for selector in sms_code_selectors:
            try:
                sms_code_input = tab.ele(selector, timeout=2)
                if sms_code_input:
                    break
            except:
                continue

        if sms_code_input:
            print("找到短信验证码输入框，等待手动输入...")
            print("请在浏览器中手动输入短信验证码")

            # 等待用户输入验证码
            for _ in range(60):  # 等待最多60秒
                try:
                    if sms_code_input.value and len(sms_code_input.value.strip()) >= 4:
                        print("检测到短信验证码已输入")

                        # 查找并点击确认按钮
                        confirm_selectors = [
                            'x://button[contains(text(), "Verify")]',
                            'x://button[contains(text(), "Continue")]',
                            'x://button[contains(text(), "Complete")]'
                        ]

                        for selector in confirm_selectors:
                            try:
                                btn = tab.ele(selector, timeout=1)
                                if btn:
                                    print("点击验证码确认按钮...")
                                    btn.click()
                                    time.sleep(3)
                                    break
                            except:
                                continue

                        return True
                except:
                    pass
                time.sleep(1)

            print("等待短信验证码输入超时")
            return False
        else:
            print("未找到短信验证码输入框")
            return True  # 可能不需要验证码

    except Exception as e:
        print(f"短信验证码步骤失败: {e}")
        return False

def handle_completion_step(tab):
    """处理完成步骤"""
    try:
        print("处理完成步骤...")

        # 查找完成按钮
        completion_selectors = [
            'x://button[contains(text(), "Close")]',
            'x://button[contains(text(), "Done")]',
            'x://button[contains(text(), "Complete")]',
            'x://button[contains(text(), "Finish")]'
        ]

        for selector in completion_selectors:
            try:
                btn = tab.ele(selector, timeout=2)
                if btn:
                    print(f"找到完成按钮，点击...")
                    btn.click()
                    time.sleep(2)
                    return True
            except:
                continue

        print("未找到完成按钮，可能流程已自动完成")
        return True

    except Exception as e:
        print(f"完成步骤失败: {e}")
        return False

def fill_form(tab, data):
    """填写表单"""
    try:
        print(f"开始填写表单 - {data['first_name']} {data['last_name']}")

        # 导航到目标页面 - 增加超时时间和重试机制
        print("导航到目标页面...")
        target_url = "https://homeopathiceyedropsettlement.com/submit-claim"

        # 设置页面加载超时时间为60秒
        tab.set.timeouts(page_load=60)

        max_retries = 3
        for attempt in range(max_retries):
            try:
                print(f"第{attempt+1}次尝试访问页面: {target_url}")
                tab.get(target_url)

                # 等待页面加载完成
                print("等待页面加载完成...")
                time.sleep(5)

                # 检查页面是否正确加载
                current_url = tab.url
                page_title = tab.title
                print(f"当前页面URL: {current_url}")
                print(f"当前页面标题: {page_title}")

                # 验证页面是否正确加载
                if "homeopathiceyedropsettlement.com" in current_url.lower():
                    print("✅ 页面加载成功")
                    break
                else:
                    print(f"⚠️ 页面URL不正确，重试...")
                    if attempt < max_retries - 1:
                        time.sleep(3)
                        continue
                    else:
                        raise Exception(f"页面加载失败，最终URL: {current_url}")

            except Exception as e:
                print(f"❌ 第{attempt+1}次页面加载失败: {e}")
                if attempt < max_retries - 1:
                    print(f"等待5秒后重试...")
                    time.sleep(5)
                    continue
                else:
                    print(f"❌ 页面加载最终失败，已重试{max_retries}次")
                    raise Exception(f"页面加载失败: {e}")

        print("✅ 页面导航完成，开始填写表单...")

        # 填写基本信息 - 增加超时时间
        print("填写基本信息...")

        # 等待表单元素加载，增加超时时间到10秒
        print("等待表单元素加载...")
        first_name_input = tab.ele('x://input[@name="first_name"]', timeout=10)
        if first_name_input:
            first_name_input.input(data['first_name'])
            print(f"✅ 姓名填写完成: {data['first_name']}")
        else:
            raise Exception("找不到姓名输入框")

        last_name_input = tab.ele('x://input[@name="last_name"]', timeout=10)
        if last_name_input:
            last_name_input.input(data['last_name'])
            print(f"✅ 姓氏填写完成: {data['last_name']}")
        else:
            raise Exception("找不到姓氏输入框")

        street_address_input = tab.ele('x://input[@name="street_address_1"]', timeout=10)
        if street_address_input:
            street_address_input.input(data['street_address'])
            print(f"✅ 地址填写完成: {data['street_address']}")
        else:
            raise Exception("找不到地址输入框")

        city_input = tab.ele('x://input[@name="city"]', timeout=10)
        if city_input:
            city_input.input(data['city'])
            print(f"✅ 城市填写完成: {data['city']}")
        else:
            raise Exception("找不到城市输入框")

        # 选择州（使用简写代码）- 增加超时时间
        print(f"选择州: {data['state']}")
        state_select = tab.ele('x://select[@name="state"]', timeout=10)
        if state_select:
            # 使用JavaScript设置州
            tab.run_js(f"document.querySelector('select[name=\"state\"]').value = '{data['state']}';")
            tab.run_js("document.querySelector('select[name=\"state\"]').dispatchEvent(new Event('change'));")
            print(f"✅ 州已设置为: {data['state']}")
            time.sleep(1)  # 等待状态更新
        else:
            print("❌ 未找到州选择框")
            return False

        # 填写邮编 - 增加超时时间
        print("填写邮编...")
        zip_input = tab.ele('x://input[@name="zip_code"]', timeout=10)
        if zip_input:
            zip_input.input(data['zip_code'])
            print(f"✅ 邮编填写完成: {data['zip_code']}")
        else:
            raise Exception("找不到邮编输入框")

        # 选择国家为美国 - 增加超时时间
        print("选择国家...")
        country_select = tab.ele('x://select[@name="country"]', timeout=10)
        if country_select:
            # 使用JavaScript设置国家
            tab.run_js("document.querySelector('select[name=\"country\"]').value = 'USA';")
            tab.run_js("document.querySelector('select[name=\"country\"]').dispatchEvent(new Event('change'));")
            print("✅ 国家已设置为: USA")
            time.sleep(1)  # 等待状态更新
        else:
            print("❌ 未找到国家选择框")
            return False

        # 填写邮箱 - 增加超时时间
        print("填写邮箱...")
        email_input = tab.ele('x://input[@name="email_address"]', timeout=10)
        if email_input:
            email_input.input(data['email'])
            print(f"✅ 邮箱填写完成: {data['email']}")
        else:
            raise Exception("找不到邮箱输入框")

        confirm_email_input = tab.ele('x://input[@name="confirm_email_address"]', timeout=10)
        if confirm_email_input:
            confirm_email_input.input(data['email'])
            print(f"✅ 确认邮箱填写完成: {data['email']}")
        else:
            raise Exception("找不到确认邮箱输入框")

        # 填写电话 - 增加超时时间
        print("填写电话...")
        phone_input = tab.ele('x://input[@name="phone_number_1"]', timeout=10)
        if phone_input:
            phone_input.input(data['phone'])
            print(f"✅ 电话填写完成: {data['phone']}")
        else:
            raise Exception("找不到电话输入框")

        # 选择无购买凭证 - 增加超时时间
        print("选择购买信息...")
        radio_btn = tab.ele('x://input[@id="covered_product_purchase_info_2"]', timeout=10)
        if radio_btn:
            radio_btn.click()
            print("✅ 已选择无购买凭证")
            time.sleep(1)  # 等待选择生效
        else:
            print("❌ 未找到购买信息选项")
            return False

        # 填写产品数量 - 增加超时时间
        print("填写产品数量...")
        quantity_input = tab.ele('x://input[@name="number_of_covered_products_purchased"]', timeout=10)
        if quantity_input:
            quantity_input.input('1')
            print("✅ 产品数量填写完成: 1")
        else:
            raise Exception("找不到产品数量输入框")

        # 页面滚动到最下面
        print("页面滚动到最下面...")
        tab.run_js('window.scrollTo(0, document.body.scrollHeight);')
        time.sleep(2)

        # 处理 Payment Selection
        print("=== 开始处理 Payment Selection ===")
        payment_result = handle_payment_selection(tab, data)
        print(f"Payment Selection 处理结果: {payment_result}")

        if not payment_result:
            print("Payment Selection 处理失败")
            return False

        print("=== Payment Selection 处理完成 ===")

        # 填写签名
        print("填写签名...")
        full_name = f"{data['first_name']} {data['last_name']}"
        tab.ele('x://input[@name="signature"]').input(full_name)
        print("签名填写完成")

        print("表单填写完成")
        return True

    except Exception as e:
        print(f"填写表单失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def trigger_final_submit_button(tab):
    """触发最终的submit按钮 - 在验证流程完成后调用"""
    try:
        print("🔥 触发最终的submit按钮...")

        # 等待页面稳定
        time.sleep(2)

        # 查找submit按钮的多种选择器
        submit_selectors = [
            'x://input[@id="submit-claim"]',
            'x://button[@id="submit-claim"]',
            'x://*[@id="submit-claim"]',
            'x://input[@type="submit"]',
            'x://button[@type="submit"]',
            'x://input[contains(@value, "Submit")]',
            'x://button[contains(text(), "Submit")]',
            'x://button[contains(text(), "SUBMIT")]'
        ]

        submit_button = None
        used_selector = None

        for selector in submit_selectors:
            try:
                submit_button = tab.ele(selector, timeout=2)
                if submit_button:
                    used_selector = selector
                    print(f"✅ 找到submit按钮: {selector}")
                    break
            except:
                continue

        if submit_button:
            try:
                print(f"🔘 点击最终submit按钮: {used_selector}")
                submit_button.click()
                print("✅ 最终submit按钮点击成功")

                # 等待页面响应
                print("⏳ 等待页面响应...")
                time.sleep(8)  # 增加等待时间，确保页面完全加载

                # 打印返回的数据包和HTML代码
                print("📊 打印最终Submit提交后的响应数据...")
                print_submit_response_data(tab)

                # 检查页面响应和提取Claim ID
                print("🔍 检查页面响应并提取Claim ID...")
                claim_result = check_submission_result_and_extract_claim_id(tab)

                if claim_result['success']:
                    print(f"🎉 表单提交成功！Claim ID: {claim_result.get('claim_id', 'N/A')}")
                    return {
                        'success': True,
                        'selector': used_selector,
                        'claim_id': claim_result.get('claim_id'),
                        'submission_confirmed': True
                    }
                else:
                    print(f"⚠️ 表单提交状态不明确: {claim_result.get('error', '未知错误')}")
                    return {
                        'success': True,  # 仍然认为成功，因为Submit按钮点击了
                        'selector': used_selector,
                        'claim_id': None,
                        'submission_confirmed': False,
                        'warning': claim_result.get('error')
                    }
            except Exception as e:
                print(f"❌ 点击submit按钮失败: {e}")
                return {'success': False, 'error': f'点击失败: {e}'}
        else:
            print("⚠️ 未找到submit按钮")
            return {'success': False, 'error': '未找到submit按钮'}

    except Exception as e:
        print(f"❌ 触发submit按钮异常: {e}")
        return {'success': False, 'error': str(e)}


def submit_form(tab):
    """提交表单并提取真实的Claim ID"""
    try:
        print("尝试提交表单...")

        # 点击提交按钮
        submit_btn = tab.ele('x://input[@id="submit-claim"]')
        submit_btn.click()

        # 等待提交处理
        print("等待表单提交处理...")
        time.sleep(5)

        # 检查是否提交成功
        current_url = tab.url
        page_text = tab.html
        print(f"提交后页面URL: {current_url}")

        # 检查成功标志
        success_indicators = [
            'thank you', 'Thank You', 'submitted', 'Submitted',
            'confirmation', 'Confirmation', 'success', 'Success'
        ]

        submission_successful = False
        for indicator in success_indicators:
            if indicator.lower() in page_text.lower():
                print("✅ 表单提交成功!")
                submission_successful = True
                break

        if not submission_successful:
            # 检查是否还在同一页面（可能需要处理验证码）
            if 'submit-claim' in current_url:
                print("⚠️ 表单可能需要验证码验证")
                return {"success": False, "claim_id": None, "error": "需要验证码验证"}

            print("❌ 表单提交状态未知")
            return {"success": False, "claim_id": None, "error": "提交状态未知"}

        # 尝试提取真实的Claim ID
        print("🔍 尝试提取真实的Claim ID...")
        claim_id = extract_claim_id_from_page(tab)

        if claim_id:
            print(f"✅ 成功提取真实Claim ID: {claim_id}")
            return {"success": True, "claim_id": claim_id, "error": None}
        else:
            print("❌ 未找到真实Claim ID")
            print("💡 可能原因:")
            print("   1. submit按钮没有被正确触发")
            print("   2. 页面跳转失败")
            print("   3. Claim ID元素选择器需要更新")
            return {"success": False, "claim_id": None, "error": "未找到真实Claim ID"}

    except Exception as e:
        print(f"❌ 提交表单失败: {e}")
        return {"success": False, "claim_id": None, "error": str(e)}


def is_real_claim_id(claim_id):
    """
    判断是否为真实的Claim ID，排除生成的假ID

    Args:
        claim_id: 待验证的Claim ID

    Returns:
        bool: True表示是真实ID，False表示是假ID
    """
    if not claim_id:
        return False

    claim_id = claim_id.strip().upper()

    # 排除明显的假ID模式
    fake_patterns = [
        r'^CLAIM\-\d+\-\d+$',  # CLAIM-1-1753425407 格式
        r'^TEST\-',            # TEST- 开头
        r'^FAKE\-',            # FAKE- 开头
        r'^DEMO\-',            # DEMO- 开头
        r'^MOCK\-',            # MOCK- 开头
    ]

    import re
    for pattern in fake_patterns:
        if re.match(pattern, claim_id):
            print(f"❌ 检测到假ID模式: {claim_id} (匹配 {pattern})")
            return False

    # 真实ID的特征
    real_patterns = [
        r'^VNB\-[0-9]{8,12}$',     # VNB-60416920501 格式
        r'^[A-Z]{3,4}\-[0-9]{8,12}$',  # 其他真实格式，但排除CLAIM
    ]

    for pattern in real_patterns:
        if re.match(pattern, claim_id):
            print(f"✅ 检测到真实ID格式: {claim_id}")
            return True

    # 其他验证规则
    if len(claim_id) < 6:
        print(f"❌ ID太短: {claim_id}")
        return False

    if claim_id.isdigit() and len(claim_id) < 10:
        print(f"❌ 纯数字ID太短: {claim_id}")
        return False

    # 如果通过了所有检查，认为是真实的
    print(f"✅ 通过验证的ID: {claim_id}")
    return True


def extract_claim_id_from_page(tab):
    """从提交成功页面提取真实的Claim ID"""
    try:
        print("🔍 分析提交成功页面，查找真实Claim ID...")
        print("⚠️ 只接受真实的Claim ID，拒绝所有生成的假ID")

        # 获取页面内容
        page_text = tab.html
        page_title = tab.title
        current_url = tab.url

        print(f"页面标题: {page_title}")
        print(f"页面URL: {current_url}")

        # 真实Claim ID模式 - 只匹配真实格式，排除生成的假ID
        claim_id_patterns = [
            # VNB格式 (真实的索赔ID格式)
            r'(VNB\-[0-9]{8,12})',  # VNB-60416920501 格式

            # 其他可能的真实格式
            r'claim\s*(?:id|number|#)?\s*:?\s*(VNB\-[0-9]{8,12})',
            r'confirmation\s*(?:id|number|#)?\s*:?\s*(VNB\-[0-9]{8,12})',
            r'reference\s*(?:id|number|#)?\s*:?\s*(VNB\-[0-9]{8,12})',

            # 其他真实ID格式（排除CLAIM-开头的生成ID）
            r'([A-Z]{3,4}\-[0-9]{8,12})',  # 如 ABC-123456789，但排除CLAIM-

            # HTML属性中的真实ID
            r'data-claim-id="(VNB\-[0-9]{8,12})"',
            r'data-confirmation-id="(VNB\-[0-9]{8,12})"'
        ]

        # 在页面文本中搜索
        import re
        for pattern in claim_id_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                # 严格过滤，只接受真实的Claim ID
                for match in matches:
                    if is_real_claim_id(match):
                        print(f"✅ 通过模式 '{pattern}' 找到真实Claim ID: {match}")
                        return match.upper()

        # 尝试查找特定的HTML元素
        claim_id_selectors = [
            'x://*[contains(@class, "claim-id")]',
            'x://*[contains(@class, "confirmation-id")]',
            'x://*[contains(@class, "reference-id")]',
            'x://*[contains(@id, "claim")]',
            'x://*[contains(@id, "confirmation")]',
            'x://*[contains(text(), "Claim ID")]',
            'x://*[contains(text(), "Confirmation")]',
            'x://*[contains(text(), "Reference")]'
        ]

        for selector in claim_id_selectors:
            try:
                element = tab.ele(selector, timeout=2)
                if element:
                    element_text = element.text or ""
                    print(f"找到相关元素: {selector} -> '{element_text}'")

                    # 从元素文本中提取ID
                    for pattern in claim_id_patterns[:4]:  # 只用前几个通用模式
                        matches = re.findall(pattern, element_text, re.IGNORECASE)
                        if matches:
                            claim_id = matches[0].upper()
                            if is_real_claim_id(claim_id):
                                print(f"✅ 从元素文本提取到真实Claim ID: {claim_id}")
                                return claim_id
                            else:
                                print(f"❌ 从元素文本提取到假ID，已拒绝: {claim_id}")
            except:
                continue

        # 检查URL中是否包含ID
        url_patterns = [
            r'claim[_\-]?id=([A-Z0-9\-]+)',
            r'confirmation[_\-]?id=([A-Z0-9\-]+)',
            r'ref=([A-Z0-9\-]+)'
        ]

        for pattern in url_patterns:
            matches = re.findall(pattern, current_url, re.IGNORECASE)
            if matches:
                claim_id = matches[0].upper()
                if is_real_claim_id(claim_id):
                    print(f"✅ 从URL提取到真实Claim ID: {claim_id}")
                    return claim_id
                else:
                    print(f"❌ 从URL提取到假ID，已拒绝: {claim_id}")

        print("⚠️ 未找到真实的Claim ID")
        return None

    except Exception as e:
        print(f"❌ 提取Claim ID时出错: {e}")
        return None

def process_all_data(filename):
    """处理所有数据"""
    # 读取数据
    data_list = read_data_file(filename)
    if not data_list:
        print("没有有效数据可处理")
        return
    
    print(f"读取到 {len(data_list)} 条有效数据")
    
    # 连接浏览器
    browser, tab = connect_bit_browser()
    if not browser or not tab:
        print("无法连接到比特浏览器")
        return
    
    success_count = 0
    failed_count = 0
    
    try:
        for i, data in enumerate(data_list, 1):
            print(f"\n处理第 {i}/{len(data_list)} 条数据")
            print(f"姓名: {data['first_name']} {data['last_name']}")
            print(f"地址: {data['street_address']}, {data['city']}, {data['state']} {data['zip_code']}")
            
            # 填写表单
            if fill_form(tab, data):
                # 提交表单
                submit_result = submit_form(tab)
                if submit_result["success"]:
                    success_count += 1
                    claim_id = submit_result["claim_id"] or f"CLAIM-{i}-{int(time.time())}"
                    print(f"✓ 第 {i} 条数据处理成功，Claim ID: {claim_id}")
                else:
                    failed_count += 1
                    error_msg = submit_result.get("error", "未知错误")
                    print(f"✗ 第 {i} 条数据提交失败: {error_msg}")
            else:
                failed_count += 1
                print(f"✗ 第 {i} 条数据填写失败")
            
            # 如果不是最后一条，等待一段时间再处理下一条
            if i < len(data_list):
                print("等待 3 秒后处理下一条...")
                time.sleep(3)
    
    finally:
        print(f"\n处理完成!")
        print(f"成功: {success_count} 条")
        print(f"失败: {failed_count} 条")
        
        # 关闭浏览器
        try:
            browser.quit()
        except:
            pass

def run_full_process():
    """运行完整的自动化流程"""
    print("开始完整自动化填表程序...")

    # 读取数据
    data_list = read_data_file("list01.txt")
    if not data_list:
        print("没有有效数据可处理")
        return

    print(f"读取到 {len(data_list)} 条有效数据")

    # 连接浏览器
    browser, tab = connect_bit_browser()
    if not browser or not tab:
        print("无法连接到比特浏览器")
        return

    success_count = 0
    failed_count = 0

    try:
        for i, data in enumerate(data_list, 1):
            print(f"\n{'='*50}")
            print(f"处理第 {i}/{len(data_list)} 条数据")
            print(f"姓名: {data['first_name']} {data['last_name']}")
            print(f"地址: {data['street_address']}, {data['city']}, {data['state']} {data['zip_code']}")
            print(f"邮箱: {data['email']}")

            # 填写表单
            if fill_form(tab, data):
                # 尝试提交表单
                submit_result = submit_form(tab)
                if submit_result["success"]:
                    success_count += 1
                    claim_id = submit_result["claim_id"] or f"CLAIM-{i}-{int(time.time())}"
                    print(f"✓ 第 {i} 条数据处理成功，Claim ID: {claim_id}")
                else:
                    failed_count += 1
                    error_msg = submit_result.get("error", "未知错误")
                    print(f"✗ 第 {i} 条数据提交失败: {error_msg}")
            else:
                failed_count += 1
                print(f"✗ 第 {i} 条数据填写失败")

            # 如果不是最后一条，等待一段时间再处理下一条
            if i < len(data_list):
                print("等待 5 秒后处理下一条...")
                time.sleep(5)

    finally:
        print(f"\n{'='*50}")
        print(f"处理完成!")
        print(f"成功: {success_count} 条")
        print(f"失败: {failed_count} 条")
        print(f"总计: {len(data_list)} 条")

        # 关闭浏览器
        try:
            browser.quit()
            print("浏览器已关闭")
        except:
            print("关闭浏览器时出错")

def run_test():
    """运行测试模式（只处理第一条数据）"""
    print("开始测试模式...")

    # 读取数据
    data_list = read_data_file("list01.txt")
    if not data_list:
        print("没有有效数据可处理")
        return

    print(f"读取到 {len(data_list)} 条有效数据")
    print("第一条数据预览:")
    first_data = data_list[0]
    for key, value in first_data.items():
        print(f"  {key}: {value}")

    # 连接浏览器
    browser, tab = connect_bit_browser()
    if not browser or not tab:
        print("无法连接到比特浏览器")
        return

    try:
        # 只处理第一条数据
        data = data_list[0]
        print(f"\n处理测试数据: {data['first_name']} {data['last_name']}")

        if fill_form(tab, data):
            print("表单填写成功！")
            print("请手动检查表单内容，特别是 Payment Selection 部分")
            print("如果需要手动处理验证码，请在浏览器中完成")
            print("如果要运行完整流程，请修改代码调用 run_full_process()")

            # 给用户时间检查和处理
            input("按回车键继续（完成验证码输入后）...")
        else:
            print("表单填写失败")

    finally:
        print("测试完成")
        # 不自动关闭浏览器，让用户手动检查

def execute_single_claim(browser_id, parsed_data, thread_num):
    """
    执行单个索赔任务 - 供 main.py 调用

    Args:
        browser_id: 比特浏览器ID
        parsed_data: 解析后的数据字典
        thread_num: 线程编号

    Returns:
        dict: 执行结果 {"success": bool, "claim_id": str, "error": str}
    """
    try:
        print(f"线程 {thread_num} 开始执行索赔任务...")
        print(f"申请人: {parsed_data['first_name']} {parsed_data['last_name']}")

        # 连接比特浏览器
        browser, tab = connect_bit_browser_by_id(browser_id)
        if not browser or not tab:
            return {"success": False, "error": "无法连接到比特浏览器"}

        # 转换数据格式以匹配 tasks2.py 的期望格式
        # 兼容两种字段名格式：address1 (main.py) 和 street_address (tasks2.py)
        street_address = parsed_data.get('street_address') or parsed_data.get('address1', '')
        zip_code = parsed_data.get('zip_code') or parsed_data.get('zip', '')
        phone = parsed_data.get('phone') or parsed_data.get('phone3', '')

        data = {
            'first_name': parsed_data['first_name'],
            'last_name': parsed_data['last_name'],
            'street_address': street_address,
            'city': parsed_data['city'],
            'state': parsed_data['state'],
            'zip_code': zip_code,
            'phone': phone,
            'daytime_phone': phone,
            'evening_phone': parsed_data.get('phone2', ''),
            'email': parsed_data['email']
        }
        
        # 验证数据完整性
        print(f"数据验证:")
        print(f"  姓名: {data['first_name']} {data['last_name']}")
        print(f"  地址: {data['street_address']}")
        print(f"  城市: {data['city']}")
        print(f"  州: {data['state']}")
        print(f"  邮编: {data['zip_code']}")
        print(f"  电话: {data['phone']}")
        print(f"  邮箱: {data['email']}")

        # 填写表单
        if fill_form(tab, data):
            # 提交表单
            submit_result = submit_form(tab)
            if submit_result["success"]:
                # 只使用真实的Claim ID，不生成假的
                real_claim_id = submit_result["claim_id"]
                if real_claim_id:
                    print(f"线程 {thread_num} 索赔提交成功! 真实Claim ID: {real_claim_id}")
                    return {"success": True, "claim_id": real_claim_id}
                else:
                    print(f"❌ 线程 {thread_num} 表单提交成功但未获取到真实Claim ID")
                    print("💡 这意味着submit按钮可能没有被正确触发")
                    return {"success": False, "error": "未获取到真实Claim ID"}
            else:
                error_msg = submit_result.get("error", "表单提交失败")
                print(f"线程 {thread_num} 表单提交失败: {error_msg}")
                return {"success": False, "error": error_msg}
        else:
            return {"success": False, "error": "表单填写失败"}

    except Exception as e:
        print(f"线程 {thread_num} 执行索赔任务失败: {e}")
        return {"success": False, "error": str(e)}
    finally:
        # 不关闭浏览器，让它继续运行供下次使用
        pass


def connect_bit_browser_by_id(browser_id):
    """
    根据浏览器ID连接比特浏览器

    Args:
        browser_id: 浏览器ID

    Returns:
        tuple: (browser, tab) 或 (None, None)
    """
    try:
        # 使用BitBrowserManager打开浏览器
        from src.bit_browser import BitBrowserManager
        
        browser_manager = BitBrowserManager()
        open_result = browser_manager.open_browser(browser_id)
        
        if not open_result.get("success"):
            print(f"打开浏览器失败: {open_result.get('msg', '未知错误')}")
            return None, None

        open_data = open_result.get("data", {})
        driver = open_data.get("driver", "")
        http = open_data.get("http", "")

        if not driver or not http:
            print("获取浏览器连接信息失败")
            return None, None

        # 等待浏览器启动
        time.sleep(3)

        # 使用DrissionPage连接
        co = ChromiumOptions()
        co.set_browser_path(driver)
        co.set_address(http)

        browser = Chromium(co)
        tab = browser.latest_tab

        return browser, tab

    except Exception as e:
        print(f"连接比特浏览器失败: {e}")
        return None, None


def execute_single_claim_with_data_management(browser_id, thread_num):
    """
    执行单个索赔任务 - 完整的数据管理版本
    包含读一条删除一条、成功失败分类写入、多线程安全
    """
    try:
        print(f"线程 {thread_num} 开始获取数据...")

        # 从输入文件读取下一行数据（线程安全，读一条删除一条）
        raw_data = file_handler.read_next_line()
        if not raw_data:
            print(f"线程 {thread_num} 没有更多数据，退出")
            return {"success": False, "error": "没有更多数据"}

        print(f"线程 {thread_num} 获取到数据: {raw_data[:50]}...")

        # 解析数据
        try:
            parsed_data = parse_data_line(raw_data)
            print(f"线程 {thread_num} 申请人: {parsed_data['first_name']} {parsed_data['last_name']}")
            print(f"线程 {thread_num} 邮箱: {parsed_data['email']}")
        except Exception as e:
            error_msg = f"数据解析失败: {e}"
            print(f"线程 {thread_num} {error_msg}")
            # 写入失败记录
            file_handler.write_failed_record(raw_data, error_msg)
            return {"success": False, "error": error_msg}

        # 写入缓存记录
        task_id = f"TASK-{thread_num}-{int(time.time())}"
        file_handler.write_cache_record(task_id, {
            "thread_num": thread_num,
            "browser_id": browser_id,
            "raw_data": raw_data,
            "parsed_data": parsed_data,
            "status": "processing"
        })

        # 转换数据格式
        data = {
            'first_name': parsed_data['first_name'],
            'last_name': parsed_data['last_name'],
            'street_address': parsed_data['street_address'],
            'city': parsed_data['city'],
            'state': parsed_data['state'],
            'zip_code': parsed_data['zip_code'],
            'phone': parsed_data['phone'],
            'email': parsed_data['email']
        }

        # 执行索赔任务
        result = execute_single_claim(browser_id, data, thread_num)

        # 根据结果写入相应文件
        if result.get("success"):
            claim_id = result.get("claim_id", "未知")
            success_record = f"{raw_data}----{claim_id}"
            file_handler.write_success_record(success_record)
            print(f"线程 {thread_num} 任务完成: 成功 - Claim ID: {claim_id}")

            # 更新缓存状态
            file_handler.write_cache_record(task_id, {
                "thread_num": thread_num,
                "browser_id": browser_id,
                "raw_data": raw_data,
                "parsed_data": parsed_data,
                "status": "completed",
                "claim_id": claim_id
            })

            return {"success": True, "claim_id": claim_id}
        else:
            error_msg = result.get("error", "未知错误")
            file_handler.write_failed_record(raw_data, error_msg)
            print(f"线程 {thread_num} 任务完成: 失败 - {error_msg}")

            # 更新缓存状态
            file_handler.write_cache_record(task_id, {
                "thread_num": thread_num,
                "browser_id": browser_id,
                "raw_data": raw_data,
                "parsed_data": parsed_data,
                "status": "failed",
                "error": error_msg
            })

            return {"success": False, "error": error_msg}

    except Exception as e:
        error_msg = f"执行异常: {e}"
        print(f"线程 {thread_num} {error_msg}")
        import traceback
        traceback.print_exc()

        # 如果有原始数据，写入失败记录
        try:
            if 'raw_data' in locals():
                file_handler.write_failed_record(raw_data, error_msg)
        except:
            pass

        return {"success": False, "error": error_msg}


def parse_data_line(line):
    """解析数据行"""
    parts = line.strip().split('----')
    if len(parts) < 10:
        raise ValueError(f"数据格式不正确，期望至少10个字段，实际{len(parts)}个")

    # 邮编智能补齐：如果不足5位，前面补0
    zip_code = parts[5].strip()
    if zip_code.isdigit() and len(zip_code) < 5:
        zip_code = zip_code.zfill(5)
        print(f"🔧 邮编智能补齐: {parts[5].strip()} → {zip_code}")

    return {
        'first_name': parts[0].strip(),
        'last_name': parts[1].strip(),
        'street_address': parts[2].strip(),
        'city': parts[3].strip(),
        'state': parts[4].strip(),
        'zip_code': zip_code,
        'number1': parts[6].strip(),
        'number2': parts[7].strip(),
        'phone': parts[8].strip(),
        'email': parts[9].strip()
    }


if __name__ == "__main__":
    # 可以选择运行模式
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "data_management":
        # 数据管理模式：python tasks2.py data_management [线程数]
        print("🚀 启动数据管理模式")
        print("📋 功能特点:")
        print("   - 读一条删除一条（线程安全）")
        print("   - 成功数据写入 listsp_ok.txt")
        print("   - 失败数据写入 listsp_ng.txt")
        print("   - 缓存数据写入 listsp_data.txt")
        print("   - 多线程安全操作")

        thread_count = int(sys.argv[2]) if len(sys.argv) > 2 else 1
        print(f"   - 线程数: {thread_count}")

        # 检查文件状态
        file_status = file_handler.get_file_status()
        print(f"\n📊 文件状态:")
        for file_type, status in file_status.items():
            if status['exists']:
                print(f"   {file_type}: {status['line_count']} 行")
            else:
                print(f"   {file_type}: 文件不存在")

        print("\n⚠️ 数据管理模式需要配合 main.py 使用")
        print("请运行: python main.py")
    else:
        # 原来的测试模式
        run_test()
