#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import requests
from DrissionPage import ChromiumOptions, Chromium

def debug_turnstile():
    """调试Cloudflare Turnstile结构"""
    
    try:
        # 首先检查比特浏览器服务是否运行
        print("检查比特浏览器服务...")
        headers = {'Content-Type': 'application/json'}
        try:
            response = requests.post("http://127.0.0.1:54345/browser/list", headers=headers, timeout=5)
            print("比特浏览器服务连接正常")
        except Exception as e:
            print(f"比特浏览器服务连接失败: {e}")
            print("请确保比特浏览器已启动并开启API服务")
            return
        
        # 获取浏览器列表
        print("获取浏览器列表...")
        import json
        list_data = {"page": 0, "pageSize": 100}
        response = requests.post("http://127.0.0.1:54345/browser/list", 
                               data=json.dumps(list_data), 
                               headers=headers)
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text[:200]}")
        
        try:
            response_json = response.json()
            if 'data' in response_json:
                browsers = response_json['data']['list']
            else:
                browsers = response_json
            print(f"找到 {len(browsers)} 个浏览器")
        except Exception as e:
            print(f"解析JSON失败: {e}")
            print("完整响应内容:")
            print(response.text)
            return
        
        if not browsers:
            print("没有找到浏览器，请先创建浏览器")
            return
        
        # 使用第一个浏览器
        browser_id = browsers[0]['id']
        print(f"使用浏览器ID: {browser_id}")
        
        # 打开浏览器
        print("打开浏览器...")
        open_data = {"id": browser_id}
        open_response = requests.post(f"http://127.0.0.1:54345/browser/open", 
                                    data=json.dumps(open_data), 
                                    headers=headers)
        if open_response.status_code != 200:
            print(f"打开浏览器失败: {open_response.text}")
            return
        
        open_response_json = open_response.json()
        if 'data' not in open_response_json:
            print(f"打开浏览器失败: 响应中没有data字段")
            print(f"完整响应: {open_response_json}")
            return
        
        driver = open_response_json['data']['driver']
        http = open_response_json['data']['http']
        print(f"获取到调试端口信息:")
        print(f"  driver: {driver}")
        print(f"  http: {http}")
        
        # 等待浏览器启动
        time.sleep(5)
        
        # 使用DrissionPage连接
        co = ChromiumOptions()
        co.set_browser_path(driver)
        co.set_address(http)
        
        print("正在连接比特浏览器...")
        browser = Chromium(co)
        tab = browser.latest_tab
        
        print("连接成功！当前页面:", tab.title)
        
        # 如果当前页面不是目标页面，则导航到目标页面
        if "veritaclassaction" not in tab.url:
            print("正在导航到目标网站...")
            tab.get("https://www.veritaclassaction.com/claim-form")
            time.sleep(3)  # 减少等待时间
            print("页面标题:", tab.title)
        
        # 立即获取页面信息，避免连接断开
        print("\n=== 快速分析页面结构 ===")
        
        try:
            # 1. 快速获取页面HTML源码（部分）
            print("\n1. 页面HTML源码（前2000字符）:")
            html = tab.html
            print(html[:2000])
            
            # 2. 快速查找所有iframe
            print("\n2. 查找所有iframe:")
            iframes = tab.eles('x://iframe')
            print(f"找到 {len(iframes)} 个iframe")
            
            for i, iframe in enumerate(iframes):
                try:
                    src = iframe.attr('src')
                    print(f"  iframe {i+1}: src={src}")
                    if 'turnstile' in src or 'cloudflare' in src:
                        print(f"    *** 这是Cloudflare Turnstile iframe ***")
                except Exception as e:
                    print(f"  iframe {i+1}: 获取src失败 - {e}")
            
            # 3. 快速查找turnstile相关元素
            print("\n3. 查找turnstile相关元素:")
            turnstile_selectors = [
                'x://div[contains(@class, "turnstile")]',
                'x://div[contains(@id, "turnstile")]',
                'x://div[contains(@class, "cf-turnstile")]',
                'x://div[contains(@id, "cf-turnstile")]'
            ]
            
            for selector in turnstile_selectors:
                try:
                    elements = tab.eles(selector)
                    if elements:
                        print(f"  找到 {len(elements)} 个元素: {selector}")
                        for j, elem in enumerate(elements):
                            try:
                                print(f"    元素 {j+1}: class={elem.attr('class')}, id={elem.attr('id')}")
                            except Exception as e:
                                print(f"    元素 {j+1}: 获取属性失败 - {e}")
                except Exception as e:
                    print(f"  查找 {selector} 失败: {e}")
            
            # 4. 快速查找包含Success的元素
            print("\n4. 查找包含Success的元素:")
            try:
                success_elements = tab.eles('x://*[contains(text(), "Success")]')
                print(f"找到 {len(success_elements)} 个包含Success的元素")
                
                for i, elem in enumerate(success_elements[:5]):  # 只显示前5个
                    try:
                        tag = elem.tag
                        text = elem.text.strip() if elem.text else "无文本"
                        class_attr = elem.attr('class') or "无class"
                        print(f"  元素 {i+1}: 标签={tag}, 文本='{text}', class={class_attr}")
                    except Exception as e:
                        print(f"  元素 {i+1}: 获取属性失败 - {e}")
            except Exception as e:
                print(f"查找Success元素失败: {e}")
            
            # 5. 快速查找包含"Success!"的元素
            print("\n5. 查找包含'Success!'的元素:")
            try:
                success_exact_elements = tab.eles('x://*[contains(text(), "Success!")]')
                print(f"找到 {len(success_exact_elements)} 个包含'Success!'的元素")
                
                for i, elem in enumerate(success_exact_elements[:5]):  # 只显示前5个
                    try:
                        tag = elem.tag
                        text = elem.text.strip() if elem.text else "无文本"
                        class_attr = elem.attr('class') or "无class"
                        print(f"  元素 {i+1}: 标签={tag}, 文本='{text}', class={class_attr}")
                    except Exception as e:
                        print(f"  元素 {i+1}: 获取属性失败 - {e}")
            except Exception as e:
                print(f"查找'Success!'元素失败: {e}")
                
        except Exception as e:
            print(f"快速分析过程中出现错误: {e}")
        
        browser.quit()
        print("\n调试完成")
        
    except Exception as e:
        print(f"调试过程中出现错误: {e}")
        print("请确保比特浏览器已启动并开启API服务")

if __name__ == "__main__":
    debug_turnstile() 