#!/usr/bin/env python3
"""
测试DrissionPage上传功能
"""

import os
import time
import glob
from DrissionPage import ChromiumPage

def test_upload_with_drission():
    """使用DrissionPage测试文件上传"""
    try:
        print("开始测试DrissionPage文件上传...")
        
        # 连接到现有浏览器
        page = ChromiumPage(addr_or_opts='127.0.0.1:14198')
        print("已连接到浏览器")
        
        # 查找证据文件
        evidence_dir = "generated_images/thread_1"
        jpg_files = glob.glob(os.path.join(evidence_dir, "*.jpg"))
        
        if not jpg_files:
            print(f"错误: 在 {evidence_dir} 中找不到JPG文件")
            return False
        
        print(f"找到 {len(jpg_files)} 个证据文件")
        
        # 检查证据上传选择框
        evidence_checkbox = page.ele('x://*[@id="ProofOfPurchase"]')
        if not evidence_checkbox:
            print("错误: 找不到证据上传选择框")
            return False
        
        if not evidence_checkbox.states.is_checked:
            evidence_checkbox.click()
            print("已点击证据上传选择框")
            time.sleep(2)
        
        # 测试上传文件到不同的输入框
        successful_uploads = 0
        files_to_test = jpg_files[:4]  # 测试前4个文件
        
        for i, jpg_file in enumerate(files_to_test):
            file_id = f"file{i+1}"
            try:
                print(f"测试上传文件 {i+1} 到 {file_id}: {os.path.basename(jpg_file)}")
                
                # 获取文件输入框
                file_input = page.ele(f'x://*[@id="{file_id}"]')
                if not file_input:
                    print(f"  找不到 {file_id} 输入框")
                    continue
                
                # 滚动到输入框
                file_input.scroll.to_see()
                time.sleep(0.5)
                
                # 上传文件
                file_input.set_files(jpg_file)
                print(f"  成功上传到 {file_id}")
                successful_uploads += 1
                time.sleep(1)
                
                # 验证上传
                upload_check = page.run_js(f"""
                    const input = document.getElementById('{file_id}');
                    return input && input.files.length > 0 ? input.files[0].name : null;
                """)
                
                if upload_check:
                    print(f"  验证成功: {file_id} 包含文件 {upload_check}")
                else:
                    print(f"  验证失败: {file_id} 未包含文件")
                
            except Exception as e:
                print(f"  上传到 {file_id} 失败: {e}")
                continue
        
        print(f"\n测试完成: {successful_uploads}/{len(files_to_test)} 个文件成功上传")
        
        # 最终验证
        final_check = page.run_js("""
            const results = [];
            for (let i = 1; i <= 10; i++) {
                const input = document.getElementById(`file${i}`);
                if (input && input.files.length > 0) {
                    results.push({
                        id: `file${i}`,
                        fileName: input.files[0].name
                    });
                }
            }
            return results;
        """)
        
        if final_check:
            print(f"最终验证: {len(final_check)} 个输入框包含文件")
            for file_info in final_check:
                print(f"  {file_info['id']}: {file_info['fileName']}")
        
        return successful_uploads > 0
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_upload_with_drission()
