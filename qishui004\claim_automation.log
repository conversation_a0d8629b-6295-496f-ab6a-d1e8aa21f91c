2025-07-25 19:46:51,116 - __main__ - INFO - 初始化索赔自动化系统...
2025-07-25 19:46:51,117 - src.bit_browser - INFO - 发送API请求: http://127.0.0.1:54345/browser/list, 数据: {'page': 0, 'pageSize': 100}
2025-07-25 19:46:53,009 - src.bit_browser - INFO - API响应: {'success': True, 'data': {'page': 0, 'pageSize': 100, 'totalNum': 5, 'list': [{'id': '66db5799ca1243af8cd6e7d3adafafe2', 'seq': 70, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'Qq112233', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 19:42:00', 'closeTime': '2025-07-25 19:42:32', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-25 10:54:58', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-25 16:59:25', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': True, 'clearCookiesBeforeLaunch': False, 'clearHistoriesBeforeLaunch': False, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'ce586031e017443aaa60d85621cae2e4', 'seq': 55, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'Qq112233', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 10:53:55', 'closeTime': '2025-07-25 10:54:42', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-24 19:23:10', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-25 10:07:07', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '4dbc5ed032df4443a12d434e4b4d5bba', 'seq': 13, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'd7f83ae959de0ae5680b2321c29bc7fff780f27d35ce3d1ef06fb8f83feabcc2@456bitbrowser', 'lastIp': '***************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 09:14:17', 'closeTime': '2025-07-25 09:18:53', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 20:44:39', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'e992c27b285546c19e520e4696d2e4af', 'seq': 12, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'a0ef8a0164e034561f94652db2aad39e152346afca77619ef1180c366e243e28@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 09:14:23', 'closeTime': '2025-07-25 09:18:45', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 17:18:01', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'd525608d0ff14f7c9e5b06099b0c0b36', 'seq': 11, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': '22161605234fcc73ddc639310d099dfd7d0b08e391db804a60fda1ce230e3b6f@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 09:14:30', 'closeTime': '2025-07-25 09:18:45', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 14:23:40', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': True, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': None, 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}]}}
2025-07-25 19:46:53,014 - __main__ - INFO - 获取到 5 个现有浏览器
2025-07-25 19:46:53,014 - __main__ - INFO -   浏览器 1: _1_1_1_1 (ID: 66db5799ca1243af8cd6e7d3adafafe2)
2025-07-25 19:46:53,014 - __main__ - INFO -   浏览器 2: _1_1_1 (ID: ce586031e017443aaa60d85621cae2e4)
2025-07-25 19:46:53,014 - __main__ - INFO -   浏览器 3: _1_1 (ID: 4dbc5ed032df4443a12d434e4b4d5bba)
2025-07-25 19:46:53,014 - __main__ - INFO -   浏览器 4: _1 (ID: e992c27b285546c19e520e4696d2e4af)
2025-07-25 19:46:53,015 - __main__ - INFO -   浏览器 5:  (ID: d525608d0ff14f7c9e5b06099b0c0b36)
2025-07-25 19:46:53,015 - __main__ - INFO - 系统初始化完成
2025-07-25 19:46:53,017 - __main__ - INFO - 开始索赔自动化流程...
2025-07-25 19:46:56,058 - __main__ - INFO - 待处理数据: 8 条
2025-07-25 19:46:56,058 - __main__ - INFO - 使用 1 个线程进行处理
2025-07-25 19:46:56,058 - __main__ - INFO - 邮编将自动补零到5位
2025-07-25 19:46:59,713 - __main__ - INFO - 🔥 使用真正的读一条删除一条模式
2025-07-25 19:46:59,713 - __main__ - INFO - 📋 特点: 每个线程直接从文件读取数据，读取后立即删除该行
2025-07-25 19:46:59,714 - __main__ - INFO - 启动读一条删除一条线程 1，使用浏览器ID: 66db5799ca1243af8cd6e7d3adafafe2
2025-07-25 19:46:59,716 - src.email_code_manager - INFO - 📧 邮箱验证码管理器初始化完成，API地址: https://mail-api-worker.anyubs.workers.dev
2025-07-25 19:46:59,767 - __main__ - INFO - 线程 1 (ID: 39644) 开始工作，固定使用浏览器ID: 66db5799ca1243af8cd6e7d3adafafe2
2025-07-25 19:46:59,767 - __main__ - INFO - 线程 1 使用真正的读一条删除一条模式
2025-07-25 19:46:59,769 - src.file_handler - INFO - 读取并删除行: Simon----Hong----632 Massachusetts Ave Apt 602----...
2025-07-25 19:46:59,770 - src.file_handler - INFO - 写入缓存记录: TASK-1-1753444019
2025-07-25 19:46:59,770 - src.bit_browser - INFO - 发送API请求: http://127.0.0.1:54345/browser/open, 数据: {'id': '66db5799ca1243af8cd6e7d3adafafe2'}
2025-07-25 19:47:01,715 - __main__ - INFO - 等待所有线程完成...
2025-07-25 19:47:08,612 - src.bit_browser - INFO - API响应: {'success': True, 'data': {'ws': 'ws://127.0.0.1:11201/devtools/browser/06d10827-7fa2-4fad-bebf-393347bdabf8', 'http': '127.0.0.1:11201', 'coreVersion': '134', 'driver': 'C:\\Users\\<USER>\\AppData\\Roaming\\BitBrowser\\chromedriver\\134\\chromedriver.exe', 'seq': 70, 'name': '_1_1_1_1', 'remark': '', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'pid': 36780}}
2025-07-25 19:52:13,587 - __main__ - INFO - 初始化索赔自动化系统...
2025-07-25 19:52:13,587 - src.bit_browser - INFO - 发送API请求: http://127.0.0.1:54345/browser/list, 数据: {'page': 0, 'pageSize': 100}
2025-07-25 19:52:15,510 - src.bit_browser - INFO - API响应: {'success': True, 'data': {'page': 0, 'pageSize': 100, 'totalNum': 5, 'list': [{'id': '66db5799ca1243af8cd6e7d3adafafe2', 'seq': 70, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'Qq112233', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 19:47:09', 'closeTime': '2025-07-25 19:47:24', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-25 10:54:58', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-25 16:59:25', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': True, 'clearCookiesBeforeLaunch': False, 'clearHistoriesBeforeLaunch': False, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'ce586031e017443aaa60d85621cae2e4', 'seq': 55, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'Qq112233', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 10:53:55', 'closeTime': '2025-07-25 10:54:42', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-24 19:23:10', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-25 10:07:07', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '4dbc5ed032df4443a12d434e4b4d5bba', 'seq': 13, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'd7f83ae959de0ae5680b2321c29bc7fff780f27d35ce3d1ef06fb8f83feabcc2@456bitbrowser', 'lastIp': '***************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 09:14:17', 'closeTime': '2025-07-25 09:18:53', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 20:44:39', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'e992c27b285546c19e520e4696d2e4af', 'seq': 12, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'a0ef8a0164e034561f94652db2aad39e152346afca77619ef1180c366e243e28@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 09:14:23', 'closeTime': '2025-07-25 09:18:45', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 17:18:01', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'd525608d0ff14f7c9e5b06099b0c0b36', 'seq': 11, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': '22161605234fcc73ddc639310d099dfd7d0b08e391db804a60fda1ce230e3b6f@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 09:14:30', 'closeTime': '2025-07-25 09:18:45', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 14:23:40', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': True, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': None, 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}]}}
2025-07-25 19:52:15,515 - __main__ - INFO - 获取到 5 个现有浏览器
2025-07-25 19:52:15,515 - __main__ - INFO -   浏览器 1: _1_1_1_1 (ID: 66db5799ca1243af8cd6e7d3adafafe2)
2025-07-25 19:52:15,515 - __main__ - INFO -   浏览器 2: _1_1_1 (ID: ce586031e017443aaa60d85621cae2e4)
2025-07-25 19:52:15,515 - __main__ - INFO -   浏览器 3: _1_1 (ID: 4dbc5ed032df4443a12d434e4b4d5bba)
2025-07-25 19:52:15,515 - __main__ - INFO -   浏览器 4: _1 (ID: e992c27b285546c19e520e4696d2e4af)
2025-07-25 19:52:15,516 - __main__ - INFO -   浏览器 5:  (ID: d525608d0ff14f7c9e5b06099b0c0b36)
2025-07-25 19:52:15,516 - __main__ - INFO - 系统初始化完成
2025-07-25 19:52:15,518 - __main__ - INFO - 开始索赔自动化流程...
2025-07-25 19:52:16,834 - __main__ - INFO - 待处理数据: 7 条
2025-07-25 19:52:16,834 - __main__ - INFO - 使用 1 个线程进行处理
2025-07-25 19:52:16,835 - __main__ - INFO - 邮编将自动补零到5位
2025-07-25 19:52:17,838 - __main__ - INFO - 🔥 使用真正的读一条删除一条模式
2025-07-25 19:52:17,838 - __main__ - INFO - 📋 特点: 每个线程直接从文件读取数据，读取后立即删除该行
2025-07-25 19:52:17,839 - __main__ - INFO - 启动读一条删除一条线程 1，使用浏览器ID: 66db5799ca1243af8cd6e7d3adafafe2
2025-07-25 19:52:17,840 - __main__ - INFO - 线程 1 (ID: 3996) 开始工作，固定使用浏览器ID: 66db5799ca1243af8cd6e7d3adafafe2
2025-07-25 19:52:17,841 - __main__ - INFO - 线程 1 使用真正的读一条删除一条模式
2025-07-25 19:52:17,843 - src.file_handler - INFO - 读取并删除行: Christine----McIntosh----665 PRIMROSE ST----HAVERH...
2025-07-25 19:52:19,840 - __main__ - INFO - 等待所有线程完成...
2025-07-25 21:03:32,485 - __main__ - INFO - 初始化索赔自动化系统...
2025-07-25 21:03:32,486 - src.bit_browser - INFO - 发送API请求: http://127.0.0.1:54345/browser/list, 数据: {'page': 0, 'pageSize': 100}
2025-07-25 21:03:34,617 - src.bit_browser - INFO - API响应: {'success': True, 'data': {'page': 0, 'pageSize': 100, 'totalNum': 5, 'list': [{'id': '66db5799ca1243af8cd6e7d3adafafe2', 'seq': 70, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'Qq112233', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 1, 'operUserName': 'anyubswork', 'operTime': '2025-07-25 20:34:11', 'closeTime': '2025-07-25 20:33:31', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-25 10:54:58', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-25 16:59:25', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': True, 'clearCookiesBeforeLaunch': False, 'clearHistoriesBeforeLaunch': False, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'ce586031e017443aaa60d85621cae2e4', 'seq': 55, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'Qq112233', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 10:53:55', 'closeTime': '2025-07-25 10:54:42', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-24 19:23:10', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-25 10:07:07', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '4dbc5ed032df4443a12d434e4b4d5bba', 'seq': 13, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'd7f83ae959de0ae5680b2321c29bc7fff780f27d35ce3d1ef06fb8f83feabcc2@456bitbrowser', 'lastIp': '***************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 09:14:17', 'closeTime': '2025-07-25 09:18:53', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 20:44:39', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'e992c27b285546c19e520e4696d2e4af', 'seq': 12, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'a0ef8a0164e034561f94652db2aad39e152346afca77619ef1180c366e243e28@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 09:14:23', 'closeTime': '2025-07-25 09:18:45', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 17:18:01', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'd525608d0ff14f7c9e5b06099b0c0b36', 'seq': 11, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': '22161605234fcc73ddc639310d099dfd7d0b08e391db804a60fda1ce230e3b6f@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 09:14:30', 'closeTime': '2025-07-25 09:18:45', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 14:23:40', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': True, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': None, 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}]}}
2025-07-25 21:03:34,622 - __main__ - INFO - 获取到 5 个现有浏览器
2025-07-25 21:03:34,622 - __main__ - INFO -   浏览器 1: _1_1_1_1 (ID: 66db5799ca1243af8cd6e7d3adafafe2)
2025-07-25 21:03:34,623 - __main__ - INFO -   浏览器 2: _1_1_1 (ID: ce586031e017443aaa60d85621cae2e4)
2025-07-25 21:03:34,623 - __main__ - INFO -   浏览器 3: _1_1 (ID: 4dbc5ed032df4443a12d434e4b4d5bba)
2025-07-25 21:03:34,623 - __main__ - INFO -   浏览器 4: _1 (ID: e992c27b285546c19e520e4696d2e4af)
2025-07-25 21:03:34,623 - __main__ - INFO -   浏览器 5:  (ID: d525608d0ff14f7c9e5b06099b0c0b36)
2025-07-25 21:03:34,624 - __main__ - INFO - 系统初始化完成
2025-07-25 21:03:34,626 - __main__ - INFO - 开始索赔自动化流程...
2025-07-25 21:03:35,798 - __main__ - INFO - 待处理数据: 6 条
2025-07-25 21:03:35,798 - __main__ - INFO - 使用 1 个线程进行处理
2025-07-25 21:03:35,799 - __main__ - INFO - 邮编将自动补零到5位
2025-07-25 21:03:37,156 - __main__ - INFO - 🔥 使用真正的读一条删除一条模式
2025-07-25 21:03:37,157 - __main__ - INFO - 📋 特点: 每个线程直接从文件读取数据，读取后立即删除该行
2025-07-25 21:03:37,157 - __main__ - INFO - 启动读一条删除一条线程 1，使用浏览器ID: 66db5799ca1243af8cd6e7d3adafafe2
2025-07-25 21:03:37,179 - __main__ - INFO - 线程 1 (ID: 35248) 开始工作，固定使用浏览器ID: 66db5799ca1243af8cd6e7d3adafafe2
2025-07-25 21:03:37,180 - __main__ - INFO - 线程 1 使用真正的读一条删除一条模式
2025-07-25 21:03:37,182 - src.file_handler - INFO - 读取并删除行: Seth----Vachon----67 Germain ave----QUINCY----MA--...
2025-07-25 21:03:39,159 - __main__ - INFO - 等待所有线程完成...
2025-07-25 21:04:39,320 - src.file_handler - INFO - 写入失败记录: Seth----Vachon----67 Germain ave----QUINCY----MA--...
2025-07-25 21:04:39,321 - __main__ - ERROR - 线程 1 任务失败: 填表任务失败
2025-07-25 21:04:39,321 - __main__ - INFO - 线程 1 等待 50.6 秒...
2025-07-25 21:19:14,835 - __main__ - INFO - 初始化索赔自动化系统...
2025-07-25 21:19:14,836 - src.bit_browser - INFO - 发送API请求: http://127.0.0.1:54345/browser/list, 数据: {'page': 0, 'pageSize': 100}
2025-07-25 21:19:17,782 - src.bit_browser - INFO - API响应: {'success': True, 'data': {'page': 0, 'pageSize': 100, 'totalNum': 5, 'list': [{'id': '66db5799ca1243af8cd6e7d3adafafe2', 'seq': 70, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'Qq112233', 'lastIp': '***************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 1, 'operUserName': 'anyubswork', 'operTime': '2025-07-25 21:06:43', 'closeTime': '2025-07-25 21:04:40', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-25 10:54:58', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-25 16:59:25', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': True, 'clearCookiesBeforeLaunch': False, 'clearHistoriesBeforeLaunch': False, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'ce586031e017443aaa60d85621cae2e4', 'seq': 55, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'Qq112233', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 10:53:55', 'closeTime': '2025-07-25 10:54:42', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-24 19:23:10', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-25 10:07:07', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '4dbc5ed032df4443a12d434e4b4d5bba', 'seq': 13, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'd7f83ae959de0ae5680b2321c29bc7fff780f27d35ce3d1ef06fb8f83feabcc2@456bitbrowser', 'lastIp': '***************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 09:14:17', 'closeTime': '2025-07-25 09:18:53', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 20:44:39', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'e992c27b285546c19e520e4696d2e4af', 'seq': 12, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'a0ef8a0164e034561f94652db2aad39e152346afca77619ef1180c366e243e28@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 09:14:23', 'closeTime': '2025-07-25 09:18:45', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 17:18:01', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'd525608d0ff14f7c9e5b06099b0c0b36', 'seq': 11, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': '22161605234fcc73ddc639310d099dfd7d0b08e391db804a60fda1ce230e3b6f@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 09:14:30', 'closeTime': '2025-07-25 09:18:45', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 14:23:40', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': True, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': None, 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}]}}
2025-07-25 21:19:17,787 - __main__ - INFO - 获取到 5 个现有浏览器
2025-07-25 21:19:17,788 - __main__ - INFO -   浏览器 1: _1_1_1_1 (ID: 66db5799ca1243af8cd6e7d3adafafe2)
2025-07-25 21:19:17,788 - __main__ - INFO -   浏览器 2: _1_1_1 (ID: ce586031e017443aaa60d85621cae2e4)
2025-07-25 21:19:17,788 - __main__ - INFO -   浏览器 3: _1_1 (ID: 4dbc5ed032df4443a12d434e4b4d5bba)
2025-07-25 21:19:17,788 - __main__ - INFO -   浏览器 4: _1 (ID: e992c27b285546c19e520e4696d2e4af)
2025-07-25 21:19:17,788 - __main__ - INFO -   浏览器 5:  (ID: d525608d0ff14f7c9e5b06099b0c0b36)
2025-07-25 21:19:17,789 - __main__ - INFO - 系统初始化完成
2025-07-25 21:19:17,791 - __main__ - INFO - 开始索赔自动化流程...
2025-07-25 21:19:22,166 - __main__ - INFO - 待处理数据: 5 条
2025-07-25 21:19:22,167 - __main__ - INFO - 使用 1 个线程进行处理
2025-07-25 21:19:22,167 - __main__ - INFO - 邮编将自动补零到5位
2025-07-25 21:19:22,828 - __main__ - INFO - 🔥 使用真正的读一条删除一条模式
2025-07-25 21:19:22,828 - __main__ - INFO - 📋 特点: 每个线程直接从文件读取数据，读取后立即删除该行
2025-07-25 21:19:22,829 - __main__ - INFO - 启动读一条删除一条线程 1，使用浏览器ID: 66db5799ca1243af8cd6e7d3adafafe2
2025-07-25 21:19:22,852 - __main__ - INFO - 线程 1 (ID: 6400) 开始工作，固定使用浏览器ID: 66db5799ca1243af8cd6e7d3adafafe2
2025-07-25 21:19:22,853 - __main__ - INFO - 线程 1 使用真正的读一条删除一条模式
2025-07-25 21:19:22,855 - src.file_handler - INFO - 读取并删除行: melisa----fletcher----7 Donna Ave----Gardner----MA...
2025-07-25 21:19:24,830 - __main__ - INFO - 等待所有线程完成...
2025-07-25 21:20:12,928 - src.file_handler - INFO - 写入失败记录: melisa----fletcher----7 Donna Ave----Gardner----MA...
2025-07-25 21:20:12,929 - __main__ - ERROR - 线程 1 任务失败: 填表任务失败
2025-07-25 21:20:12,929 - __main__ - INFO - 线程 1 等待 51.5 秒...
2025-07-29 10:47:04,692 - __main__ - INFO - 初始化索赔自动化系统...
2025-07-29 10:47:04,692 - src.bit_browser - INFO - 发送API请求: http://127.0.0.1:54345/browser/list, 数据: {'page': 0, 'pageSize': 100}
2025-07-29 10:47:06,859 - src.bit_browser - INFO - API响应: {'success': True, 'data': {'page': 0, 'pageSize': 100, 'totalNum': 6, 'list': [{'id': '677f2396fb1e47c5a5a95d1dca7e1483', 'seq': 72, 'code': '20250729103719519', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': '36473ef5a69d0627357f6f278e93fd5ee338e5b0cf8bf765449909789cebcf0b@456bitbrowser', 'lastIp': '*************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-29 10:39:25', 'closeTime': '2025-07-29 10:45:35', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-29 10:37:20', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': True, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': True, 'clearCookiesBeforeLaunch': False, 'clearHistoriesBeforeLaunch': False, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': None, 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '66db5799ca1243af8cd6e7d3adafafe2', 'seq': 70, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'Qq112233', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-29 08:29:14', 'closeTime': '2025-07-29 10:32:35', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-25 10:54:58', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-25 16:59:25', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': True, 'clearCookiesBeforeLaunch': False, 'clearHistoriesBeforeLaunch': False, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'ce586031e017443aaa60d85621cae2e4', 'seq': 55, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'Qq112233', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 10:53:55', 'closeTime': '2025-07-25 10:54:42', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-24 19:23:10', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-25 10:07:07', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '4dbc5ed032df4443a12d434e4b4d5bba', 'seq': 13, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'd7f83ae959de0ae5680b2321c29bc7fff780f27d35ce3d1ef06fb8f83feabcc2@456bitbrowser', 'lastIp': '***************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 09:14:17', 'closeTime': '2025-07-25 09:18:53', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 20:44:39', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'e992c27b285546c19e520e4696d2e4af', 'seq': 12, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'a0ef8a0164e034561f94652db2aad39e152346afca77619ef1180c366e243e28@456bitbrowser', 'lastIp': '***********', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-27 21:05:23', 'closeTime': '2025-07-27 21:07:14', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 17:18:01', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'd525608d0ff14f7c9e5b06099b0c0b36', 'seq': 11, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-hk', 'proxyPassword': 'Qq112233', 'lastIp': '**************', 'lastCountry': '香港(HK)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-27 21:07:55', 'closeTime': '2025-07-27 21:10:15', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 14:23:40', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-27 21:07:44', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}]}}
2025-07-29 10:47:06,868 - __main__ - INFO - 获取到 6 个现有浏览器
2025-07-29 10:47:06,869 - __main__ - INFO -   浏览器 1:  (ID: 677f2396fb1e47c5a5a95d1dca7e1483)
2025-07-29 10:47:06,869 - __main__ - INFO -   浏览器 2: _1_1_1_1 (ID: 66db5799ca1243af8cd6e7d3adafafe2)
2025-07-29 10:47:06,869 - __main__ - INFO -   浏览器 3: _1_1_1 (ID: ce586031e017443aaa60d85621cae2e4)
2025-07-29 10:47:06,869 - __main__ - INFO -   浏览器 4: _1_1 (ID: 4dbc5ed032df4443a12d434e4b4d5bba)
2025-07-29 10:47:06,870 - __main__ - INFO -   浏览器 5: _1 (ID: e992c27b285546c19e520e4696d2e4af)
2025-07-29 10:47:06,870 - __main__ - INFO -   浏览器 6:  (ID: d525608d0ff14f7c9e5b06099b0c0b36)
2025-07-29 10:47:06,870 - __main__ - INFO - 系统初始化完成
2025-07-29 10:47:06,873 - __main__ - INFO - 开始索赔自动化流程...
2025-07-29 10:47:10,825 - __main__ - INFO - 待处理数据: 3 条
2025-07-29 10:47:10,825 - __main__ - INFO - 使用 1 个线程进行处理
2025-07-29 10:47:10,825 - __main__ - INFO - 邮编将自动补零到5位
2025-07-29 10:47:13,266 - __main__ - INFO - 🔥 使用真正的读一条删除一条模式
2025-07-29 10:47:13,266 - __main__ - INFO - 📋 特点: 每个线程直接从文件读取数据，读取后立即删除该行
2025-07-29 10:47:13,267 - __main__ - INFO - 启动读一条删除一条线程 1，使用浏览器ID: 677f2396fb1e47c5a5a95d1dca7e1483
2025-07-29 10:47:13,304 - __main__ - INFO - 线程 1 (ID: 16880) 开始工作，固定使用浏览器ID: 677f2396fb1e47c5a5a95d1dca7e1483
2025-07-29 10:47:13,305 - __main__ - INFO - 线程 1 使用真正的读一条删除一条模式
2025-07-29 10:47:13,307 - src.file_handler - INFO - 读取并删除行: Linda----White----70 Bennett st----BROCKTON----MA-...
2025-07-29 10:47:15,268 - __main__ - INFO - 等待所有线程完成...
2025-07-29 10:49:24,055 - src.file_handler - INFO - 写入成功记录: Linda----White----70 Bennett st----BROCKTON----MA-...
2025-07-29 10:49:24,055 - __main__ - INFO - 线程 1 等待 32.0 秒...
2025-07-29 10:49:56,023 - src.file_handler - INFO - 读取并删除行: Aaron----Walsh----714 Arboretum Way----BURLINGTON-...
2025-07-29 10:52:04,911 - src.file_handler - INFO - 写入成功记录: Aaron----Walsh----714 Arboretum Way----BURLINGTON-...
2025-07-29 10:52:04,911 - __main__ - INFO - 线程 1 等待 58.8 秒...
2025-07-29 10:53:03,683 - src.file_handler - INFO - 读取并删除行: Suzanne----Bowes----79 Jackson St----Belchertown--...
2025-07-29 10:55:17,651 - src.file_handler - INFO - 写入成功记录: Suzanne----Bowes----79 Jackson St----Belchertown--...
2025-07-29 10:55:17,652 - __main__ - INFO - 线程 1 等待 33.2 秒...
2025-07-29 10:55:50,812 - __main__ - INFO - 线程 1 完成所有任务，退出
2025-07-29 10:55:50,813 - __main__ - INFO - 线程 1 退出
2025-07-29 10:55:50,813 - __main__ - INFO - 所有任务已完成
2025-07-29 10:55:50,814 - __main__ - INFO - 关闭索赔自动化系统...
2025-07-29 10:55:50,814 - __main__ - INFO - 系统已关闭
2025-07-29 11:03:18,929 - __main__ - INFO - 初始化索赔自动化系统...
2025-07-29 11:03:18,929 - src.bit_browser - INFO - 发送API请求: http://127.0.0.1:54345/browser/list, 数据: {'page': 0, 'pageSize': 100}
2025-07-29 11:03:20,860 - src.bit_browser - INFO - API响应: {'success': True, 'data': {'page': 0, 'pageSize': 100, 'totalNum': 6, 'list': [{'id': '677f2396fb1e47c5a5a95d1dca7e1483', 'seq': 72, 'code': '20250729103719519', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': '36473ef5a69d0627357f6f278e93fd5ee338e5b0cf8bf765449909789cebcf0b@456bitbrowser', 'lastIp': '************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-29 10:53:13', 'closeTime': '2025-07-29 10:55:19', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-29 10:37:20', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': True, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': True, 'clearCookiesBeforeLaunch': False, 'clearHistoriesBeforeLaunch': False, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': None, 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '66db5799ca1243af8cd6e7d3adafafe2', 'seq': 70, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'Qq112233', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-29 08:29:14', 'closeTime': '2025-07-29 10:32:35', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-25 10:54:58', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-25 16:59:25', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': True, 'clearCookiesBeforeLaunch': False, 'clearHistoriesBeforeLaunch': False, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'ce586031e017443aaa60d85621cae2e4', 'seq': 55, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'Qq112233', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 10:53:55', 'closeTime': '2025-07-25 10:54:42', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-24 19:23:10', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-25 10:07:07', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '4dbc5ed032df4443a12d434e4b4d5bba', 'seq': 13, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'd7f83ae959de0ae5680b2321c29bc7fff780f27d35ce3d1ef06fb8f83feabcc2@456bitbrowser', 'lastIp': '***************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-25 09:14:17', 'closeTime': '2025-07-25 09:18:53', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 20:44:39', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'e992c27b285546c19e520e4696d2e4af', 'seq': 12, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'a0ef8a0164e034561f94652db2aad39e152346afca77619ef1180c366e243e28@456bitbrowser', 'lastIp': '***********', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-27 21:05:23', 'closeTime': '2025-07-27 21:07:14', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 17:18:01', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'd525608d0ff14f7c9e5b06099b0c0b36', 'seq': 11, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-hk', 'proxyPassword': 'Qq112233', 'lastIp': '**************', 'lastCountry': '香港(HK)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-27 21:07:55', 'closeTime': '2025-07-27 21:10:15', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 14:23:40', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-27 21:07:44', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}]}}
2025-07-29 11:03:20,871 - __main__ - INFO - 获取到 6 个现有浏览器
2025-07-29 11:03:20,871 - __main__ - INFO -   浏览器 1:  (ID: 677f2396fb1e47c5a5a95d1dca7e1483)
2025-07-29 11:03:20,871 - __main__ - INFO -   浏览器 2: _1_1_1_1 (ID: 66db5799ca1243af8cd6e7d3adafafe2)
2025-07-29 11:03:20,871 - __main__ - INFO -   浏览器 3: _1_1_1 (ID: ce586031e017443aaa60d85621cae2e4)
2025-07-29 11:03:20,872 - __main__ - INFO -   浏览器 4: _1_1 (ID: 4dbc5ed032df4443a12d434e4b4d5bba)
2025-07-29 11:03:20,872 - __main__ - INFO -   浏览器 5: _1 (ID: e992c27b285546c19e520e4696d2e4af)
2025-07-29 11:03:20,872 - __main__ - INFO -   浏览器 6:  (ID: d525608d0ff14f7c9e5b06099b0c0b36)
2025-07-29 11:03:20,872 - __main__ - INFO - 系统初始化完成
2025-07-29 11:03:20,875 - __main__ - INFO - 开始索赔自动化流程...
2025-07-29 11:04:33,067 - __main__ - INFO - 待处理数据: 12 条
2025-07-29 11:04:33,067 - __main__ - INFO - 使用 6 个线程进行处理
2025-07-29 11:04:33,067 - __main__ - INFO - 邮编将自动补零到5位
2025-07-29 11:04:35,199 - __main__ - INFO - 🔥 使用真正的读一条删除一条模式
2025-07-29 11:04:35,199 - __main__ - INFO - 📋 特点: 每个线程直接从文件读取数据，读取后立即删除该行
2025-07-29 11:04:35,200 - __main__ - INFO - 线程 1 (ID: 36676) 开始工作，固定使用浏览器ID: 677f2396fb1e47c5a5a95d1dca7e1483
2025-07-29 11:04:35,200 - __main__ - INFO - 启动读一条删除一条线程 1，使用浏览器ID: 677f2396fb1e47c5a5a95d1dca7e1483
2025-07-29 11:04:35,200 - __main__ - INFO - 线程 1 使用真正的读一条删除一条模式
2025-07-29 11:04:35,201 - src.file_handler - INFO - 读取并删除行: Seth----Vachon----67 Germain ave----QUINCY----MA--...
2025-07-29 11:04:37,202 - __main__ - INFO - 线程 2 (ID: 10064) 开始工作，固定使用浏览器ID: 66db5799ca1243af8cd6e7d3adafafe2
2025-07-29 11:04:37,202 - __main__ - INFO - 启动读一条删除一条线程 2，使用浏览器ID: 66db5799ca1243af8cd6e7d3adafafe2
2025-07-29 11:04:37,202 - __main__ - INFO - 线程 2 使用真正的读一条删除一条模式
2025-07-29 11:04:37,204 - src.file_handler - INFO - 读取并删除行: melisa----fletcher----7 Donna Ave----Gardner----MA...
2025-07-29 11:04:39,204 - __main__ - INFO - 线程 3 (ID: 29616) 开始工作，固定使用浏览器ID: ce586031e017443aaa60d85621cae2e4
2025-07-29 11:04:39,204 - __main__ - INFO - 启动读一条删除一条线程 3，使用浏览器ID: ce586031e017443aaa60d85621cae2e4
2025-07-29 11:04:39,204 - __main__ - INFO - 线程 3 使用真正的读一条删除一条模式
2025-07-29 11:04:39,205 - src.file_handler - INFO - 读取并删除行: William----Union----4 Old English Rd----Worcester-...
2025-07-29 11:04:41,205 - __main__ - INFO - 线程 4 (ID: 15500) 开始工作，固定使用浏览器ID: 4dbc5ed032df4443a12d434e4b4d5bba
2025-07-29 11:04:41,205 - __main__ - INFO - 启动读一条删除一条线程 4，使用浏览器ID: 4dbc5ed032df4443a12d434e4b4d5bba
2025-07-29 11:04:41,206 - __main__ - INFO - 线程 4 使用真正的读一条删除一条模式
2025-07-29 11:04:41,207 - src.file_handler - INFO - 读取并删除行: Anthony----Antonellis----407 D St----BOSTON----MA-...
2025-07-29 11:04:43,207 - __main__ - INFO - 线程 5 (ID: 980) 开始工作，固定使用浏览器ID: e992c27b285546c19e520e4696d2e4af
2025-07-29 11:04:43,207 - __main__ - INFO - 启动读一条删除一条线程 5，使用浏览器ID: e992c27b285546c19e520e4696d2e4af
2025-07-29 11:04:43,207 - __main__ - INFO - 线程 5 使用真正的读一条删除一条模式
2025-07-29 11:04:43,209 - src.file_handler - INFO - 读取并删除行: Ruben----Ramirez----40Walnut St#2----DORCHESTER---...
2025-07-29 11:04:45,208 - __main__ - INFO - 线程 6 (ID: 16164) 开始工作，固定使用浏览器ID: d525608d0ff14f7c9e5b06099b0c0b36
2025-07-29 11:04:45,209 - __main__ - INFO - 启动读一条删除一条线程 6，使用浏览器ID: d525608d0ff14f7c9e5b06099b0c0b36
2025-07-29 11:04:45,209 - __main__ - INFO - 线程 6 使用真正的读一条删除一条模式
2025-07-29 11:04:45,210 - src.file_handler - INFO - 读取并删除行: Jennie----Emery----41 millers falls road----TURNER...
2025-07-29 11:04:47,210 - __main__ - INFO - 等待所有线程完成...
2025-07-29 11:06:45,664 - src.file_handler - INFO - 写入成功记录: melisa----fletcher----7 Donna Ave----Gardner----MA...
2025-07-29 11:06:45,664 - __main__ - INFO - 线程 2 等待 56.2 秒...
2025-07-29 11:07:06,480 - src.file_handler - INFO - 写入成功记录: Jennie----Emery----41 millers falls road----TURNER...
2025-07-29 11:07:06,481 - __main__ - INFO - 线程 6 等待 56.0 秒...
2025-07-29 11:07:11,953 - src.file_handler - INFO - 写入失败记录: Anthony----Antonellis----407 D St----BOSTON----MA-...
2025-07-29 11:07:11,954 - __main__ - ERROR - 线程 4 任务失败: 填表任务失败
2025-07-29 11:07:11,954 - __main__ - INFO - 线程 4 等待 51.4 秒...
2025-07-29 11:07:15,143 - src.file_handler - INFO - 写入成功记录: William----Union----4 Old English Rd----Worcester-...
2025-07-29 11:07:15,143 - __main__ - INFO - 线程 3 等待 38.9 秒...
2025-07-29 11:07:20,724 - src.file_handler - INFO - 写入失败记录: Ruben----Ramirez----40Walnut St#2----DORCHESTER---...
2025-07-29 11:07:20,724 - __main__ - ERROR - 线程 5 任务失败: 填表任务失败
2025-07-29 11:07:20,724 - __main__ - INFO - 线程 5 等待 34.0 秒...
2025-07-29 11:07:41,891 - src.file_handler - INFO - 读取并删除行: Jonathan----Marrero----42 Frazer street----HYDE PA...
2025-07-29 11:07:54,038 - src.file_handler - INFO - 读取并删除行: Christine----Joyner----42A Sterling Road----Prince...
2025-07-29 11:07:54,740 - src.file_handler - INFO - 读取并删除行: Benjamin----Poirier----437 Broadway----NORTH ATTLE...
2025-07-29 11:08:02,497 - src.file_handler - INFO - 读取并删除行: Karen----Harvey----44 MANNING RD----Chelmsford----...
2025-07-29 11:08:03,334 - src.file_handler - INFO - 读取并删除行: Brittany----Acca----49 Newport st----DORCHESTER---...
2025-07-29 11:08:26,229 - src.file_handler - INFO - 写入成功记录: Seth----Vachon----67 Germain ave----QUINCY----MA--...
2025-07-29 11:08:26,230 - __main__ - INFO - 线程 1 等待 34.7 秒...
2025-07-29 11:09:00,961 - src.file_handler - INFO - 读取并删除行: Dexter----Morgan----5 Marcella St Apt 1----Roxbury...
2025-07-29 11:09:53,803 - src.file_handler - INFO - 写入成功记录: Jonathan----Marrero----42 Frazer street----HYDE PA...
2025-07-29 11:09:53,804 - __main__ - INFO - 线程 2 等待 30.5 秒...
2025-07-29 11:10:06,168 - src.file_handler - INFO - 写入成功记录: Brittany----Acca----49 Newport st----DORCHESTER---...
2025-07-29 11:10:06,169 - __main__ - INFO - 线程 4 等待 40.3 秒...
2025-07-29 11:10:07,778 - src.file_handler - INFO - 写入成功记录: Karen----Harvey----44 MANNING RD----Chelmsford----...
2025-07-29 11:10:07,778 - __main__ - INFO - 线程 6 等待 47.1 秒...
2025-07-29 11:10:12,763 - src.file_handler - INFO - 写入成功记录: Benjamin----Poirier----437 Broadway----NORTH ATTLE...
2025-07-29 11:10:12,763 - __main__ - INFO - 线程 5 等待 30.7 秒...
2025-07-29 11:10:24,302 - __main__ - INFO - 线程 2 完成所有任务，退出
2025-07-29 11:10:24,303 - __main__ - INFO - 线程 2 退出
2025-07-29 11:10:43,453 - __main__ - INFO - 线程 5 完成所有任务，退出
2025-07-29 11:10:43,453 - __main__ - INFO - 线程 5 退出
2025-07-29 11:10:44,629 - src.file_handler - INFO - 写入成功记录: Christine----Joyner----42A Sterling Road----Prince...
2025-07-29 11:10:44,629 - __main__ - INFO - 线程 3 等待 42.1 秒...
2025-07-29 11:10:46,509 - __main__ - INFO - 线程 4 完成所有任务，退出
2025-07-29 11:10:46,510 - __main__ - INFO - 线程 4 退出
2025-07-29 11:10:54,876 - __main__ - INFO - 线程 6 完成所有任务，退出
2025-07-29 11:10:54,876 - __main__ - INFO - 线程 6 退出
2025-07-29 11:11:13,235 - src.file_handler - INFO - 写入成功记录: Dexter----Morgan----5 Marcella St Apt 1----Roxbury...
2025-07-29 11:11:13,235 - __main__ - INFO - 线程 1 等待 55.1 秒...
2025-07-29 11:11:26,698 - __main__ - INFO - 线程 3 完成所有任务，退出
2025-07-29 11:11:26,698 - __main__ - INFO - 线程 3 退出
2025-07-29 11:12:06,145 - __main__ - INFO - 初始化索赔自动化系统...
2025-07-29 11:12:06,145 - src.bit_browser - INFO - 发送API请求: http://127.0.0.1:54345/browser/list, 数据: {'page': 0, 'pageSize': 100}
2025-07-29 11:12:09,338 - src.bit_browser - INFO - API响应: {'success': True, 'data': {'page': 0, 'pageSize': 100, 'totalNum': 6, 'list': [{'id': '677f2396fb1e47c5a5a95d1dca7e1483', 'seq': 72, 'code': '20250729103719519', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': '36473ef5a69d0627357f6f278e93fd5ee338e5b0cf8bf765449909789cebcf0b@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-29 11:09:16', 'closeTime': '2025-07-29 11:11:15', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-29 10:37:20', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': True, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': True, 'clearCookiesBeforeLaunch': False, 'clearHistoriesBeforeLaunch': False, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': None, 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '66db5799ca1243af8cd6e7d3adafafe2', 'seq': 70, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'Qq112233', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-29 11:07:53', 'closeTime': '2025-07-29 11:09:55', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-25 10:54:58', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-25 16:59:25', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': True, 'clearCookiesBeforeLaunch': False, 'clearHistoriesBeforeLaunch': False, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'ce586031e017443aaa60d85621cae2e4', 'seq': 55, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'Qq112233', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-29 11:04:48', 'closeTime': '2025-07-29 11:10:46', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-24 19:23:10', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-25 10:07:07', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '4dbc5ed032df4443a12d434e4b4d5bba', 'seq': 13, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'd7f83ae959de0ae5680b2321c29bc7fff780f27d35ce3d1ef06fb8f83feabcc2@456bitbrowser', 'lastIp': '*************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-29 11:04:51', 'closeTime': '2025-07-29 11:10:07', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 20:44:39', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'e992c27b285546c19e520e4696d2e4af', 'seq': 12, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'a0ef8a0164e034561f94652db2aad39e152346afca77619ef1180c366e243e28@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-29 11:04:51', 'closeTime': '2025-07-29 11:10:14', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 17:18:01', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'd525608d0ff14f7c9e5b06099b0c0b36', 'seq': 11, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-hk', 'proxyPassword': 'Qq112233', 'lastIp': '***********', 'lastCountry': '香港(HK)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-29 11:05:02', 'closeTime': '2025-07-29 11:10:08', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 14:23:40', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-27 21:07:44', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}]}}
2025-07-29 11:12:09,345 - __main__ - INFO - 获取到 6 个现有浏览器
2025-07-29 11:12:09,346 - __main__ - INFO -   浏览器 1:  (ID: 677f2396fb1e47c5a5a95d1dca7e1483)
2025-07-29 11:12:09,346 - __main__ - INFO -   浏览器 2: _1_1_1_1 (ID: 66db5799ca1243af8cd6e7d3adafafe2)
2025-07-29 11:12:09,346 - __main__ - INFO -   浏览器 3: _1_1_1 (ID: ce586031e017443aaa60d85621cae2e4)
2025-07-29 11:12:09,346 - __main__ - INFO -   浏览器 4: _1_1 (ID: 4dbc5ed032df4443a12d434e4b4d5bba)
2025-07-29 11:12:09,347 - __main__ - INFO -   浏览器 5: _1 (ID: e992c27b285546c19e520e4696d2e4af)
2025-07-29 11:12:09,347 - __main__ - INFO -   浏览器 6:  (ID: d525608d0ff14f7c9e5b06099b0c0b36)
2025-07-29 11:12:09,347 - __main__ - INFO - 系统初始化完成
2025-07-29 11:12:09,350 - __main__ - INFO - 开始索赔自动化流程...
2025-07-29 11:12:13,930 - __main__ - INFO - 待处理数据: 2 条
2025-07-29 11:12:13,930 - __main__ - INFO - 使用 2 个线程进行处理
2025-07-29 11:12:13,930 - __main__ - INFO - 邮编将自动补零到5位
2025-07-29 11:12:16,512 - __main__ - INFO - 🔥 使用真正的读一条删除一条模式
2025-07-29 11:12:16,513 - __main__ - INFO - 📋 特点: 每个线程直接从文件读取数据，读取后立即删除该行
2025-07-29 11:12:16,514 - __main__ - INFO - 启动读一条删除一条线程 1，使用浏览器ID: 677f2396fb1e47c5a5a95d1dca7e1483
2025-07-29 11:12:16,515 - __main__ - INFO - 线程 1 (ID: 34116) 开始工作，固定使用浏览器ID: 677f2396fb1e47c5a5a95d1dca7e1483
2025-07-29 11:12:16,516 - __main__ - INFO - 线程 1 使用真正的读一条删除一条模式
2025-07-29 11:12:16,517 - src.file_handler - INFO - 读取并删除行: Anthony----Antonellis----407 D St----BOSTON----MA-...
2025-07-29 11:12:18,515 - __main__ - INFO - 线程 2 (ID: 8684) 开始工作，固定使用浏览器ID: 66db5799ca1243af8cd6e7d3adafafe2
2025-07-29 11:12:18,515 - __main__ - INFO - 启动读一条删除一条线程 2，使用浏览器ID: 66db5799ca1243af8cd6e7d3adafafe2
2025-07-29 11:12:18,515 - __main__ - INFO - 线程 2 使用真正的读一条删除一条模式
2025-07-29 11:12:18,518 - src.file_handler - INFO - 读取并删除行: Ruben----Ramirez----40Walnut St#2----DORCHESTER---...
2025-07-29 11:12:20,516 - __main__ - INFO - 等待所有线程完成...
2025-07-29 11:14:25,222 - src.file_handler - INFO - 写入成功记录: Anthony----Antonellis----407 D St----BOSTON----MA-...
2025-07-29 11:14:25,223 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-29 11:14:27,151 - src.file_handler - INFO - 写入成功记录: Ruben----Ramirez----40Walnut St#2----DORCHESTER---...
2025-07-29 11:14:27,152 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-29 11:14:35,224 - __main__ - INFO - 线程 1 完成所有任务，退出
2025-07-29 11:14:35,225 - __main__ - INFO - 线程 1 退出
2025-07-29 11:14:37,153 - __main__ - INFO - 线程 2 完成所有任务，退出
2025-07-29 11:14:37,154 - __main__ - INFO - 线程 2 退出
2025-07-29 11:14:37,154 - __main__ - INFO - 所有任务已完成
2025-07-29 11:14:37,155 - __main__ - INFO - 关闭索赔自动化系统...
2025-07-29 11:14:37,155 - __main__ - INFO - 系统已关闭
2025-07-29 11:20:24,635 - __main__ - INFO - 初始化索赔自动化系统...
2025-07-29 11:20:24,636 - src.bit_browser - INFO - 发送API请求: http://127.0.0.1:54345/browser/list, 数据: {'page': 0, 'pageSize': 100}
2025-07-29 11:20:27,594 - src.bit_browser - INFO - API响应: {'success': True, 'data': {'page': 0, 'pageSize': 100, 'totalNum': 6, 'list': [{'id': '677f2396fb1e47c5a5a95d1dca7e1483', 'seq': 72, 'code': '20250729103719519', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': '36473ef5a69d0627357f6f278e93fd5ee338e5b0cf8bf765449909789cebcf0b@456bitbrowser', 'lastIp': '*************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-29 11:12:26', 'closeTime': '2025-07-29 11:14:27', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-29 10:37:20', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': True, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': True, 'clearCookiesBeforeLaunch': False, 'clearHistoriesBeforeLaunch': False, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': None, 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '66db5799ca1243af8cd6e7d3adafafe2', 'seq': 70, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'Qq112233', 'lastIp': '*************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-29 11:12:26', 'closeTime': '2025-07-29 11:14:28', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-25 10:54:58', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-25 16:59:25', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': True, 'clearCookiesBeforeLaunch': False, 'clearHistoriesBeforeLaunch': False, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'ce586031e017443aaa60d85621cae2e4', 'seq': 55, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'Qq112233', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-29 11:04:48', 'closeTime': '2025-07-29 11:10:46', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-24 19:23:10', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-25 10:07:07', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '4dbc5ed032df4443a12d434e4b4d5bba', 'seq': 13, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'd7f83ae959de0ae5680b2321c29bc7fff780f27d35ce3d1ef06fb8f83feabcc2@456bitbrowser', 'lastIp': '*************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-29 11:04:51', 'closeTime': '2025-07-29 11:10:07', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 20:44:39', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'e992c27b285546c19e520e4696d2e4af', 'seq': 12, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'a0ef8a0164e034561f94652db2aad39e152346afca77619ef1180c366e243e28@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-29 11:04:51', 'closeTime': '2025-07-29 11:10:14', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 17:18:01', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'd525608d0ff14f7c9e5b06099b0c0b36', 'seq': 11, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-hk', 'proxyPassword': 'Qq112233', 'lastIp': '***********', 'lastCountry': '香港(HK)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-29 11:05:02', 'closeTime': '2025-07-29 11:10:08', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-21 14:23:40', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'updateTime': '2025-07-27 21:07:44', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}]}}
2025-07-29 11:20:27,601 - __main__ - INFO - 获取到 6 个现有浏览器
2025-07-29 11:20:27,601 - __main__ - INFO -   浏览器 1:  (ID: 677f2396fb1e47c5a5a95d1dca7e1483)
2025-07-29 11:20:27,601 - __main__ - INFO -   浏览器 2: _1_1_1_1 (ID: 66db5799ca1243af8cd6e7d3adafafe2)
2025-07-29 11:20:27,602 - __main__ - INFO -   浏览器 3: _1_1_1 (ID: ce586031e017443aaa60d85621cae2e4)
2025-07-29 11:20:27,602 - __main__ - INFO -   浏览器 4: _1_1 (ID: 4dbc5ed032df4443a12d434e4b4d5bba)
2025-07-29 11:20:27,602 - __main__ - INFO -   浏览器 5: _1 (ID: e992c27b285546c19e520e4696d2e4af)
2025-07-29 11:20:27,602 - __main__ - INFO -   浏览器 6:  (ID: d525608d0ff14f7c9e5b06099b0c0b36)
2025-07-29 11:20:27,603 - __main__ - INFO - 系统初始化完成
2025-07-29 11:20:27,605 - __main__ - INFO - 开始索赔自动化流程...
2025-07-29 11:20:30,557 - __main__ - INFO - 待处理数据: 1 条
2025-07-29 11:20:30,557 - __main__ - INFO - 使用 1 个线程进行处理
2025-07-29 11:20:30,557 - __main__ - INFO - 邮编将自动补零到5位
2025-07-29 11:20:31,821 - __main__ - INFO - 🔥 使用真正的读一条删除一条模式
2025-07-29 11:20:31,822 - __main__ - INFO - 📋 特点: 每个线程直接从文件读取数据，读取后立即删除该行
2025-07-29 11:20:31,822 - __main__ - INFO - 启动读一条删除一条线程 1，使用浏览器ID: 677f2396fb1e47c5a5a95d1dca7e1483
2025-07-29 11:20:31,824 - __main__ - INFO - 线程 1 (ID: 21700) 开始工作，固定使用浏览器ID: 677f2396fb1e47c5a5a95d1dca7e1483
2025-07-29 11:20:31,824 - __main__ - INFO - 线程 1 使用真正的读一条删除一条模式
2025-07-29 11:20:31,827 - src.file_handler - INFO - 读取并删除行: William----Union----4 Old English Rd----Worcester-...
2025-07-29 11:20:33,823 - __main__ - INFO - 等待所有线程完成...
2025-07-30 10:16:55,775 - __main__ - INFO - 初始化索赔自动化系统...
2025-07-30 10:16:55,776 - src.bit_browser - INFO - 发送API请求: http://127.0.0.1:54345/browser/list, 数据: {'page': 0, 'pageSize': 100}
2025-07-30 10:16:57,203 - src.bit_browser - INFO - API响应: {'success': True, 'data': {'page': 0, 'pageSize': 100, 'totalNum': 5, 'list': [{'id': 'edbee0a1ad4941c58e8a563f489ac85b', 'seq': 74, 'code': '20250730072357086', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_4', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'ac1e3ce03e8edd2de76620548a9ba9beb1ca2adeae3341d051305ab29a1ec188@456bitbrowser', 'lastIp': '*************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 09:39:18', 'closeTime': '2025-07-30 10:16:21', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '630087fbdb154160bebb2806f3156e26', 'seq': 73, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_3', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'aaba0991a0ef87d1a68670764f85927b7e75aef989b30aadbe93055a9a846218@456bitbrowser', 'lastIp': '', 'lastCountry': '', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'ae73464fb8734427a0df8a3bc7e23682', 'seq': 72, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_2', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'e19983de84b05ff79a0cca64d40780e0666119f31127edfdb872b80801fa752a@456bitbrowser', 'lastIp': '', 'lastCountry': '', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'closeTime': '2025-07-30 07:24:45', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '87042e8ab19e45e4b90a3eae6af541d1', 'seq': 71, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': '9414e8b07fd481afcbccfe3f2f48930bdd6f6f4ca919f967e910ff9f148fc5e5@456bitbrowser', 'lastIp': '', 'lastCountry': '', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '********************************', 'seq': 70, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'd482b33e9915443b11f84ce69dd22d13ff0c03c97d343c7322313330a0388cee@456bitbrowser', 'lastIp': '', 'lastCountry': '', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:46', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': True, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': None, 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}]}}
2025-07-30 10:16:57,211 - __main__ - INFO - 获取到 5 个现有浏览器
2025-07-30 10:16:57,211 - __main__ - INFO -   浏览器 1: _4 (ID: edbee0a1ad4941c58e8a563f489ac85b)
2025-07-30 10:16:57,211 - __main__ - INFO -   浏览器 2: _3 (ID: 630087fbdb154160bebb2806f3156e26)
2025-07-30 10:16:57,212 - __main__ - INFO -   浏览器 3: _2 (ID: ae73464fb8734427a0df8a3bc7e23682)
2025-07-30 10:16:57,212 - __main__ - INFO -   浏览器 4: _1 (ID: 87042e8ab19e45e4b90a3eae6af541d1)
2025-07-30 10:16:57,212 - __main__ - INFO -   浏览器 5:  (ID: ********************************)
2025-07-30 10:16:57,212 - __main__ - INFO - 系统初始化完成
2025-07-30 10:16:57,215 - __main__ - INFO - 开始索赔自动化流程...
2025-07-30 10:17:05,095 - __main__ - INFO - 待处理数据: 10 条
2025-07-30 10:17:05,095 - __main__ - INFO - 使用 2 个线程进行处理
2025-07-30 10:17:05,095 - __main__ - INFO - 邮编将自动补零到5位
2025-07-30 10:17:05,096 - __main__ - INFO - 🔥 使用真正的读一条删除一条模式
2025-07-30 10:17:05,096 - __main__ - INFO - 📋 特点: 每个线程直接从文件读取数据，读取后立即删除该行
2025-07-30 10:17:05,097 - __main__ - INFO - 启动读一条删除一条线程 1，使用浏览器ID: edbee0a1ad4941c58e8a563f489ac85b
2025-07-30 10:17:05,100 - __main__ - INFO - 线程 1 (ID: 30884) 开始工作，固定使用浏览器ID: edbee0a1ad4941c58e8a563f489ac85b
2025-07-30 10:17:05,100 - __main__ - INFO - 线程 1 使用真正的读一条删除一条模式
2025-07-30 10:17:05,106 - src.file_handler - INFO - 读取并删除行: William----Union----4 Old English Rd----Worcester-...
2025-07-30 10:17:07,098 - __main__ - INFO - 线程 2 (ID: 20352) 开始工作，固定使用浏览器ID: 630087fbdb154160bebb2806f3156e26
2025-07-30 10:17:07,098 - __main__ - INFO - 启动读一条删除一条线程 2，使用浏览器ID: 630087fbdb154160bebb2806f3156e26
2025-07-30 10:17:07,098 - __main__ - INFO - 线程 2 使用真正的读一条删除一条模式
2025-07-30 10:17:07,100 - src.file_handler - INFO - 读取并删除行: Anthony----Antonellis----407 D St----BOSTON----MA-...
2025-07-30 10:17:09,099 - __main__ - INFO - 等待所有线程完成...
2025-07-30 10:19:15,080 - src.file_handler - INFO - 写入成功记录: William----Union----4 Old English Rd----Worcester-...
2025-07-30 10:19:15,081 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 10:19:18,897 - src.file_handler - INFO - 写入成功记录: Anthony----Antonellis----407 D St----BOSTON----MA-...
2025-07-30 10:19:18,898 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 10:19:25,084 - src.file_handler - INFO - 读取并删除行: Ruben----Ramirez----40Walnut St#2----DORCHESTER---...
2025-07-30 10:19:28,900 - src.file_handler - INFO - 读取并删除行: Jennie----Emery----41 millers falls road----TURNER...
2025-07-30 10:21:25,105 - src.file_handler - INFO - 写入成功记录: Ruben----Ramirez----40Walnut St#2----DORCHESTER---...
2025-07-30 10:21:25,106 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 10:21:29,520 - src.file_handler - INFO - 写入成功记录: Jennie----Emery----41 millers falls road----TURNER...
2025-07-30 10:21:29,521 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 10:21:35,107 - src.file_handler - INFO - 读取并删除行: Jennifer----Mccarthy----55 Woodsong----Plymouth---...
2025-07-30 10:21:39,522 - src.file_handler - INFO - 读取并删除行: Nancy----Dorrance----575 Wilmarth St.----ATTLEBORO...
2025-07-30 10:23:38,200 - src.file_handler - INFO - 写入成功记录: Jennifer----Mccarthy----55 Woodsong----Plymouth---...
2025-07-30 10:23:38,201 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 10:23:41,321 - src.file_handler - INFO - 写入成功记录: Nancy----Dorrance----575 Wilmarth St.----ATTLEBORO...
2025-07-30 10:23:41,321 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 10:23:48,203 - src.file_handler - INFO - 读取并删除行: William----Sechrist----58 East Longmeadow Road----...
2025-07-30 10:23:51,323 - src.file_handler - INFO - 读取并删除行: Cesar----Puemape----6 wadsworth Street----ALLSTON-...
2025-07-30 10:25:49,844 - src.file_handler - INFO - 写入成功记录: William----Sechrist----58 East Longmeadow Road----...
2025-07-30 10:25:49,844 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 10:25:59,846 - src.file_handler - INFO - 读取并删除行: Ali----Obuz----6 Woodsong Rd----WESTFIELD----MA---...
2025-07-30 10:26:00,790 - src.file_handler - INFO - 写入成功记录: Cesar----Puemape----6 wadsworth Street----ALLSTON-...
2025-07-30 10:26:00,791 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 10:26:10,793 - src.file_handler - INFO - 读取并删除行: lori----elfman----60 sarah drive----BRIDGEWATER---...
2025-07-30 10:28:05,804 - src.file_handler - INFO - 写入成功记录: Ali----Obuz----6 Woodsong Rd----WESTFIELD----MA---...
2025-07-30 10:28:05,804 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 10:28:15,806 - src.file_handler - INFO - 读取并删除行: Adnan----Termos----603 Eastern Ave----Fall River--...
2025-07-30 10:28:20,545 - src.file_handler - INFO - 写入成功记录: lori----elfman----60 sarah drive----BRIDGEWATER---...
2025-07-30 10:28:20,546 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 10:28:30,549 - src.file_handler - INFO - 读取并删除行: Simon----Hong----632 Massachusetts Ave Apt 602----...
2025-07-30 10:30:22,006 - src.file_handler - INFO - 写入成功记录: Adnan----Termos----603 Eastern Ave----Fall River--...
2025-07-30 10:30:22,007 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 10:30:32,009 - src.file_handler - INFO - 读取并删除行: Christine----McIntosh----665 PRIMROSE ST----HAVERH...
2025-07-30 10:30:34,476 - src.file_handler - INFO - 写入成功记录: Simon----Hong----632 Massachusetts Ave Apt 602----...
2025-07-30 10:30:34,477 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 10:30:44,479 - src.file_handler - INFO - 读取并删除行: Seth----Vachon----67 Germain ave----QUINCY----MA--...
2025-07-30 10:32:32,796 - src.file_handler - INFO - 写入成功记录: Christine----McIntosh----665 PRIMROSE ST----HAVERH...
2025-07-30 10:32:32,797 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 10:32:42,798 - __main__ - INFO - 线程 1 完成所有任务，退出
2025-07-30 10:32:42,799 - __main__ - INFO - 线程 1 退出
2025-07-30 10:32:51,064 - src.file_handler - INFO - 写入成功记录: Seth----Vachon----67 Germain ave----QUINCY----MA--...
2025-07-30 10:32:51,064 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 10:33:01,066 - __main__ - INFO - 线程 2 完成所有任务，退出
2025-07-30 10:33:01,066 - __main__ - INFO - 线程 2 退出
2025-07-30 10:33:01,067 - __main__ - INFO - 所有任务已完成
2025-07-30 10:33:01,067 - __main__ - INFO - 关闭索赔自动化系统...
2025-07-30 10:33:01,067 - __main__ - INFO - 系统已关闭
2025-07-30 10:33:25,890 - __main__ - INFO - 初始化索赔自动化系统...
2025-07-30 10:33:25,890 - src.bit_browser - INFO - 发送API请求: http://127.0.0.1:54345/browser/list, 数据: {'page': 0, 'pageSize': 100}
2025-07-30 10:33:27,416 - src.bit_browser - INFO - API响应: {'success': True, 'data': {'page': 0, 'pageSize': 100, 'totalNum': 5, 'list': [{'id': 'edbee0a1ad4941c58e8a563f489ac85b', 'seq': 74, 'code': '20250730072357086', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_4', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'ac1e3ce03e8edd2de76620548a9ba9beb1ca2adeae3341d051305ab29a1ec188@456bitbrowser', 'lastIp': '************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 10:30:40', 'closeTime': '2025-07-30 10:32:34', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '630087fbdb154160bebb2806f3156e26', 'seq': 73, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_3', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'aaba0991a0ef87d1a68670764f85927b7e75aef989b30aadbe93055a9a846218@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 10:30:54', 'closeTime': '2025-07-30 10:32:52', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'ae73464fb8734427a0df8a3bc7e23682', 'seq': 72, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_2', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'e19983de84b05ff79a0cca64d40780e0666119f31127edfdb872b80801fa752a@456bitbrowser', 'lastIp': '', 'lastCountry': '', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'closeTime': '2025-07-30 07:24:45', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '87042e8ab19e45e4b90a3eae6af541d1', 'seq': 71, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': '9414e8b07fd481afcbccfe3f2f48930bdd6f6f4ca919f967e910ff9f148fc5e5@456bitbrowser', 'lastIp': '', 'lastCountry': '', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '********************************', 'seq': 70, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'd482b33e9915443b11f84ce69dd22d13ff0c03c97d343c7322313330a0388cee@456bitbrowser', 'lastIp': '', 'lastCountry': '', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:46', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': True, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': None, 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}]}}
2025-07-30 10:33:27,427 - __main__ - INFO - 获取到 5 个现有浏览器
2025-07-30 10:33:27,427 - __main__ - INFO -   浏览器 1: _4 (ID: edbee0a1ad4941c58e8a563f489ac85b)
2025-07-30 10:33:27,427 - __main__ - INFO -   浏览器 2: _3 (ID: 630087fbdb154160bebb2806f3156e26)
2025-07-30 10:33:27,427 - __main__ - INFO -   浏览器 3: _2 (ID: ae73464fb8734427a0df8a3bc7e23682)
2025-07-30 10:33:27,428 - __main__ - INFO -   浏览器 4: _1 (ID: 87042e8ab19e45e4b90a3eae6af541d1)
2025-07-30 10:33:27,428 - __main__ - INFO -   浏览器 5:  (ID: ********************************)
2025-07-30 10:33:27,428 - __main__ - INFO - 系统初始化完成
2025-07-30 10:33:27,431 - __main__ - INFO - 开始索赔自动化流程...
2025-07-30 10:33:31,338 - __main__ - INFO - 待处理数据: 20 条
2025-07-30 10:33:31,338 - __main__ - INFO - 使用 2 个线程进行处理
2025-07-30 10:33:31,339 - __main__ - INFO - 邮编将自动补零到5位
2025-07-30 10:33:31,339 - __main__ - INFO - 🔥 使用真正的读一条删除一条模式
2025-07-30 10:33:31,339 - __main__ - INFO - 📋 特点: 每个线程直接从文件读取数据，读取后立即删除该行
2025-07-30 10:33:31,340 - __main__ - INFO - 线程 1 (ID: 11864) 开始工作，固定使用浏览器ID: edbee0a1ad4941c58e8a563f489ac85b
2025-07-30 10:33:31,340 - __main__ - INFO - 启动读一条删除一条线程 1，使用浏览器ID: edbee0a1ad4941c58e8a563f489ac85b
2025-07-30 10:33:31,340 - __main__ - INFO - 线程 1 使用真正的读一条删除一条模式
2025-07-30 10:33:31,342 - src.file_handler - INFO - 读取并删除行: melisa----fletcher----7 Donna Ave----Gardner----MA...
2025-07-30 10:33:33,342 - __main__ - INFO - 线程 2 (ID: 33204) 开始工作，固定使用浏览器ID: 630087fbdb154160bebb2806f3156e26
2025-07-30 10:33:33,342 - __main__ - INFO - 启动读一条删除一条线程 2，使用浏览器ID: 630087fbdb154160bebb2806f3156e26
2025-07-30 10:33:33,343 - __main__ - INFO - 线程 2 使用真正的读一条删除一条模式
2025-07-30 10:33:33,344 - src.file_handler - INFO - 读取并删除行: Thomas----Carroll----7 Thomas Circle----STONEHAM--...
2025-07-30 10:33:35,344 - __main__ - INFO - 等待所有线程完成...
2025-07-30 10:35:34,610 - src.file_handler - INFO - 写入成功记录: melisa----fletcher----7 Donna Ave----Gardner----MA...
2025-07-30 10:35:34,610 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 10:35:37,814 - src.file_handler - INFO - 写入成功记录: Thomas----Carroll----7 Thomas Circle----STONEHAM--...
2025-07-30 10:35:37,815 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 10:35:44,612 - src.file_handler - INFO - 读取并删除行: Linda----White----70 Bennett st----BROCKTON----MA-...
2025-07-30 10:35:47,817 - src.file_handler - INFO - 读取并删除行: Aaron----Walsh----714 Arboretum Way----BURLINGTON-...
2025-07-30 10:37:47,485 - src.file_handler - INFO - 写入成功记录: Aaron----Walsh----714 Arboretum Way----BURLINGTON-...
2025-07-30 10:37:47,486 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 10:37:52,135 - src.file_handler - INFO - 写入成功记录: Linda----White----70 Bennett st----BROCKTON----MA-...
2025-07-30 10:37:52,136 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 10:37:57,488 - src.file_handler - INFO - 读取并删除行: Suzanne----Bowes----79 Jackson St----Belchertown--...
2025-07-30 10:38:02,137 - src.file_handler - INFO - 读取并删除行: Nicole----Davidson----82 Hollingsworth St #3----Ly...
2025-07-30 10:39:57,571 - src.file_handler - INFO - 写入成功记录: Suzanne----Bowes----79 Jackson St----Belchertown--...
2025-07-30 10:39:57,572 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 10:40:03,723 - src.file_handler - INFO - 写入成功记录: Nicole----Davidson----82 Hollingsworth St #3----Ly...
2025-07-30 10:40:03,724 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 10:40:07,574 - src.file_handler - INFO - 读取并删除行: Adry----velez----83 chestnut ave----JAMAICA PLAIN-...
2025-07-30 10:40:13,726 - src.file_handler - INFO - 读取并删除行: Ruth----Beato----86 Everett St----Lawrence----MA--...
2025-07-30 10:42:07,296 - src.file_handler - INFO - 写入成功记录: Adry----velez----83 chestnut ave----JAMAICA PLAIN-...
2025-07-30 10:42:07,297 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 10:42:14,275 - src.file_handler - INFO - 写入成功记录: Ruth----Beato----86 Everett St----Lawrence----MA--...
2025-07-30 10:42:14,276 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 10:42:17,298 - src.file_handler - INFO - 读取并删除行: Lynn----Andrade----86 Maplewood Ave----SWANSEA----...
2025-07-30 10:42:24,278 - src.file_handler - INFO - 读取并删除行: Emmanuel----Apanisile----881 Main St Apt 9----Walp...
2025-07-30 10:44:18,796 - src.file_handler - INFO - 写入成功记录: Lynn----Andrade----86 Maplewood Ave----SWANSEA----...
2025-07-30 10:44:18,797 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 10:44:25,333 - src.file_handler - INFO - 写入成功记录: Emmanuel----Apanisile----881 Main St Apt 9----Walp...
2025-07-30 10:44:25,334 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 10:44:28,799 - src.file_handler - INFO - 读取并删除行: Madeline----Mccollum----881 Main St Apt 9----South...
2025-07-30 10:44:35,336 - src.file_handler - INFO - 读取并删除行: Patricia----Walton----90 Kearney Ave----Pittsfield...
2025-07-30 10:46:38,571 - src.file_handler - INFO - 写入成功记录: Madeline----Mccollum----881 Main St Apt 9----South...
2025-07-30 10:46:38,571 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 10:46:38,781 - src.file_handler - INFO - 写入成功记录: Patricia----Walton----90 Kearney Ave----Pittsfield...
2025-07-30 10:46:38,782 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 10:46:48,573 - src.file_handler - INFO - 读取并删除行: Vincent----Bertain----33 Blanco Pl----Ukiah----CA-...
2025-07-30 10:46:48,784 - src.file_handler - INFO - 读取并删除行: Dyna----Hernandez----549 w Sepulveda st----SAN PED...
2025-07-30 10:48:50,313 - src.file_handler - INFO - 写入成功记录: Dyna----Hernandez----549 w Sepulveda st----SAN PED...
2025-07-30 10:48:50,313 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 10:48:59,706 - src.file_handler - INFO - 写入成功记录: Vincent----Bertain----33 Blanco Pl----Ukiah----CA-...
2025-07-30 10:48:59,706 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 10:49:00,316 - src.file_handler - INFO - 读取并删除行: Roberto----Vargas----1503 Liberty Pl----Escondido-...
2025-07-30 10:49:09,708 - src.file_handler - INFO - 读取并删除行: Anush----Harutunyan----13559 Crewe St----Van Nuys-...
2025-07-30 10:51:12,549 - src.file_handler - INFO - 写入成功记录: Roberto----Vargas----1503 Liberty Pl----Escondido-...
2025-07-30 10:51:12,550 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 10:51:22,551 - src.file_handler - INFO - 读取并删除行: Ebenezer----Samson----32095 Via Cordoba----Temecul...
2025-07-30 10:51:31,370 - src.file_handler - INFO - 写入成功记录: Anush----Harutunyan----13559 Crewe St----Van Nuys-...
2025-07-30 10:51:31,370 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 10:51:41,372 - src.file_handler - INFO - 读取并删除行: Juan----Alvarado----1550 Etna Drive----TULARE----C...
2025-07-30 10:53:22,480 - src.file_handler - INFO - 写入成功记录: Ebenezer----Samson----32095 Via Cordoba----Temecul...
2025-07-30 10:53:22,481 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 10:53:32,482 - src.file_handler - INFO - 读取并删除行: Justina----Engen----29601 Butterfield Way----Tehac...
2025-07-30 10:53:42,017 - src.file_handler - INFO - 写入成功记录: Juan----Alvarado----1550 Etna Drive----TULARE----C...
2025-07-30 10:53:42,017 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 10:53:52,019 - src.file_handler - INFO - 读取并删除行: Victoria----Kersch----10502 Borwick Street----BELL...
2025-07-30 10:55:44,858 - src.file_handler - INFO - 写入成功记录: Justina----Engen----29601 Butterfield Way----Tehac...
2025-07-30 10:55:44,859 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 10:55:54,208 - src.file_handler - INFO - 写入成功记录: Victoria----Kersch----10502 Borwick Street----BELL...
2025-07-30 10:55:54,209 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 10:55:54,860 - __main__ - INFO - 线程 1 完成所有任务，退出
2025-07-30 10:55:54,860 - __main__ - INFO - 线程 1 退出
2025-07-30 10:56:04,210 - __main__ - INFO - 线程 2 完成所有任务，退出
2025-07-30 10:56:04,211 - __main__ - INFO - 线程 2 退出
2025-07-30 10:56:04,211 - __main__ - INFO - 所有任务已完成
2025-07-30 10:56:04,211 - __main__ - INFO - 关闭索赔自动化系统...
2025-07-30 10:56:04,212 - __main__ - INFO - 系统已关闭
2025-07-30 10:57:45,838 - __main__ - INFO - 初始化索赔自动化系统...
2025-07-30 10:57:45,839 - src.bit_browser - INFO - 发送API请求: http://127.0.0.1:54345/browser/list, 数据: {'page': 0, 'pageSize': 100}
2025-07-30 10:57:46,800 - src.bit_browser - INFO - API响应: {'success': True, 'data': {'page': 0, 'pageSize': 100, 'totalNum': 5, 'list': [{'id': 'edbee0a1ad4941c58e8a563f489ac85b', 'seq': 74, 'code': '20250730072357086', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_4', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'ac1e3ce03e8edd2de76620548a9ba9beb1ca2adeae3341d051305ab29a1ec188@456bitbrowser', 'lastIp': '***********', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 10:53:40', 'closeTime': '2025-07-30 10:55:45', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '630087fbdb154160bebb2806f3156e26', 'seq': 73, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_3', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'aaba0991a0ef87d1a68670764f85927b7e75aef989b30aadbe93055a9a846218@456bitbrowser', 'lastIp': '***************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 10:54:00', 'closeTime': '2025-07-30 10:55:55', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'ae73464fb8734427a0df8a3bc7e23682', 'seq': 72, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_2', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'e19983de84b05ff79a0cca64d40780e0666119f31127edfdb872b80801fa752a@456bitbrowser', 'lastIp': '', 'lastCountry': '', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'closeTime': '2025-07-30 07:24:45', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '87042e8ab19e45e4b90a3eae6af541d1', 'seq': 71, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': '9414e8b07fd481afcbccfe3f2f48930bdd6f6f4ca919f967e910ff9f148fc5e5@456bitbrowser', 'lastIp': '', 'lastCountry': '', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '********************************', 'seq': 70, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'd482b33e9915443b11f84ce69dd22d13ff0c03c97d343c7322313330a0388cee@456bitbrowser', 'lastIp': '', 'lastCountry': '', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:46', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': True, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': None, 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}]}}
2025-07-30 10:57:46,807 - __main__ - INFO - 获取到 5 个现有浏览器
2025-07-30 10:57:46,808 - __main__ - INFO -   浏览器 1: _4 (ID: edbee0a1ad4941c58e8a563f489ac85b)
2025-07-30 10:57:46,808 - __main__ - INFO -   浏览器 2: _3 (ID: 630087fbdb154160bebb2806f3156e26)
2025-07-30 10:57:46,808 - __main__ - INFO -   浏览器 3: _2 (ID: ae73464fb8734427a0df8a3bc7e23682)
2025-07-30 10:57:46,808 - __main__ - INFO -   浏览器 4: _1 (ID: 87042e8ab19e45e4b90a3eae6af541d1)
2025-07-30 10:57:46,809 - __main__ - INFO -   浏览器 5:  (ID: ********************************)
2025-07-30 10:57:46,809 - __main__ - INFO - 系统初始化完成
2025-07-30 10:57:46,811 - __main__ - INFO - 开始索赔自动化流程...
2025-07-30 10:57:48,634 - __main__ - INFO - 待处理数据: 50 条
2025-07-30 10:57:48,634 - __main__ - INFO - 使用 4 个线程进行处理
2025-07-30 10:57:48,634 - __main__ - INFO - 邮编将自动补零到5位
2025-07-30 10:57:48,635 - __main__ - INFO - 🔥 使用真正的读一条删除一条模式
2025-07-30 10:57:48,635 - __main__ - INFO - 📋 特点: 每个线程直接从文件读取数据，读取后立即删除该行
2025-07-30 10:57:48,635 - __main__ - INFO - 线程 1 (ID: 40956) 开始工作，固定使用浏览器ID: edbee0a1ad4941c58e8a563f489ac85b
2025-07-30 10:57:48,635 - __main__ - INFO - 启动读一条删除一条线程 1，使用浏览器ID: edbee0a1ad4941c58e8a563f489ac85b
2025-07-30 10:57:48,636 - __main__ - INFO - 线程 1 使用真正的读一条删除一条模式
2025-07-30 10:57:48,637 - src.file_handler - INFO - 读取并删除行: Justin----Stamper----612 Blue Mountain Way Apt A--...
2025-07-30 10:57:50,637 - __main__ - INFO - 线程 2 (ID: 14988) 开始工作，固定使用浏览器ID: 630087fbdb154160bebb2806f3156e26
2025-07-30 10:57:50,637 - __main__ - INFO - 启动读一条删除一条线程 2，使用浏览器ID: 630087fbdb154160bebb2806f3156e26
2025-07-30 10:57:50,637 - __main__ - INFO - 线程 2 使用真正的读一条删除一条模式
2025-07-30 10:57:50,639 - src.file_handler - INFO - 读取并删除行: Nicole----Willis----2744 Wildflower Dr----ANTIOCH-...
2025-07-30 10:57:52,638 - __main__ - INFO - 线程 3 (ID: 36308) 开始工作，固定使用浏览器ID: ae73464fb8734427a0df8a3bc7e23682
2025-07-30 10:57:52,639 - __main__ - INFO - 启动读一条删除一条线程 3，使用浏览器ID: ae73464fb8734427a0df8a3bc7e23682
2025-07-30 10:57:52,639 - __main__ - INFO - 线程 3 使用真正的读一条删除一条模式
2025-07-30 10:57:52,640 - src.file_handler - INFO - 读取并删除行: Samantha----Kosta----872 Sonoma Avenue----SANTA RO...
2025-07-30 10:57:54,640 - __main__ - INFO - 线程 4 (ID: 39856) 开始工作，固定使用浏览器ID: 87042e8ab19e45e4b90a3eae6af541d1
2025-07-30 10:57:54,640 - __main__ - INFO - 启动读一条删除一条线程 4，使用浏览器ID: 87042e8ab19e45e4b90a3eae6af541d1
2025-07-30 10:57:54,641 - __main__ - INFO - 线程 4 使用真正的读一条删除一条模式
2025-07-30 10:57:54,642 - src.file_handler - INFO - 读取并删除行: Marisa----Meeks----3053 Melissa Lane----Modesto---...
2025-07-30 10:57:56,641 - __main__ - INFO - 等待所有线程完成...
2025-07-30 10:59:48,252 - src.file_handler - INFO - 写入成功记录: Justin----Stamper----612 Blue Mountain Way Apt A--...
2025-07-30 10:59:48,253 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 10:59:49,615 - src.file_handler - INFO - 写入成功记录: Nicole----Willis----2744 Wildflower Dr----ANTIOCH-...
2025-07-30 10:59:49,616 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 10:59:58,256 - src.file_handler - INFO - 读取并删除行: Evgeny----Bondarev----67 IVORY PETAL----IRVINE----...
2025-07-30 10:59:59,617 - src.file_handler - INFO - 读取并删除行: Edwin----Montealegre----43654 22nd St East----Lanc...
2025-07-30 10:59:59,822 - src.file_handler - INFO - 写入成功记录: Samantha----Kosta----872 Sonoma Avenue----SANTA RO...
2025-07-30 10:59:59,822 - __main__ - INFO - 线程 3 等待 10.0 秒...
2025-07-30 11:00:09,824 - src.file_handler - INFO - 读取并删除行: Stacey----Nelson----943 Dainty Ave----Brentwood---...
2025-07-30 11:00:12,851 - src.file_handler - INFO - 写入成功记录: Marisa----Meeks----3053 Melissa Lane----Modesto---...
2025-07-30 11:00:12,852 - __main__ - INFO - 线程 4 等待 10.0 秒...
2025-07-30 11:00:22,853 - src.file_handler - INFO - 读取并删除行: andrea----morroy----3205 L street----San Diego----...
2025-07-30 11:02:04,946 - src.file_handler - INFO - 写入成功记录: Evgeny----Bondarev----67 IVORY PETAL----IRVINE----...
2025-07-30 11:02:04,947 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 11:02:12,885 - src.file_handler - INFO - 写入成功记录: Stacey----Nelson----943 Dainty Ave----Brentwood---...
2025-07-30 11:02:12,886 - __main__ - INFO - 线程 3 等待 10.0 秒...
2025-07-30 11:02:14,949 - src.file_handler - INFO - 读取并删除行: Efrain----Espana----238 E Spencer Ave----Tipton---...
2025-07-30 11:02:22,887 - src.file_handler - INFO - 读取并删除行: MARK----ACERO----17 STEPHANIE DR APT G10----SLNS--...
2025-07-30 11:02:23,739 - src.file_handler - INFO - 写入成功记录: Edwin----Montealegre----43654 22nd St East----Lanc...
2025-07-30 11:02:23,740 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 11:02:33,532 - src.file_handler - INFO - 写入成功记录: andrea----morroy----3205 L street----San Diego----...
2025-07-30 11:02:33,533 - __main__ - INFO - 线程 4 等待 10.0 秒...
2025-07-30 11:02:33,742 - src.file_handler - INFO - 读取并删除行: TEK----CHEW----40686 CALIENTE----FMT----CA----9453...
2025-07-30 11:02:43,536 - src.file_handler - INFO - 读取并删除行: Carrie----Woodfork----14730 old westside rd----GRE...
2025-07-30 11:04:44,489 - src.file_handler - INFO - 写入成功记录: Efrain----Espana----238 E Spencer Ave----Tipton---...
2025-07-30 11:04:44,489 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 11:04:53,514 - src.file_handler - INFO - 写入成功记录: MARK----ACERO----17 STEPHANIE DR APT G10----SLNS--...
2025-07-30 11:04:53,515 - __main__ - INFO - 线程 3 等待 10.0 秒...
2025-07-30 11:04:54,491 - src.file_handler - INFO - 读取并删除行: Hana----Afemata----1832 baywood Dr apt 101----CORO...
2025-07-30 11:04:58,709 - src.file_handler - INFO - 写入成功记录: TEK----CHEW----40686 CALIENTE----FMT----CA----9453...
2025-07-30 11:04:58,710 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 11:05:03,517 - src.file_handler - INFO - 读取并删除行: carl----hanson----2576 stanford ave----SOUTH LAKE ...
2025-07-30 11:05:08,712 - src.file_handler - INFO - 读取并删除行: Ariann----Costa----179 S Sycamore Ave----Los Angel...
2025-07-30 11:05:52,680 - src.file_handler - INFO - 写入成功记录: Carrie----Woodfork----14730 old westside rd----GRE...
2025-07-30 11:05:52,680 - __main__ - INFO - 线程 4 等待 10.0 秒...
2025-07-30 11:06:02,682 - src.file_handler - INFO - 读取并删除行: Heather----Todish----1016 W FOUNTAIN WAY----Fresno...
2025-07-30 11:06:54,301 - src.file_handler - INFO - 写入成功记录: Hana----Afemata----1832 baywood Dr apt 101----CORO...
2025-07-30 11:06:54,301 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 11:07:02,542 - src.file_handler - INFO - 写入成功记录: carl----hanson----2576 stanford ave----SOUTH LAKE ...
2025-07-30 11:07:02,543 - __main__ - INFO - 线程 3 等待 10.0 秒...
2025-07-30 11:07:04,303 - src.file_handler - INFO - 读取并删除行: Eligio----Orona----5043 trmpletion st apt 4----LOS...
2025-07-30 11:07:12,544 - src.file_handler - INFO - 读取并删除行: Leila----Amirsadeghi----1453 1/2 S Fairfax Ave----...
2025-07-30 11:08:48,923 - src.file_handler - INFO - 写入成功记录: Ariann----Costa----179 S Sycamore Ave----Los Angel...
2025-07-30 11:08:48,924 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 11:08:58,926 - src.file_handler - INFO - 读取并删除行: Lloyd----Balbier----309 S Doheny Dr----Beverly Hil...
2025-07-30 11:09:14,903 - src.file_handler - INFO - 写入成功记录: Eligio----Orona----5043 trmpletion st apt 4----LOS...
2025-07-30 11:09:14,904 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 11:09:16,662 - src.file_handler - INFO - 写入成功记录: Heather----Todish----1016 W FOUNTAIN WAY----Fresno...
2025-07-30 11:09:16,662 - __main__ - INFO - 线程 4 等待 10.0 秒...
2025-07-30 11:09:20,742 - src.file_handler - INFO - 写入成功记录: Leila----Amirsadeghi----1453 1/2 S Fairfax Ave----...
2025-07-30 11:09:20,742 - __main__ - INFO - 线程 3 等待 10.0 秒...
2025-07-30 11:09:24,906 - src.file_handler - INFO - 读取并删除行: Kaleena----Schaper----10244 Arrow Rte Apt 85----Ra...
2025-07-30 11:09:26,664 - src.file_handler - INFO - 读取并删除行: Mileidys----Gonzalez----8525 Paramount Blvd Apt 12...
2025-07-30 11:09:30,744 - src.file_handler - INFO - 读取并删除行: Ryan----Lee----2015 E 22nd St----Oakland----CA----...
2025-07-30 13:56:38,922 - __main__ - INFO - 初始化索赔自动化系统...
2025-07-30 13:56:38,922 - src.bit_browser - INFO - 发送API请求: http://127.0.0.1:54345/browser/list, 数据: {'page': 0, 'pageSize': 100}
2025-07-30 13:56:45,851 - src.bit_browser - INFO - API响应: {'success': True, 'data': {'page': 0, 'pageSize': 100, 'totalNum': 5, 'list': [{'id': 'edbee0a1ad4941c58e8a563f489ac85b', 'seq': 74, 'code': '20250730072357086', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_4', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'ac1e3ce03e8edd2de76620548a9ba9beb1ca2adeae3341d051305ab29a1ec188@456bitbrowser', 'lastIp': '***************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 11:09:32', 'closeTime': '2025-07-30 11:09:59', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '630087fbdb154160bebb2806f3156e26', 'seq': 73, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_3', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'aaba0991a0ef87d1a68670764f85927b7e75aef989b30aadbe93055a9a846218@456bitbrowser', 'lastIp': '***************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 11:09:06', 'closeTime': '2025-07-30 11:10:00', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'ae73464fb8734427a0df8a3bc7e23682', 'seq': 72, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_2', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'e19983de84b05ff79a0cca64d40780e0666119f31127edfdb872b80801fa752a@456bitbrowser', 'lastIp': '*************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 11:09:38', 'closeTime': '2025-07-30 11:10:01', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '87042e8ab19e45e4b90a3eae6af541d1', 'seq': 71, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': '9414e8b07fd481afcbccfe3f2f48930bdd6f6f4ca919f967e910ff9f148fc5e5@456bitbrowser', 'lastIp': '*************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 11:09:34', 'closeTime': '2025-07-30 11:10:02', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '********************************', 'seq': 70, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'd482b33e9915443b11f84ce69dd22d13ff0c03c97d343c7322313330a0388cee@456bitbrowser', 'lastIp': '', 'lastCountry': '', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:46', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': True, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': None, 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}]}}
2025-07-30 13:56:45,858 - __main__ - INFO - 获取到 5 个现有浏览器
2025-07-30 13:56:45,859 - __main__ - INFO -   浏览器 1: _4 (ID: edbee0a1ad4941c58e8a563f489ac85b)
2025-07-30 13:56:45,859 - __main__ - INFO -   浏览器 2: _3 (ID: 630087fbdb154160bebb2806f3156e26)
2025-07-30 13:56:45,859 - __main__ - INFO -   浏览器 3: _2 (ID: ae73464fb8734427a0df8a3bc7e23682)
2025-07-30 13:56:45,859 - __main__ - INFO -   浏览器 4: _1 (ID: 87042e8ab19e45e4b90a3eae6af541d1)
2025-07-30 13:56:45,860 - __main__ - INFO -   浏览器 5:  (ID: ********************************)
2025-07-30 13:56:45,860 - __main__ - INFO - 系统初始化完成
2025-07-30 13:56:45,863 - __main__ - INFO - 开始索赔自动化流程...
2025-07-30 13:56:47,804 - __main__ - INFO - 待处理数据: 28 条
2025-07-30 13:56:47,804 - __main__ - INFO - 使用 5 个线程进行处理
2025-07-30 13:56:47,804 - __main__ - INFO - 邮编将自动补零到5位
2025-07-30 13:56:47,805 - __main__ - INFO - 🔥 使用真正的读一条删除一条模式
2025-07-30 13:56:47,805 - __main__ - INFO - 📋 特点: 每个线程直接从文件读取数据，读取后立即删除该行
2025-07-30 13:56:47,806 - __main__ - INFO - 启动读一条删除一条线程 1，使用浏览器ID: edbee0a1ad4941c58e8a563f489ac85b
2025-07-30 13:56:47,842 - __main__ - INFO - 线程 1 (ID: 39668) 开始工作，固定使用浏览器ID: edbee0a1ad4941c58e8a563f489ac85b
2025-07-30 13:56:47,842 - __main__ - INFO - 线程 1 使用真正的读一条删除一条模式
2025-07-30 13:56:47,852 - src.file_handler - INFO - 读取并删除行: Mike----Murrieta----1432 N Willow Ave----RIALTO---...
2025-07-30 13:56:49,806 - __main__ - INFO - 线程 2 (ID: 37972) 开始工作，固定使用浏览器ID: 630087fbdb154160bebb2806f3156e26
2025-07-30 13:56:49,807 - __main__ - INFO - 启动读一条删除一条线程 2，使用浏览器ID: 630087fbdb154160bebb2806f3156e26
2025-07-30 13:56:49,807 - __main__ - INFO - 线程 2 使用真正的读一条删除一条模式
2025-07-30 13:56:49,808 - src.file_handler - INFO - 读取并删除行: Bella----Orion----93 Sun Leon ----Irvine----CA----...
2025-07-30 13:56:51,808 - __main__ - INFO - 线程 3 (ID: 32540) 开始工作，固定使用浏览器ID: ae73464fb8734427a0df8a3bc7e23682
2025-07-30 13:56:51,808 - __main__ - INFO - 启动读一条删除一条线程 3，使用浏览器ID: ae73464fb8734427a0df8a3bc7e23682
2025-07-30 13:56:51,808 - __main__ - INFO - 线程 3 使用真正的读一条删除一条模式
2025-07-30 13:56:51,810 - src.file_handler - INFO - 读取并删除行: Shane----Dustin----3251 Kearny Villa Lane ----San ...
2025-07-30 13:56:53,809 - __main__ - INFO - 线程 4 (ID: 32392) 开始工作，固定使用浏览器ID: 87042e8ab19e45e4b90a3eae6af541d1
2025-07-30 13:56:53,810 - __main__ - INFO - 启动读一条删除一条线程 4，使用浏览器ID: 87042e8ab19e45e4b90a3eae6af541d1
2025-07-30 13:56:53,810 - __main__ - INFO - 线程 4 使用真正的读一条删除一条模式
2025-07-30 13:56:53,812 - src.file_handler - INFO - 读取并删除行: JOHN----NGUYEN----6311 PACIFIC BLVD----Huntington ...
2025-07-30 13:56:55,811 - __main__ - INFO - 线程 5 (ID: 40260) 开始工作，固定使用浏览器ID: ********************************
2025-07-30 13:56:55,811 - __main__ - INFO - 启动读一条删除一条线程 5，使用浏览器ID: ********************************
2025-07-30 13:56:55,811 - __main__ - INFO - 线程 5 使用真正的读一条删除一条模式
2025-07-30 13:56:55,813 - src.file_handler - INFO - 读取并删除行: Hugo----Suastegui----5434 Sierra Vista Ave Apt 30-...
2025-07-30 13:56:57,813 - __main__ - INFO - 等待所有线程完成...
2025-07-30 13:58:49,391 - src.file_handler - INFO - 写入成功记录: JOHN----NGUYEN----6311 PACIFIC BLVD----Huntington ...
2025-07-30 13:58:49,391 - __main__ - INFO - 线程 4 等待 10.0 秒...
2025-07-30 13:58:50,343 - src.file_handler - INFO - 写入成功记录: Mike----Murrieta----1432 N Willow Ave----RIALTO---...
2025-07-30 13:58:50,344 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 13:58:50,710 - src.file_handler - INFO - 写入成功记录: Shane----Dustin----3251 Kearny Villa Lane ----San ...
2025-07-30 13:58:50,711 - __main__ - INFO - 线程 3 等待 10.0 秒...
2025-07-30 13:58:50,741 - src.file_handler - INFO - 写入成功记录: Bella----Orion----93 Sun Leon ----Irvine----CA----...
2025-07-30 13:58:50,741 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 13:58:59,393 - src.file_handler - INFO - 读取并删除行: Carlos----Villalvazo----1111 Chapman st----SAN JOS...
2025-07-30 13:59:00,346 - src.file_handler - INFO - 读取并删除行: Ethan----Taylor----701 Vallejo way----Montclair---...
2025-07-30 13:59:00,713 - src.file_handler - INFO - 读取并删除行: Ryan----Morin----33586 Emerson way unit a----Temec...
2025-07-30 13:59:00,744 - src.file_handler - INFO - 读取并删除行: Rene----Reyes----21420 Christina Court----Riverdal...
2025-07-30 13:59:14,013 - src.file_handler - INFO - 写入成功记录: Hugo----Suastegui----5434 Sierra Vista Ave Apt 30-...
2025-07-30 13:59:14,013 - __main__ - INFO - 线程 5 等待 10.0 秒...
2025-07-30 13:59:24,015 - src.file_handler - INFO - 读取并删除行: Bertha----Aguirre----1120 Arno Dr----Sierra Madre-...
2025-07-30 14:00:47,254 - src.file_handler - INFO - 写入成功记录: Carlos----Villalvazo----1111 Chapman st----SAN JOS...
2025-07-30 14:00:47,255 - __main__ - INFO - 线程 4 等待 10.0 秒...
2025-07-30 14:00:47,729 - src.file_handler - INFO - 写入成功记录: Ryan----Morin----33586 Emerson way unit a----Temec...
2025-07-30 14:00:47,730 - __main__ - INFO - 线程 3 等待 10.0 秒...
2025-07-30 14:00:48,909 - src.file_handler - INFO - 写入成功记录: Ethan----Taylor----701 Vallejo way----Montclair---...
2025-07-30 14:00:48,910 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 14:00:51,146 - src.file_handler - INFO - 写入成功记录: Rene----Reyes----21420 Christina Court----Riverdal...
2025-07-30 14:00:51,147 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 14:00:57,257 - src.file_handler - INFO - 读取并删除行: Osvaldo----Gomez----5518 Summer Villa----BAKERSFIE...
2025-07-30 14:00:57,732 - src.file_handler - INFO - 读取并删除行: David----Sprague----155 Roe Rd----Paradise----CA--...
2025-07-30 14:00:58,912 - src.file_handler - INFO - 读取并删除行: Lily----Liu----43150 Calle Familia----Fremont----C...
2025-07-30 14:01:01,148 - src.file_handler - INFO - 读取并删除行: Luis----Villatoro----608 Woolrich Bay Drive----Bak...
2025-07-30 14:01:11,266 - src.file_handler - INFO - 写入成功记录: Bertha----Aguirre----1120 Arno Dr----Sierra Madre-...
2025-07-30 14:01:11,267 - __main__ - INFO - 线程 5 等待 10.0 秒...
2025-07-30 14:01:21,269 - src.file_handler - INFO - 读取并删除行: Fennah----Menrige----162 Santa Paula Dr Daly City ...
2025-07-30 14:02:44,139 - src.file_handler - INFO - 写入成功记录: David----Sprague----155 Roe Rd----Paradise----CA--...
2025-07-30 14:02:44,140 - __main__ - INFO - 线程 3 等待 10.0 秒...
2025-07-30 14:02:46,983 - src.file_handler - INFO - 写入成功记录: Osvaldo----Gomez----5518 Summer Villa----BAKERSFIE...
2025-07-30 14:02:46,984 - __main__ - INFO - 线程 4 等待 10.0 秒...
2025-07-30 14:02:50,766 - src.file_handler - INFO - 写入成功记录: Lily----Liu----43150 Calle Familia----Fremont----C...
2025-07-30 14:02:50,767 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 14:02:50,770 - src.file_handler - INFO - 写入成功记录: Luis----Villatoro----608 Woolrich Bay Drive----Bak...
2025-07-30 14:02:50,771 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 14:02:54,141 - src.file_handler - INFO - 读取并删除行: Cheri----Borrelli----1820 N Daubenberger rd----Tur...
2025-07-30 14:02:56,986 - src.file_handler - INFO - 读取并删除行: Daniel----Masip----5636 E homecoming Cir #H----eas...
2025-07-30 14:03:00,769 - src.file_handler - INFO - 读取并删除行: Douglas----England----8615 Hwy 99E----LOS MOLINOS-...
2025-07-30 14:03:00,773 - src.file_handler - INFO - 读取并删除行: Eva----Nava----25610 Spring St ----Perris----CA---...
2025-07-30 14:03:20,955 - src.file_handler - INFO - 写入成功记录: Fennah----Menrige----162 Santa Paula Dr Daly City ...
2025-07-30 14:03:20,956 - __main__ - INFO - 线程 5 等待 10.0 秒...
2025-07-30 14:03:30,958 - src.file_handler - INFO - 读取并删除行: Mario----Perez----380 N Linden Ave----RIALTO----CA...
2025-07-30 14:04:43,263 - src.file_handler - INFO - 写入成功记录: Cheri----Borrelli----1820 N Daubenberger rd----Tur...
2025-07-30 14:04:43,264 - __main__ - INFO - 线程 3 等待 10.0 秒...
2025-07-30 14:04:46,232 - src.file_handler - INFO - 写入成功记录: Daniel----Masip----5636 E homecoming Cir #H----eas...
2025-07-30 14:04:46,233 - __main__ - INFO - 线程 4 等待 10.0 秒...
2025-07-30 14:04:47,856 - src.file_handler - INFO - 写入成功记录: Eva----Nava----25610 Spring St ----Perris----CA---...
2025-07-30 14:04:47,856 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 14:04:51,667 - src.file_handler - INFO - 写入成功记录: Douglas----England----8615 Hwy 99E----LOS MOLINOS-...
2025-07-30 14:04:51,668 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 14:04:53,266 - src.file_handler - INFO - 读取并删除行: Cinthia----Santillan----13825 Brynwood St ----Vict...
2025-07-30 14:04:56,235 - src.file_handler - INFO - 读取并删除行: Ernest----McCommick----2126 W Lark Avenue----VISAL...
2025-07-30 14:04:57,858 - src.file_handler - INFO - 读取并删除行: Ali----Fathi----12158 Rancho Bernardo Rd Unit B---...
2025-07-30 14:05:01,670 - src.file_handler - INFO - 读取并删除行: Brian----Swartzbaugh----529 Park Way----South San ...
2025-07-30 14:05:47,822 - src.file_handler - INFO - 写入失败记录: Mario----Perez----380 N Linden Ave----RIALTO----CA...
2025-07-30 14:05:47,823 - __main__ - ERROR - 线程 5 任务失败: 填表任务失败
2025-07-30 14:05:47,823 - __main__ - INFO - 线程 5 等待 10.0 秒...
2025-07-30 14:05:57,825 - src.file_handler - INFO - 读取并删除行: Alejandra----Lopez----1000 Valintine Ct----NAPA---...
2025-07-30 14:06:43,327 - src.file_handler - INFO - 写入成功记录: Cinthia----Santillan----13825 Brynwood St ----Vict...
2025-07-30 14:06:43,327 - __main__ - INFO - 线程 3 等待 10.0 秒...
2025-07-30 14:06:47,237 - src.file_handler - INFO - 写入成功记录: Ali----Fathi----12158 Rancho Bernardo Rd Unit B---...
2025-07-30 14:06:47,238 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 14:06:48,001 - src.file_handler - INFO - 写入成功记录: Ernest----McCommick----2126 W Lark Avenue----VISAL...
2025-07-30 14:06:48,001 - __main__ - INFO - 线程 4 等待 10.0 秒...
2025-07-30 14:06:50,349 - src.file_handler - INFO - 写入成功记录: Brian----Swartzbaugh----529 Park Way----South San ...
2025-07-30 14:06:50,350 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 14:06:53,329 - src.file_handler - INFO - 读取并删除行: Miguel----Morenocisneros----9908 Lenore St----Lamo...
2025-07-30 14:06:57,240 - src.file_handler - INFO - 读取并删除行: ELVIRA----MENDOZA----654 LOYOLA AVE----CARSON----C...
2025-07-30 14:06:58,003 - src.file_handler - INFO - 读取并删除行: Petra----Santibanez----1600 E. 17th St----Santa An...
2025-07-30 14:07:00,351 - __main__ - INFO - 线程 1 完成所有任务，退出
2025-07-30 14:07:00,351 - __main__ - INFO - 线程 1 退出
2025-07-30 14:07:43,103 - src.file_handler - INFO - 写入失败记录: Alejandra----Lopez----1000 Valintine Ct----NAPA---...
2025-07-30 14:07:43,104 - __main__ - ERROR - 线程 5 任务失败: 填表任务失败
2025-07-30 14:07:43,104 - __main__ - INFO - 线程 5 等待 10.0 秒...
2025-07-30 14:07:53,106 - __main__ - INFO - 线程 5 完成所有任务，退出
2025-07-30 14:07:53,106 - __main__ - INFO - 线程 5 退出
2025-07-30 14:08:39,748 - src.file_handler - INFO - 写入成功记录: Miguel----Morenocisneros----9908 Lenore St----Lamo...
2025-07-30 14:08:39,748 - __main__ - INFO - 线程 3 等待 10.0 秒...
2025-07-30 14:08:43,171 - src.file_handler - INFO - 写入成功记录: ELVIRA----MENDOZA----654 LOYOLA AVE----CARSON----C...
2025-07-30 14:08:43,172 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 14:08:49,582 - src.file_handler - INFO - 写入成功记录: Petra----Santibanez----1600 E. 17th St----Santa An...
2025-07-30 14:08:49,583 - __main__ - INFO - 线程 4 等待 10.0 秒...
2025-07-30 14:08:49,750 - __main__ - INFO - 线程 3 完成所有任务，退出
2025-07-30 14:08:49,750 - __main__ - INFO - 线程 3 退出
2025-07-30 14:08:53,174 - __main__ - INFO - 线程 2 完成所有任务，退出
2025-07-30 14:08:53,174 - __main__ - INFO - 线程 2 退出
2025-07-30 14:08:59,585 - __main__ - INFO - 线程 4 完成所有任务，退出
2025-07-30 14:08:59,585 - __main__ - INFO - 线程 4 退出
2025-07-30 14:08:59,585 - __main__ - INFO - 所有任务已完成
2025-07-30 14:08:59,586 - __main__ - INFO - 关闭索赔自动化系统...
2025-07-30 14:08:59,586 - __main__ - INFO - 系统已关闭
2025-07-30 14:50:43,850 - __main__ - INFO - 初始化索赔自动化系统...
2025-07-30 14:50:43,851 - src.bit_browser - INFO - 发送API请求: http://127.0.0.1:54345/browser/list, 数据: {'page': 0, 'pageSize': 100}
2025-07-30 14:50:44,996 - src.bit_browser - INFO - API响应: {'success': True, 'data': {'page': 0, 'pageSize': 100, 'totalNum': 5, 'list': [{'id': 'edbee0a1ad4941c58e8a563f489ac85b', 'seq': 74, 'code': '20250730072357086', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_4', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'ac1e3ce03e8edd2de76620548a9ba9beb1ca2adeae3341d051305ab29a1ec188@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 14:05:10', 'closeTime': '2025-07-30 14:06:54', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '630087fbdb154160bebb2806f3156e26', 'seq': 73, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_3', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'aaba0991a0ef87d1a68670764f85927b7e75aef989b30aadbe93055a9a846218@456bitbrowser', 'lastIp': '***********', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 14:07:05', 'closeTime': '2025-07-30 14:08:44', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'ae73464fb8734427a0df8a3bc7e23682', 'seq': 72, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_2', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'e19983de84b05ff79a0cca64d40780e0666119f31127edfdb872b80801fa752a@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 14:07:01', 'closeTime': '2025-07-30 14:08:40', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '87042e8ab19e45e4b90a3eae6af541d1', 'seq': 71, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': '9414e8b07fd481afcbccfe3f2f48930bdd6f6f4ca919f967e910ff9f148fc5e5@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 14:07:06', 'closeTime': '2025-07-30 14:08:50', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '********************************', 'seq': 70, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'd482b33e9915443b11f84ce69dd22d13ff0c03c97d343c7322313330a0388cee@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 14:06:05', 'closeTime': '2025-07-30 14:07:44', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:46', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': True, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': None, 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}]}}
2025-07-30 14:50:45,001 - __main__ - INFO - 获取到 5 个现有浏览器
2025-07-30 14:50:45,002 - __main__ - INFO -   浏览器 1: _4 (ID: edbee0a1ad4941c58e8a563f489ac85b)
2025-07-30 14:50:45,002 - __main__ - INFO -   浏览器 2: _3 (ID: 630087fbdb154160bebb2806f3156e26)
2025-07-30 14:50:45,002 - __main__ - INFO -   浏览器 3: _2 (ID: ae73464fb8734427a0df8a3bc7e23682)
2025-07-30 14:50:45,002 - __main__ - INFO -   浏览器 4: _1 (ID: 87042e8ab19e45e4b90a3eae6af541d1)
2025-07-30 14:50:45,002 - __main__ - INFO -   浏览器 5:  (ID: ********************************)
2025-07-30 14:50:45,003 - __main__ - INFO - 系统初始化完成
2025-07-30 14:50:45,005 - __main__ - INFO - 关闭索赔自动化系统...
2025-07-30 14:50:45,005 - __main__ - INFO - 系统已关闭
2025-07-30 14:51:25,630 - __main__ - INFO - 初始化索赔自动化系统...
2025-07-30 14:51:25,630 - src.bit_browser - INFO - 发送API请求: http://127.0.0.1:54345/browser/list, 数据: {'page': 0, 'pageSize': 100}
2025-07-30 14:51:26,591 - src.bit_browser - INFO - API响应: {'success': True, 'data': {'page': 0, 'pageSize': 100, 'totalNum': 5, 'list': [{'id': 'edbee0a1ad4941c58e8a563f489ac85b', 'seq': 74, 'code': '20250730072357086', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_4', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'ac1e3ce03e8edd2de76620548a9ba9beb1ca2adeae3341d051305ab29a1ec188@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 14:05:10', 'closeTime': '2025-07-30 14:06:54', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '630087fbdb154160bebb2806f3156e26', 'seq': 73, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_3', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'aaba0991a0ef87d1a68670764f85927b7e75aef989b30aadbe93055a9a846218@456bitbrowser', 'lastIp': '***********', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 14:07:05', 'closeTime': '2025-07-30 14:08:44', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': 'ae73464fb8734427a0df8a3bc7e23682', 'seq': 72, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_2', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'e19983de84b05ff79a0cca64d40780e0666119f31127edfdb872b80801fa752a@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 14:07:01', 'closeTime': '2025-07-30 14:08:40', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '87042e8ab19e45e4b90a3eae6af541d1', 'seq': 71, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '_1', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': '9414e8b07fd481afcbccfe3f2f48930bdd6f6f4ca919f967e910ff9f148fc5e5@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 14:07:06', 'closeTime': '2025-07-30 14:08:50', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:57', 'updateBy': '4028808b97d9a95e0197dea36c2f78d0', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': False, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': 'anyubswork', 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}, {'id': '********************************', 'seq': 70, 'code': '*****************', 'groupId': '4028808b97d9a95e0197dea36c9178d2', 'platform': '', 'platformIcon': '', 'url': '', 'name': '', 'userName': '', 'password': '', 'cookie': '', 'otherCookie': '', 'isGlobalProxyInfo': False, 'isIpv6': False, 'proxyMethod': 2, 'proxyType': 'socks5', 'agentId': '', 'ipCheckService': 'IP2Location', 'host': 'na.lunaproxy.com', 'port': 32233, 'proxyUserName': 'user-anyubs_E4wrL-region-us', 'proxyPassword': 'd482b33e9915443b11f84ce69dd22d13ff0c03c97d343c7322313330a0388cee@456bitbrowser', 'lastIp': '**************', 'lastCountry': '美国(US)', 'isIpNoChange': False, 'ip': '', 'country': '', 'province': '', 'city': '', 'dynamicIpChannel': '', 'dynamicIpUrl': '', 'isDynamicIpChangeIp': True, 'remark': '', 'status': 0, 'operUserName': '', 'operTime': '2025-07-30 14:06:05', 'closeTime': '2025-07-30 14:07:44', 'isDelete': 0, 'delReason': '', 'isMostCommon': 0, 'isRemove': 0, 'tempStr': None, 'createdBy': '4028808b97d9a95e0197dea36c2f78d0', 'userId': '4028808b97d9a95e0197dea36c2f78d0', 'createdTime': '2025-07-30 07:23:46', 'recycleBinRemark': '', 'mainUserId': '2c9bc0478ad5c5c2018ad5fd66da28d7', 'abortImage': False, 'abortMedia': False, 'stopWhileNetError': False, 'stopWhileCountryChange': False, 'syncTabs': False, 'syncCookies': False, 'syncIndexedDb': False, 'syncBookmarks': False, 'syncAuthorization': False, 'syncHistory': False, 'syncGoogleAccount': True, 'allowedSignin': False, 'syncSessions': True, 'workbench': 'localserver', 'clearCacheFilesBeforeLaunch': False, 'clearCookiesBeforeLaunch': True, 'clearHistoriesBeforeLaunch': True, 'randomFingerprint': True, 'muteAudio': False, 'disableGpu': False, 'enableBackgroundMode': False, 'abortImageMaxSize': None, 'syncExtensions': False, 'syncUserExtensions': False, 'syncLocalStorage': False, 'credentialsEnableService': False, 'disableTranslatePopup': False, 'stopWhileIpChange': False, 'disableClipboard': False, 'disableNotifications': False, 'memorySaver': False, 'groupName': 'anyubswork-默认分组', 'createdName': 'anyubswork', 'belongUserName': None, 'updateName': None, 'agentIpCount': None, 'belongToMe': True, 'seqExport': None, 'groupIDs': None, 'browserShareID': None, 'share': None, 'shareUserName': None, 'isShare': 0, 'isValidUsername': True, 'createNum': 0, 'isRandomFinger': True, 'remarkType': 1, 'refreshProxyUrl': None, 'duplicateCheck': 0, 'ossExtend': None, 'randomKey': None, 'randomKeyUser': None, 'syncBrowserAccount': None, 'cookieBak': '', 'passwordBak': None, 'manual': 0, 'proxyPasswordBak': None, 'proxyAgreementType': None, 'clearCacheWithoutExtensions': False, 'syncPaymentsAndAddress': False, 'extendIds': [], 'isSynOpen': 1, 'faSecretKey': None, 'coreProduct': 'chrome', 'ostype': 'PC', 'os': 'Win32', 'coreVersion': '134', 'sort': 0, 'checkPassword': None}]}}
2025-07-30 14:51:26,597 - __main__ - INFO - 获取到 5 个现有浏览器
2025-07-30 14:51:26,597 - __main__ - INFO -   浏览器 1: _4 (ID: edbee0a1ad4941c58e8a563f489ac85b)
2025-07-30 14:51:26,598 - __main__ - INFO -   浏览器 2: _3 (ID: 630087fbdb154160bebb2806f3156e26)
2025-07-30 14:51:26,598 - __main__ - INFO -   浏览器 3: _2 (ID: ae73464fb8734427a0df8a3bc7e23682)
2025-07-30 14:51:26,598 - __main__ - INFO -   浏览器 4: _1 (ID: 87042e8ab19e45e4b90a3eae6af541d1)
2025-07-30 14:51:26,598 - __main__ - INFO -   浏览器 5:  (ID: ********************************)
2025-07-30 14:51:26,599 - __main__ - INFO - 系统初始化完成
2025-07-30 14:51:26,601 - __main__ - INFO - 开始索赔自动化流程...
2025-07-30 14:51:31,213 - __main__ - INFO - 待处理数据: 5 条
2025-07-30 14:51:31,213 - __main__ - INFO - 使用 5 个线程进行处理
2025-07-30 14:51:31,213 - __main__ - INFO - 邮编将自动补零到5位
2025-07-30 14:51:31,214 - __main__ - INFO - 🔥 使用真正的读一条删除一条模式
2025-07-30 14:51:31,214 - __main__ - INFO - 📋 特点: 每个线程直接从文件读取数据，读取后立即删除该行
2025-07-30 14:51:31,214 - __main__ - INFO - 启动读一条删除一条线程 1，使用浏览器ID: edbee0a1ad4941c58e8a563f489ac85b
2025-07-30 14:51:31,251 - __main__ - INFO - 线程 1 (ID: 31408) 开始工作，固定使用浏览器ID: edbee0a1ad4941c58e8a563f489ac85b
2025-07-30 14:51:31,251 - __main__ - INFO - 线程 1 使用真正的读一条删除一条模式
2025-07-30 14:51:31,257 - src.file_handler - INFO - 读取并删除行: Patrick----Fuentes----15258 Ramona blvd----Baldwin...
2025-07-30 14:51:33,216 - __main__ - INFO - 线程 2 (ID: 12420) 开始工作，固定使用浏览器ID: 630087fbdb154160bebb2806f3156e26
2025-07-30 14:51:33,216 - __main__ - INFO - 启动读一条删除一条线程 2，使用浏览器ID: 630087fbdb154160bebb2806f3156e26
2025-07-30 14:51:33,216 - __main__ - INFO - 线程 2 使用真正的读一条删除一条模式
2025-07-30 14:51:33,218 - src.file_handler - INFO - 读取并删除行: Alexey----Mustafin----33 Lodato Ave APT 14----SAN ...
2025-07-30 14:51:35,217 - __main__ - INFO - 线程 3 (ID: 30800) 开始工作，固定使用浏览器ID: ae73464fb8734427a0df8a3bc7e23682
2025-07-30 14:51:35,217 - __main__ - INFO - 启动读一条删除一条线程 3，使用浏览器ID: ae73464fb8734427a0df8a3bc7e23682
2025-07-30 14:51:35,218 - __main__ - INFO - 线程 3 使用真正的读一条删除一条模式
2025-07-30 14:51:35,219 - src.file_handler - INFO - 读取并删除行: Alfredo----Franco----827 w 10th ave----Escondido--...
2025-07-30 14:51:37,219 - __main__ - INFO - 线程 4 (ID: 21980) 开始工作，固定使用浏览器ID: 87042e8ab19e45e4b90a3eae6af541d1
2025-07-30 14:51:37,219 - __main__ - INFO - 启动读一条删除一条线程 4，使用浏览器ID: 87042e8ab19e45e4b90a3eae6af541d1
2025-07-30 14:51:37,220 - __main__ - INFO - 线程 4 使用真正的读一条删除一条模式
2025-07-30 14:51:37,221 - src.file_handler - INFO - 读取并删除行: Jonathan----Guzman----497 Sudan Ave----Buttonwillo...
2025-07-30 14:51:39,221 - __main__ - INFO - 线程 5 (ID: 5036) 开始工作，固定使用浏览器ID: ********************************
2025-07-30 14:51:39,221 - __main__ - INFO - 启动读一条删除一条线程 5，使用浏览器ID: ********************************
2025-07-30 14:51:39,221 - __main__ - INFO - 线程 5 使用真正的读一条删除一条模式
2025-07-30 14:51:39,222 - src.file_handler - INFO - 读取并删除行: SILVIA----OLSON----38720 32ND ST E----PALMDALE----...
2025-07-30 14:51:41,222 - __main__ - INFO - 等待所有线程完成...
2025-07-30 14:53:33,456 - src.file_handler - INFO - 写入成功记录: Alfredo----Franco----827 w 10th ave----Escondido--...
2025-07-30 14:53:33,456 - __main__ - INFO - 线程 3 等待 10.0 秒...
2025-07-30 14:53:33,651 - src.file_handler - INFO - 写入成功记录: Alexey----Mustafin----33 Lodato Ave APT 14----SAN ...
2025-07-30 14:53:33,651 - __main__ - INFO - 线程 2 等待 10.0 秒...
2025-07-30 14:53:35,200 - src.file_handler - INFO - 写入成功记录: Patrick----Fuentes----15258 Ramona blvd----Baldwin...
2025-07-30 14:53:35,201 - __main__ - INFO - 线程 1 等待 10.0 秒...
2025-07-30 14:53:36,247 - src.file_handler - INFO - 写入成功记录: Jonathan----Guzman----497 Sudan Ave----Buttonwillo...
2025-07-30 14:53:36,247 - __main__ - INFO - 线程 4 等待 10.0 秒...
2025-07-30 14:53:43,457 - __main__ - INFO - 线程 3 完成所有任务，退出
2025-07-30 14:53:43,457 - __main__ - INFO - 线程 3 退出
2025-07-30 14:53:43,652 - __main__ - INFO - 线程 2 完成所有任务，退出
2025-07-30 14:53:43,652 - __main__ - INFO - 线程 2 退出
2025-07-30 14:53:45,202 - __main__ - INFO - 线程 1 完成所有任务，退出
2025-07-30 14:53:45,202 - __main__ - INFO - 线程 1 退出
2025-07-30 14:53:46,248 - __main__ - INFO - 线程 4 完成所有任务，退出
2025-07-30 14:53:46,249 - __main__ - INFO - 线程 4 退出
2025-07-30 14:54:07,188 - src.file_handler - INFO - 写入失败记录: SILVIA----OLSON----38720 32ND ST E----PALMDALE----...
2025-07-30 14:54:07,188 - __main__ - ERROR - 线程 5 任务失败: 填表任务失败
2025-07-30 14:54:07,188 - __main__ - INFO - 线程 5 等待 10.0 秒...
2025-07-30 14:54:17,190 - __main__ - INFO - 线程 5 完成所有任务，退出
2025-07-30 14:54:17,190 - __main__ - INFO - 线程 5 退出
2025-07-30 14:54:17,191 - __main__ - INFO - 所有任务已完成
2025-07-30 14:54:17,191 - __main__ - INFO - 关闭索赔自动化系统...
2025-07-30 14:54:17,191 - __main__ - INFO - 系统已关闭
