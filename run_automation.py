#!/usr/bin/env python3
"""
自动运行索赔自动化系统的脚本
"""

import subprocess
import sys
import time

def run_automation():
    """运行自动化索赔系统"""
    try:
        print("启动索赔自动化系统...")
        print("自动选择: 运行自动化索赔")
        print("自动选择: 有证据模式")
        
        # 准备输入：选择2（运行自动化索赔），然后选择2（有证据模式），然后回车（默认1个线程），最后选择5（退出）
        inputs = "2\n2\n\n5\n"
        
        # 启动主程序
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 发送输入
        stdout, _ = process.communicate(input=inputs)
        
        # 显示输出
        print("=" * 60)
        print("程序输出:")
        print("=" * 60)
        print(stdout)
        
        return process.returncode == 0
        
    except Exception as e:
        print(f"运行失败: {e}")
        return False

if __name__ == "__main__":
    success = run_automation()
    if success:
        print("程序执行完成")
    else:
        print("程序执行失败")
