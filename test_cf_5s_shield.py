#!/usr/bin/env python3
"""
测试5秒盾CF验证逻辑
"""

import time
import logging
from src.cf_check import CloudflareChecker

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MockTab:
    """模拟浏览器标签页"""
    def __init__(self):
        self.cf_passed = False
        self.attempt_count = 0
        self.click_count = 0
        
    @property
    def html(self):
        # 模拟CF页面内容，前15次检测返回CF内容，之后返回正常页面
        if self.attempt_count < 15:
            return "Cloudflare checking your browser please wait"
        else:
            return "Welcome to the website"
    
    @property 
    def url(self):
        if self.attempt_count < 15:
            return "https://example.com/cf-challenge"
        else:
            return "https://example.com/home"
    
    def ele(self, xpath, timeout=1):
        # 模拟CF元素检测
        if self.attempt_count < 15 and 'cf-' in xpath:
            return True  # 模拟找到CF元素
        return None
    
    def wait(self, seconds):
        print(f"    等待 {seconds} 秒...")
        time.sleep(0.1)  # 实际测试时缩短等待时间
        self.attempt_count += 1
    
    @property
    def actions(self):
        return MockActions(self)

class MockActions:
    """模拟浏览器动作"""
    def __init__(self, tab):
        self.tab = tab
    
    def move_to(self, coords):
        print(f"    移动鼠标到坐标: {coords}")
        return self
    
    def click(self):
        self.tab.click_count += 1
        print(f"    执行点击操作 (第 {self.tab.click_count} 次点击)")
        return self

def test_cf_5s_shield():
    """测试5秒盾CF验证"""
    print("开始测试5秒盾CF验证逻辑...")
    
    # 创建CF检测器
    cf_checker = CloudflareChecker(logger)
    
    # 创建模拟标签页
    mock_tab = MockTab()
    
    print("\n=== 测试场景：模拟CF 5秒盾验证 ===")
    print("前15次检测会遇到CF盾，第16次检测通过")
    print("每5次检测会点击一次坐标 (208, 289)")
    
    # 执行CF验证
    start_time = time.time()
    result = cf_checker.pass_cf_check_with_5s_shield(mock_tab, max_attempts=20, click_interval=5)
    end_time = time.time()
    
    print(f"\n=== 测试结果 ===")
    print(f"验证结果: {'✅ 成功' if result else '❌ 失败'}")
    print(f"总检测次数: {mock_tab.attempt_count}")
    print(f"总点击次数: {mock_tab.click_count}")
    print(f"预期点击次数: {mock_tab.attempt_count // 5}")
    print(f"测试耗时: {end_time - start_time:.2f} 秒")
    
    # 验证逻辑正确性
    expected_clicks = mock_tab.attempt_count // 5
    if mock_tab.click_count == expected_clicks and result:
        print("\n✅ 5秒盾CF验证逻辑测试通过！")
        return True
    else:
        print("\n❌ 5秒盾CF验证逻辑测试失败！")
        return False

def test_cf_detection_logic():
    """测试CF检测逻辑"""
    print("\n开始测试CF检测逻辑...")
    
    cf_checker = CloudflareChecker(logger)
    mock_tab = MockTab()
    
    # 测试CF未通过的情况
    mock_tab.attempt_count = 5  # 模拟仍在CF验证中
    result1 = cf_checker._check_cf_passed(mock_tab)
    print(f"CF验证中检测结果: {'通过' if result1 else '未通过'} (预期: 未通过)")
    
    # 测试CF已通过的情况  
    mock_tab.attempt_count = 20  # 模拟已通过CF验证
    result2 = cf_checker._check_cf_passed(mock_tab)
    print(f"CF已通过检测结果: {'通过' if result2 else '未通过'} (预期: 通过)")
    
    if not result1 and result2:
        print("✅ CF检测逻辑测试通过！")
        return True
    else:
        print("❌ CF检测逻辑测试失败！")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("CF 5秒盾验证功能测试")
    print("=" * 60)
    
    # 测试CF检测逻辑
    detection_test = test_cf_detection_logic()
    
    # 测试5秒盾验证逻辑
    shield_test = test_cf_5s_shield()
    
    print("\n" + "=" * 60)
    if detection_test and shield_test:
        print("🎉 所有测试通过！CF 5秒盾验证功能正常工作")
    else:
        print("⚠️  部分测试失败，请检查代码逻辑")
    print("=" * 60)
