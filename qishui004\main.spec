# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('templates', 'templates'),
        ('config.example.json', '.'),
        ('list01.txt', '.'),
        ('API配置说明.md', '.'),
        ('项目说明.txt', '.'),
    ],
    hiddenimports=[
        'DrissionPage',
        'DrissionPage.ChromiumOptions',
        'DrissionPage.Chromium',
        'requests',
        'json',
        'threading',
        'queue',
        'time',
        'random',
        'logging',
        're',
        'os',
        'pathlib',
        'src.bit_browser',
        'src.config',
        'src.file_handler',
        'src.captcha_solver',
        'src.cloudflare_bypass',
        'src.drission_controller',
        'src.email_code_manager',
        'src.network_analyzer',
        'src.phone_manager',
        'src.thread_manager',
        'src.verification_handler',
        'tasks3',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='索赔自动化系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
