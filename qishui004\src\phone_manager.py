import threading
import time
import requests
import json
import uuid
import socket
import hashlib
from loguru import logger
from .config import config

class PhoneManager:
    """
    手机号管理器 - 复刻1.84版本完整的手机号码管理逻辑
    支持：窗口排队、15分钟有效期、10个验证码、获取最新验证码
    """

    def __init__(self, window_id=None):
        # 从配置文件获取API配置
        phone_config = config.get_phone_api_config()

        self.api_url = phone_config["api_url"]  # https://sms.fixdestar.com
        self.api_key = phone_config["api_key"]
        self.service = phone_config["service"]  # PAYMENT_SMS
        self.country = phone_config["country"]  # us
        self.platform = phone_config["platform"]  # fixdestar
        self.timeout = phone_config["timeout"]
        self.max_retries = phone_config["max_retries"]
        self.retry_delay = phone_config["retry_delay"]
        self.verification_timeout = phone_config["verification_timeout"]
        self.verification_interval = phone_config["verification_interval"]

        # 手机号状态管理
        self.current_phone = None
        self.current_phone_id = None
        self.phone_expires_at = 0  # 手机号过期时间（毫秒）
        self.sms_count = 0  # 已使用的验证码数量
        self.max_sms = 10  # 最大验证码数量
        self.phone_lock = threading.Lock()

        # 生成唯一标识
        self.client_id = self._generate_client_id()
        self.machine_id = self._get_machine_id()

        # 生成窗口ID
        if window_id:
            self.window_id = window_id
        else:
            thread_id = threading.get_ident()
            timestamp = int(time.time() * 1000)
            self.window_id = f"window_{thread_id}_{timestamp}"

        # 请求会话
        self.session = requests.Session()
        self.session.timeout = self.timeout

        # 服务代码映射（基于1.84版本）
        self.service_map = {
            'PAYMENT_SMS': 'po',  # payment对应po
            'PAYMENT SMS': 'po',
            'payment': 'po',
            'paypal': 'po',
            'google': 'go',
            'microsoft': 'microsoft',
            'apple': 'apple',
            'any': 'any'
        }

        # 国家代码映射
        self.country_map = {
            'us': '0',    # 美国
            'ru': '7',    # 俄罗斯
            'uk': '16',   # 英国
            'cn': '1'     # 中国
        }

        logger.info("📱 手机号管理器已初始化（1.84版本完整逻辑）")
        logger.info(f"🆔 窗口ID: {self.window_id}")
        logger.info(f"🔧 客户端ID: {self.client_id}")
        logger.info(f"🖥️ 机器ID: {self.machine_id}")
        logger.info(f"🌐 API地址: {self.api_url}")
        logger.info(f"🔑 API密钥: {self.api_key[:10]}...")
        logger.info(f"📋 服务类型: {self.service}")
        logger.info(f"🌍 国家: {self.country}")

        # 检查配置
        if not self.api_url or not self.api_key:
            logger.warning("⚠️ 手机号码API未配置，请在配置文件中设置api_url和api_key")
            logger.warning("💡 运行程序选择'3. 配置设置'来配置API")
        else:
            logger.info("✅ fixdestar官方API配置已加载")

    def _generate_client_id(self):
        """生成唯一的客户端ID"""
        thread_id = threading.get_ident()
        timestamp = int(time.time() * 1000)
        unique_str = f"{thread_id}_{timestamp}_{uuid.uuid4().hex[:8]}"
        return hashlib.md5(unique_str.encode()).hexdigest()[:16]

    def _get_machine_id(self):
        """获取机器唯一标识"""
        try:
            hostname = socket.gethostname()
            mac = hex(uuid.getnode())[2:]
            machine_str = f"{hostname}_{mac}"
            return hashlib.md5(machine_str.encode()).hexdigest()[:12]
        except Exception as e:
            logger.warning(f"获取机器ID失败，使用随机ID: {e}")
            return uuid.uuid4().hex[:12]
    
    def get_phone_number(self, window_id=None, thread_id=None, email=None):
        """
        获取手机号码 - 复刻1.84版本完整逻辑
        支持窗口排队、15分钟有效期、10个验证码限制

        Args:
            window_id: 浏览器窗口ID（兼容性参数）
            thread_id: 线程ID（兼容性参数）
            email: 邮箱（兼容性参数）

        Returns:
            str: 手机号码，失败返回None
        """
        with self.phone_lock:
            try:
                thread_id = thread_id or threading.get_ident()
                logger.info(f"🔄 线程 {thread_id} 请求获取手机号...")
                logger.info(f"📋 请求参数: 平台={self.platform}, 服务={self.service}, 国家={self.country}")

                # 检查当前手机号是否仍然有效
                if self.current_phone and self._is_phone_valid():
                    logger.info(f"♻️ 复用有效手机号: {self.current_phone}")
                    logger.info(f"📊 使用状态: {self.sms_count}/{self.max_sms} 条短信")
                    remaining_minutes = max(0, (self.phone_expires_at - int(time.time() * 1000)) // 60000)
                    logger.info(f"⏰ 剩余时间: {remaining_minutes} 分钟")
                    return self.current_phone

                # 获取新的手机号
                logger.info(f"🔧 使用fixdestar官方API: {self.api_url}")

                # 检查API配置
                if not self.api_url or not self.api_key:
                    logger.error("❌ 手机号码API未配置")
                    return None

                # 服务代码和国家代码映射
                service_code = self.service_map.get(self.service, 'po')
                country_code = self.country_map.get(self.country, '0')

                # 构建请求参数
                params = {
                    'api_key': self.api_key,
                    'service': service_code,
                    'country': country_code
                }

                # 调用fixdestar官方API
                url = f"{self.api_url}/api/getNumber"
                logger.info(f"📞 调用fixdestar API: {url}")
                logger.info(f"📋 参数: service={service_code}, country={country_code}")

                response = self.session.get(url, params=params)
                logger.info(f"📡 API响应状态码: {response.status_code}")

                if response.status_code == 200:
                    # fixdestar API返回格式：ACCESS_NUMBER:phone_id:phone_number
                    response_text = response.text.strip()
                    logger.info(f"📋 API响应内容: {response_text}")

                    if response_text.startswith('ACCESS_NUMBER:'):
                        # 解析响应：ACCESS_NUMBER:123456:1234567890
                        parts = response_text.split(':')
                        if len(parts) >= 3:
                            self.current_phone_id = parts[1]
                            self.current_phone = parts[2]

                            # 设置手机号状态（15分钟有效期）
                            self.phone_expires_at = int(time.time() * 1000) + (15 * 60 * 1000)  # 15分钟
                            self.sms_count = 0
                            self.max_sms = 10

                            # 计算剩余时间
                            remaining_minutes = (self.phone_expires_at - int(time.time() * 1000)) // 60000

                            logger.info(f"✅ 线程 {thread_id} 成功获取手机号: {self.current_phone}")
                            logger.info(f"🆔 手机号ID: {self.current_phone_id}")
                            logger.info(f"🏢 接码平台: {self.platform}")
                            logger.info(f"🔧 服务类型: {self.service}")
                            logger.info(f"🌍 国家: {self.country}")
                            logger.info(f"📊 使用状态: {self.sms_count}/{self.max_sms} 条短信")
                            logger.info(f"⏰ 有效期: {remaining_minutes} 分钟")
                            logger.info(f"🆔 窗口ID: {self.window_id}")

                            return self.current_phone
                        else:
                            logger.warning(f"❌ 响应格式错误: {response_text}")

                    elif 'NO_NUMBERS' in response_text:
                        logger.warning(f"❌ 线程 {thread_id} 暂无可用手机号")
                        self._log_error_suggestions("暂时没有可用号码")
                    elif 'WRONG_SERVICE' in response_text:
                        logger.error(f"❌ 线程 {thread_id} 服务代码错误")
                        self._log_error_suggestions("服务代码错误")
                    elif 'BAD_KEY' in response_text:
                        logger.error(f"❌ 线程 {thread_id} API密钥无效")
                        self._log_error_suggestions("API密钥无效")
                    elif 'NO_BALANCE' in response_text:
                        logger.error(f"❌ 线程 {thread_id} 账户余额不足")
                        self._log_error_suggestions("账户余额不足")
                    elif 'ERROR' in response_text:
                        logger.error(f"❌ 线程 {thread_id} API错误: {response_text}")
                    else:
                        logger.warning(f"❌ 线程 {thread_id} 未知响应: {response_text}")

                else:
                    logger.warning(f"❌ 线程 {thread_id} HTTP错误: {response.status_code}")
                    logger.debug(f"响应内容: {response.text}")

                return None

            except Exception as e:
                logger.error(f"❌ 获取手机号异常: {e}")
                self._log_error_suggestions(str(e))
                return None

    def _is_phone_valid(self):
        """检查当前手机号是否仍然有效"""
        if not self.current_phone or not self.current_phone_id:
            return False

        # 检查是否过期
        current_time = int(time.time() * 1000)
        if current_time >= self.phone_expires_at:
            logger.info("⏰ 手机号已过期")
            return False

        # 检查是否达到最大短信数量
        if self.sms_count >= self.max_sms:
            logger.info("📊 手机号已达到最大短信数量")
            return False

        return True

    def _log_error_suggestions(self, error_msg):
        """记录错误解决建议"""
        if "API密钥无效" in error_msg or "BAD_KEY" in error_msg:
            logger.error("💡 解决建议:")
            logger.error("   1. 检查config.json中的phone_api.api_key配置")
            logger.error("   2. 确认API密钥在fixdestar平台是否有效")
            logger.error("   3. 检查API密钥是否有足够权限")
        elif "账户余额不足" in error_msg or "NO_BALANCE" in error_msg:
            logger.error("💡 解决建议:")
            logger.error("   1. 登录fixdestar平台查看账户余额")
            logger.error("   2. 充值账户余额")
            logger.error("   3. 检查当前服务的价格设置")
        elif "暂时没有可用号码" in error_msg or "NO_NUMBERS" in error_msg:
            logger.error("💡 解决建议:")
            logger.error("   1. 稍后重试，可能是当前号码池暂时耗尽")
            logger.error("   2. 尝试切换到其他国家")
            logger.error("   3. 联系fixdestar客服")
        elif "服务代码错误" in error_msg or "WRONG_SERVICE" in error_msg:
            logger.error("💡 解决建议:")
            logger.error("   1. 检查服务类型配置是否正确")
            logger.error("   2. 确认fixdestar平台支持该服务")
            logger.error("   3. 查看服务代码映射是否正确")
    
    def get_latest_sms(self, phone=None, timeout=None):
        """
        获取最新的短信验证码 - 复刻1.84版本完整逻辑
        确保每次获取的都是最新验证码，而不是上次的验证码

        Args:
            phone: 手机号（可选，用于验证）
            timeout: 超时时间（秒）

        Returns:
            str: 验证码，失败返回None
        """
        if timeout is None:
            timeout = self.verification_timeout

        start_time = time.time()
        check_interval = 5  # 每5秒检查一次，避免对fixdestar API请求过于频繁
        last_error_time = 0  # 上次错误时间
        consecutive_errors = 0  # 连续错误次数

        thread_id = threading.get_ident()
        logger.info(f"� 开始获取验证码，超时时间: {timeout}秒（每{check_interval}秒检查一次）")
        logger.info(f"📱 手机号: {phone or self.current_phone}")
        logger.info(f"🆔 手机号ID: {self.current_phone_id}")

        # 检查手机号状态
        if not self.current_phone_id:
            logger.error("❌ 没有有效的手机号ID")
            return None

        if not self._is_phone_valid():
            logger.error("❌ 手机号已过期或无效")
            return None

        while (time.time() - start_time) < timeout:
            try:
                # fixdestar官方API获取验证码
                params = {
                    'api_key': self.api_key,
                    'id': self.current_phone_id
                }

                url = f"{self.api_url}/api/getStatus"
                logger.debug(f"📞 调用验证码API: {url}")

                response = self.session.get(url, params=params)
                logger.debug(f"📡 验证码API响应状态码: {response.status_code}")

                if response.status_code == 200:
                    # fixdestar API返回格式：STATUS_WAIT_CODE 或 STATUS_OK:验证码
                    response_text = response.text.strip()
                    logger.debug(f"📋 验证码API响应: {response_text}")

                    if response_text.startswith('STATUS_OK:'):
                        # 获取到验证码：STATUS_OK:123456
                        verification_code = response_text.split(':', 1)[1]

                        # 更新短信计数
                        self.sms_count += 1

                        logger.info(f"✅ 成功获取验证码: {verification_code}")
                        logger.info(f"📊 短信计数: {self.sms_count}/{self.max_sms}")
                        logger.info(f"🏢 平台: {self.platform}")
                        logger.info(f"🆔 窗口ID: {self.window_id}")

                        # 重置错误计数器
                        consecutive_errors = 0

                        return verification_code

                    elif response_text == 'STATUS_WAIT_CODE':
                        logger.debug(f"⏳ 等待验证码...")

                    elif response_text == 'STATUS_WAIT_RETRY':
                        logger.debug(f"⏳ 等待重试...")

                    elif response_text == 'STATUS_CANCEL':
                        logger.warning(f"❌ 手机号已取消")
                        break

                    elif 'BAD_KEY' in response_text:
                        logger.error(f"❌ API密钥无效")
                        break

                    elif 'BAD_STATUS' in response_text:
                        logger.error(f"❌ 手机号状态错误")
                        break

                    else:
                        logger.debug(f"⏳ 其他状态: {response_text}")

                else:
                    logger.warning(f"⚠️ HTTP错误: {response.status_code}")
                    logger.debug(f"响应内容: {response.text}")

                # 重置错误计数器
                consecutive_errors = 0
                time.sleep(check_interval)

            except Exception as e:
                consecutive_errors += 1
                current_time = time.time()

                # 避免频繁打印相同错误
                if current_time - last_error_time > 10:  # 10秒内不重复打印相同错误
                    logger.warning(f"⚠️ 获取验证码时出错 (连续{consecutive_errors}次): {e}")
                    last_error_time = current_time

                # 如果连续错误太多，可能是严重问题
                if consecutive_errors >= 5:
                    logger.error(f"❌ 连续错误次数过多，可能是严重问题")
                    break

                time.sleep(check_interval)

        logger.warning(f"⏰ 获取验证码超时")
        return None

    # 兼容性方法
    def get_verification_code(self, phone_number, timeout=None, interval=None):
        """
        获取验证码的兼容性方法

        Args:
            phone_number: 手机号码
            timeout: 超时时间（秒）
            interval: 轮询间隔（秒）（忽略，使用固定间隔）

        Returns:
            str: 验证码，失败返回None
        """
        return self.get_latest_sms(phone_number, timeout)

    def release_phone(self, phone_number):
        """
        释放手机号码（设置为完成状态）- 复刻1.84版本逻辑

        Args:
            phone_number: 要释放的手机号码
        """
        thread_id = threading.get_ident()
        logger.info(f"🔄 线程 {thread_id} 窗口 {self.window_id} 释放手机号: {phone_number}")

        if not self.current_phone_id:
            logger.warning(f"⚠️ 线程 {thread_id} 没有有效的手机号ID")
            return

        try:
            # fixdestar官方API设置状态为完成
            params = {
                'api_key': self.api_key,
                'id': self.current_phone_id,
                'status': '6'  # 6表示完成状态
            }

            url = f"{self.api_url}/api/setStatus"
            logger.info(f"🔄 设置手机号状态为完成: {url}")

            response = self.session.get(url, params=params)

            if response.status_code == 200:
                response_text = response.text.strip()
                if response_text == 'ACCESS_ACTIVATION':
                    logger.info(f"✅ 线程 {thread_id} 手机号释放成功")
                else:
                    logger.warning(f"⚠️ 线程 {thread_id} 释放响应: {response_text}")
            else:
                logger.warning(f"⚠️ 线程 {thread_id} 释放API响应: {response.status_code}")

        except Exception as e:
            logger.warning(f"⚠️ 线程 {thread_id} 释放请求异常: {e}")

        # 清理当前手机号信息
        self.current_phone = None
        self.current_phone_id = None
        self.phone_expires_at = 0
        self.sms_count = 0
        logger.info(f"✅ 线程 {thread_id} 手机号释放完成")

    def get_balance(self):
        """
        获取fixdestar账户余额

        Returns:
            float: 账户余额
        """
        try:
            logger.info(f"🔍 查询 {self.platform} 平台账户余额...")

            params = {
                'api_key': self.api_key
            }

            url = f"{self.api_url}/api/getBalance"
            response = self.session.get(url, params=params)

            if response.status_code == 200:
                response_text = response.text.strip()
                if response_text.startswith('ACCESS_BALANCE:'):
                    # 解析余额：ACCESS_BALANCE:100.50
                    balance = float(response_text.split(':', 1)[1])
                    logger.info(f"💰 账户余额: {balance}")
                    return balance
                else:
                    logger.warning(f"⚠️ 余额响应格式错误: {response_text}")
                    return 0.0
            else:
                logger.warning(f"⚠️ 获取余额失败: {response.status_code}")
                return 0.0

        except Exception as e:
            logger.error(f"❌ 获取余额异常: {e}")
            return 0.0


# 创建全局实例
phone_manager = PhoneManager()


# 便捷函数 - 兼容1.84版本接口
def get_phone_number(window_id=None, thread_id=None, email=None):
    """获取手机号码的便捷函数"""
    return phone_manager.get_phone_number(window_id, thread_id, email)


def get_phone_verification_code(phone_number, timeout=60):
    """获取手机验证码的便捷函数"""
    return phone_manager.get_latest_sms(phone_number, timeout)


def release_phone_number(phone_number):
    """释放手机号码的便捷函数"""
    return phone_manager.release_phone(phone_number)


def get_balance():
    """获取账户余额的便捷函数"""
    return phone_manager.get_balance()
