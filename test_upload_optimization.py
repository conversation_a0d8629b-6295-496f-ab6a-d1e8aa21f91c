#!/usr/bin/env python3
"""
测试证据上传优化功能
使用DrissionPageMCP服务测试比特浏览器的文件上传功能
"""

import os
import time
import glob

def test_evidence_upload():
    """测试证据文件上传功能"""
    print("开始测试证据文件上传功能...")
    
    # 查找证据文件
    evidence_dir = "generated_images/thread_1"
    if not os.path.exists(evidence_dir):
        print(f"错误: 证据目录不存在 {evidence_dir}")
        return False
    
    jpg_files = glob.glob(os.path.join(evidence_dir, "*.jpg"))
    if not jpg_files:
        print(f"错误: 在 {evidence_dir} 中找不到JPG文件")
        return False
    
    print(f"找到 {len(jpg_files)} 个证据文件:")
    for i, file in enumerate(jpg_files, 1):
        print(f"  {i}. {os.path.basename(file)}")
    
    # 模拟上传过程
    print("\n开始模拟上传过程...")
    
    # 1. 检查证据上传选择框
    print("1. 检查证据上传选择框状态...")
    
    # 2. 检查文件输入框状态
    print("2. 检查文件输入框状态...")
    
    # 3. 模拟上传文件
    print("3. 模拟上传文件...")
    successful_uploads = 0
    
    for i, jpg_file in enumerate(jpg_files[:4]):  # 测试前4个文件
        file_id = f"file{i+1}"
        print(f"   上传文件 {i+1} 到 {file_id}: {os.path.basename(jpg_file)}")
        
        # 检查文件是否存在
        if os.path.exists(jpg_file):
            print(f"     文件存在: {jpg_file}")
            successful_uploads += 1
        else:
            print(f"     文件不存在: {jpg_file}")
    
    print(f"\n测试完成: {successful_uploads}/{len(jpg_files[:4])} 个文件可以上传")
    
    return successful_uploads > 0

def create_upload_test_script():
    """创建JavaScript测试脚本"""
    js_script = """
    // 证据上传测试脚本
    function testEvidenceUpload() {
        console.log('开始测试证据上传功能...');
        
        // 1. 检查证据上传选择框
        const proofCheckbox = document.getElementById('ProofOfPurchase');
        if (!proofCheckbox) {
            console.error('找不到证据上传选择框');
            return false;
        }
        
        console.log('证据上传选择框状态:', proofCheckbox.checked);
        
        // 2. 检查文件输入框
        const fileInputs = [];
        for (let i = 1; i <= 10; i++) {
            const input = document.getElementById(`file${i}`);
            if (input) {
                const style = window.getComputedStyle(input);
                fileInputs.push({
                    id: `file${i}`,
                    visible: style.display !== 'none' && style.visibility !== 'hidden',
                    enabled: !input.disabled,
                    hasFiles: input.files.length > 0
                });
            }
        }
        
        console.log('文件输入框状态:', fileInputs);
        
        // 3. 返回测试结果
        return {
            proofCheckboxExists: !!proofCheckbox,
            proofCheckboxChecked: proofCheckbox ? proofCheckbox.checked : false,
            availableInputs: fileInputs.filter(input => input.visible && input.enabled).length,
            totalInputs: fileInputs.length
        };
    }
    
    // 运行测试
    return testEvidenceUpload();
    """
    
    return js_script

if __name__ == "__main__":
    # 运行测试
    test_evidence_upload()
    
    # 输出JavaScript测试脚本
    print("\n" + "="*60)
    print("JavaScript测试脚本:")
    print("="*60)
    print(create_upload_test_script())
