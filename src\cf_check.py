import threading

class CloudflareChecker:
    """Cloudflare盾检测与自动点击类"""
    def __init__(self, logger=None):
        self.logger = logger
    def pass_cf_check(self, tab, attempt_num, div_xpath='x:/html/body/div[2]/div/div[1]/div/div', checkbox_type='@type=checkbox', timeout=3):
        
        thread_id = threading.get_ident()
        try:
            self.logger.info(f"线程 {thread_id} - 第 {attempt_num + 1} 次尝试：查找div元素: {div_xpath}")
            div_ele = tab.ele(div_xpath, timeout=timeout) 
            if attempt_num >= 1:
                  self.logger.info("点击cf盾框")
                  tab.actions.move_to((208, 289)).click()
                  tab.wait(3)
            return False
        except Exception as e:
            if self.logger:
                self.logger.error(f"线程 {thread_id} - 第 {attempt_num + 1} 次尝试：cf盾处理异常: {e}")
            return False
    
    
    #修改V2的代码(点击固定坐标:),进行固定的cf模拟人手点击方法
    #修改V3的代码(点击固定坐标:),进行固定的cf模拟人手点击方法
    def pass_cf_check_with_5s_shield(self, tab, max_attempts=25, click_interval=5):
        """
        处理5秒盾的CF验证码
        每检测5次，点击一次坐标 (208, 289)

        Args:
            tab: 浏览器标签页对象
            max_attempts: 最大尝试次数，默认25次
            click_interval: 点击间隔，每几次检测点击一次，默认5次

        Returns:
            bool: 是否成功通过CF验证
        """
        thread_id = threading.get_ident()

        try:
            self.logger.info(f"线程 {thread_id} - 开始5秒盾CF验证处理，最大尝试 {max_attempts} 次")

            for attempt in range(max_attempts):
                self.logger.info(f"线程 {thread_id} - 第 {attempt + 1}/{max_attempts} 次检测CF盾状态")

                # 检查是否已经通过CF验证
                if self._check_cf_passed(tab):
                    self.logger.info(f"线程 {thread_id} - ✅ CF盾验证成功，已到达目标页面")
                    return True

                # 每5次检测点击一次坐标
                if (attempt + 1) % click_interval == 0:
                    self.logger.info(f"线程 {thread_id} - 第 {attempt + 1} 次检测，执行点击操作")
                    try:
                        tab.actions.move_to((208, 289)).click()
                        self.logger.info(f"线程 {thread_id} - 点击坐标 (208, 289) 完成")
                        tab.wait(2)  # 点击后等待2秒
                    except Exception as click_e:
                        self.logger.error(f"线程 {thread_id} - 点击操作失败: {click_e}")

                # 等待5秒再进行下一次检测
                tab.wait(5)

            # 最终检查
            if self._check_cf_passed(tab):
                self.logger.info(f"线程 {thread_id} - ✅ CF盾验证最终成功")
                return True
            else:
                self.logger.warning(f"线程 {thread_id} - ❌ CF盾验证失败，已达到最大尝试次数")
                return False

        except Exception as e:
            self.logger.error(f"线程 {thread_id} - 5秒盾CF验证处理异常: {e}")
            return False

    def _check_cf_passed(self, tab):
        """
        检查是否已经通过CF验证

        Args:
            tab: 浏览器标签页对象

        Returns:
            bool: 是否已通过CF验证
        """
        try:
            current_page = tab.html.lower()
            current_url = tab.url.lower()

            # CF盾标识词
            cf_indicators = [
                'cloudflare',
                'checking your browser',
                'please wait',
                'verifying you are human',
                'just a moment',
                'browser verification',
                'security check',
                'ddos protection',
                'cf-browser-verification',
                'cf-challenge'
            ]

            # 检查页面内容是否包含CF盾标识
            has_cf_content = any(indicator in current_page for indicator in cf_indicators)

            # 检查URL是否包含CF相关标识
            has_cf_url = any(indicator in current_url for indicator in ['cloudflare', 'cf-', 'challenge'])

            # 检查是否有CF相关的元素
            has_cf_elements = False
            try:
                cf_elements = [
                    'x://div[contains(@class, "cf-")]',
                    'x://div[contains(@class, "cloudflare")]',
                    'x://div[contains(@class, "challenge")]',
                    'x://div[@id="cf-stage"]',
                    'x://form[@id="challenge-form"]'
                ]

                for xpath in cf_elements:
                    if tab.ele(xpath, timeout=0.5):
                        has_cf_elements = True
                        break
            except:
                pass

            # 如果没有CF标识，说明已经通过验证
            passed = not (has_cf_content or has_cf_url or has_cf_elements)

            if passed:
                self.logger.debug(f"CF验证检查: 已通过 - URL: {current_url[:100]}...")
            else:
                self.logger.debug(f"CF验证检查: 仍在验证中")

            return passed

        except Exception as e:
            self.logger.error(f"检查CF验证状态异常: {e}")
            return False

    def pass_cf_check_v2(self, tab, attempt_num, div_xpath='x:/html/body/div[2]/div/div[1]/div/div', checkbox_type='@type=checkbox', timeout=3):
        thread_id = threading.get_ident()
        try:
            self.logger.info(f"线程 {thread_id} - 第 {attempt_num + 1} 次尝试：查找div元素")

            # 多种常见的CF盾div路径
            div_xpaths = [
                'x:/html/body/div[2]/div/div[1]/div/div',  # 原始路径
                'x:/html/body/div[1]/div/div[1]/div/div',  # 第一个div容器
                'x:/html/body/div[3]/div/div[1]/div/div',  # 第三个div容器
                'x://div[contains(@class, "cf-challenge")]',  # CF挑战div
                'x://div[contains(@class, "cloudflare")]',   # Cloudflare相关div
                'x://div[contains(@class, "turnstile")]',    # Turnstile相关div
                'x://div[@id="cf-stage"]',                   # CF舞台区域
                'x://div[@id="challenge-stage"]',            # 挑战舞台区域
                'x://div[contains(@class, "challenge-container")]',  # 挑战容器
                'x://div[contains(@class, "cf-browser-verification")]',  # 浏览器验证
                'x://div[contains(@class, "cf-turnstile-wrapper")]',     # Turnstile包装器
                'x://div[contains(@class, "challenge-form")]',          # 挑战表单
                'x://div[contains(@id, "cf-")]',             # 任何包含cf-的id
                'x://div[contains(@data-ray, "")]',          # Ray ID相关
                'x://div[@class="main-wrapper"]',            # 主包装器
                'x://div[@class="challenge-wrapper"]',       # 挑战包装器
                'x://form[@id="challenge-form"]',            # 挑战表单
                'x://div[contains(@class, "spacer")]//parent::div',  # 间隔器的父div
                'x://iframe[@title="Widget containing checkbox for hCaptcha security challenge"]//parent::div',  # hCaptcha父div
                'x://iframe[contains(@src, "cloudflare")]//parent::div'  # Cloudflare iframe父div
            ]
            
            div_ele = None
            used_xpath = None
            
            # 遍历尝试所有div路径
            for xpath in div_xpaths:
                try:
                    div_ele = tab.ele(xpath, timeout=0.5)  # 短超时快速尝试
                    if div_ele:
                        used_xpath = xpath
                        self.logger.info(f"线程 {thread_id} - 找到div元素: {xpath}")
                        break
                except:
                    continue
            
            if not div_ele:
                self.logger.info(f"线程 {thread_id} - 未找到任何div元素，尝试直接坐标点击")
            
            # 如果是第2次及以后的尝试，直接点击坐标
            if attempt_num >= 1:
                self.logger.info(f"线程 {thread_id} - 第 {attempt_num + 1} 次尝试，点击cf盾框坐标 (208, 289)")
                tab.actions.move_to((208, 289)).click()
                tab.wait(3)
                
                # 如果找到了div元素，也可以尝试点击div中心
                if div_ele:
                    try:
                        self.logger.info(f"线程 {thread_id} - 同时尝试点击找到的div元素")
                        tab.actions.move_to(div_ele).click()
                        tab.wait(2)
                    except Exception as div_click_e:
                        self.logger.debug(f"线程 {thread_id} - div点击失败: {div_click_e}")
                        
                # 检查是否成功绕过CF盾 - 等待2秒后检查页面
                tab.wait(2)
                current_page = tab.html.lower()
                
                # 检查是否还有CF盾标识
                cf_indicators = ['cloudflare', 'checking your browser', 'please wait', 'verifying you are human']
                has_cf = any(indicator in current_page for indicator in cf_indicators)
                
                if not has_cf:
                    self.logger.info(f"线程 {thread_id} - ✅ CF盾处理成功，页面已跳转")
                    return True
                else:
                    self.logger.info(f"线程 {thread_id} - ⏳ CF盾仍在处理中")
                    return False
            else:
                # 第一次尝试，只是检测CF盾存在
                self.logger.info(f"线程 {thread_id} - 第一次检测CF盾，暂不处理")
                return False
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"线程 {thread_id} - 第 {attempt_num + 1} 次尝试：cf盾处理异常: {e}")
            return False
    