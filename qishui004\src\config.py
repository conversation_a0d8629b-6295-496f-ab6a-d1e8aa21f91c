"""
配置管理模块
管理系统配置参数
"""

import json
import os
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class Config:
    """配置管理类"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config_data = {}
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
                logger.info(f"配置文件已加载: {self.config_file}")
            else:
                # 创建默认配置
                self.create_default_config()
                logger.info("创建默认配置文件")
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置"""
        self.config_data = {
            "thread_count": 1,
            "yescaptcha_api_key": "",
            "sms_platform_key": "",
            "phone_api": {
                "api_url": "",
                "api_key": "",
                "service": "payment",
                "country": "us",
                "platform": "fixdestar",
                "timeout": 30,
                "max_retries": 3,
                "retry_delay": 5,
                "verification_timeout": 60,
                "verification_interval": 3
            },
            "email_api": {
                "api_url": "",
                "api_key": "",
                "timeout": 30,
                "max_retries": 3
            },
            "bit_browser": {
                "api_host": "127.0.0.1",
                "api_port": 54345
            },
            "target_url": "https://veritaconnect.com/poppisettlement/Claimant",
            "files": {
                "input_file": "list01.txt",
                "success_file": "listsp_ok.txt",
                "failed_file": "listsp_ng.txt",
                "cache_file": "listsp_data.txt"
            },
            "timeouts": {
                "page_load": 30,
                "cloudflare_shield": 60,
                "turnstile": 300,
                "verification_code": 300
            }
        }
        self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=2, ensure_ascii=False)
            logger.info(f"配置文件已保存: {self.config_file}")
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        try:
            keys = key.split('.')
            value = self.config_data
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
        except Exception:
            return default
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        try:
            keys = key.split('.')
            data = self.config_data
            
            for k in keys[:-1]:
                if k not in data:
                    data[k] = {}
                data = data[k]
            
            data[keys[-1]] = value
            self.save_config()
        except Exception as e:
            logger.error(f"设置配置值失败: {e}")
    
    def update_from_user_input(self):
        """从用户输入更新配置"""
        print("配置设置")
        print("=" * 50)

        # 线程数量
        current_threads = self.get("thread_count", 1)
        thread_input = input(f"请输入线程数量 (当前: {current_threads}): ").strip()
        if thread_input:
            try:
                thread_count = int(thread_input)
                if thread_count > 0:
                    self.set("thread_count", thread_count)
                else:
                    print("线程数量必须大于0")
            except ValueError:
                print("无效的线程数量")

        print("\n📱 手机号码API配置")
        print("-" * 30)

        # 手机号码API地址
        current_phone_url = self.get("phone_api.api_url", "")
        phone_url_input = input(f"请输入手机号码API地址 (当前: {'已设置' if current_phone_url else '未设置'}): ").strip()
        if phone_url_input:
            self.set("phone_api.api_url", phone_url_input)

        # 手机号码API密钥
        current_phone_key = self.get("phone_api.api_key", "")
        phone_key_input = input(f"请输入手机号码API密钥 (当前: {'已设置' if current_phone_key else '未设置'}): ").strip()
        if phone_key_input:
            self.set("phone_api.api_key", phone_key_input)

        print("\n📧 邮箱验证码API配置")
        print("-" * 30)

        # 邮箱API地址
        current_email_url = self.get("email_api.api_url", "")
        email_url_input = input(f"请输入邮箱API地址 (当前: {'已设置' if current_email_url else '未设置'}): ").strip()
        if email_url_input:
            self.set("email_api.api_url", email_url_input)

        # 邮箱API密钥
        current_email_key = self.get("email_api.api_key", "")
        email_key_input = input(f"请输入邮箱API密钥 (当前: {'已设置' if current_email_key else '未设置'}): ").strip()
        if email_key_input:
            self.set("email_api.api_key", email_key_input)

        print("\n🔧 其他配置")
        print("-" * 30)

        # YesCaptcha API密钥
        current_api = self.get("yescaptcha_api_key", "")
        api_input = input(f"请输入YesCaptcha API密钥 (当前: {'已设置' if current_api else '未设置'}): ").strip()
        if api_input:
            self.set("yescaptcha_api_key", api_input)

        # 手机接码平台密钥（旧版本兼容）
        current_sms = self.get("sms_platform_key", "")
        sms_input = input(f"请输入手机接码平台密钥 (当前: {'已设置' if current_sms else '未设置'}): ").strip()
        if sms_input:
            self.set("sms_platform_key", sms_input)

        print("\n✅ 配置更新完成")
        print("💡 提示：手机号码和邮箱API配置是必需的，确保填写正确的API地址和密钥")
    
    def validate_config(self) -> bool:
        """
        验证配置完整性
        
        Returns:
            bool: 配置是否有效
        """
        required_keys = [
            "thread_count",
            "yescaptcha_api_key",
            "sms_platform_key"
        ]
        
        missing_keys = []
        for key in required_keys:
            value = self.get(key)
            if not value:
                missing_keys.append(key)
        
        if missing_keys:
            logger.error(f"缺少必要配置: {missing_keys}")
            return False
        
        # 验证线程数量
        thread_count = self.get("thread_count")
        if not isinstance(thread_count, int) or thread_count <= 0:
            logger.error("线程数量必须是正整数")
            return False
        
        return True
    
    def get_thread_count(self) -> int:
        """获取线程数量"""
        return self.get("thread_count", 1)

    def update_thread_count_from_browsers(self, browser_count: int):
        """
        根据浏览器数量更新线程数量

        Args:
            browser_count: 浏览器数量
        """
        if browser_count > 0:
            self.set("thread_count", browser_count)
            logger.info(f"线程数量已更新为浏览器数量: {browser_count}")
        else:
            logger.warning("浏览器数量为0，保持当前线程数量")
    
    def get_yescaptcha_api_key(self) -> str:
        """获取YesCaptcha API密钥"""
        return self.get("yescaptcha_api_key", "")
    
    def get_sms_platform_key(self) -> str:
        """获取手机接码平台密钥"""
        return self.get("sms_platform_key", "")
    
    def get_bit_browser_config(self) -> Dict[str, Any]:
        """获取比特浏览器配置"""
        return {
            "api_host": self.get("bit_browser.api_host", "127.0.0.1"),
            "api_port": self.get("bit_browser.api_port", 54345)
        }
    
    def get_target_url(self) -> str:
        """获取目标URL"""
        return self.get("target_url", "https://veritaconnect.com/poppisettlement/Claimant")
    
    def get_file_paths(self) -> Dict[str, str]:
        """获取文件路径配置"""
        return {
            "input_file": self.get("files.input_file", "list01.txt"),
            "success_file": self.get("files.success_file", "listsp_ok.txt"),
            "failed_file": self.get("files.failed_file", "listsp_ng.txt"),
            "cache_file": self.get("files.cache_file", "listsp_data.txt")
        }
    
    def get_timeouts(self) -> Dict[str, int]:
        """获取超时配置"""
        return {
            "page_load": self.get("timeouts.page_load", 30),
            "cloudflare_shield": self.get("timeouts.cloudflare_shield", 60),
            "turnstile": self.get("timeouts.turnstile", 300),
            "verification_code": self.get("timeouts.verification_code", 300)
        }

    def get_phone_api_config(self) -> Dict[str, Any]:
        """获取手机号码API配置"""
        return {
            "api_url": self.get("phone_api.api_url", ""),
            "api_key": self.get("phone_api.api_key", ""),
            "service": self.get("phone_api.service", "payment"),
            "country": self.get("phone_api.country", "us"),
            "platform": self.get("phone_api.platform", "fixdestar"),
            "timeout": self.get("phone_api.timeout", 30),
            "max_retries": self.get("phone_api.max_retries", 3),
            "retry_delay": self.get("phone_api.retry_delay", 5),
            "verification_timeout": self.get("phone_api.verification_timeout", 60),
            "verification_interval": self.get("phone_api.verification_interval", 3)
        }

    def get_email_api_config(self) -> Dict[str, Any]:
        """获取邮箱API配置"""
        return {
            "api_url": self.get("email_api.api_url", ""),
            "api_key": self.get("email_api.api_key", ""),
            "timeout": self.get("email_api.timeout", 30),
            "max_retries": self.get("email_api.max_retries", 3)
        }

    def is_phone_api_configured(self) -> bool:
        """检查手机号码API是否已配置"""
        config = self.get_phone_api_config()
        return bool(config["api_url"] and config["api_key"])

    def is_email_api_configured(self) -> bool:
        """检查邮箱API是否已配置"""
        config = self.get_email_api_config()
        return bool(config["api_url"] and config["api_key"])

    def validate_api_config(self) -> Dict[str, bool]:
        """
        验证API配置

        Returns:
            Dict[str, bool]: 各API配置的验证结果
        """
        return {
            "phone_api": self.is_phone_api_configured(),
            "email_api": self.is_email_api_configured(),
            "yescaptcha_api": bool(self.get("yescaptcha_api_key"))
        }


# 全局配置实例
config = Config()
