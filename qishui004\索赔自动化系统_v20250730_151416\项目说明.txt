项目网址:https://veritaconnect.com/poppisettlement/Claimant

第一步:打开比特浏览器,打开项目网址,过5s盾(会跳转网页)

第二步:等待点击点选框:单选框 3: value='UnknownFileOnline', name='Option', id='Option'
----如果点选框出现,证明在第一个页面,
----判断页面CLOUDFLARE是否Success,如果不是需要检测状态是否点击,模拟真人点击(如果需要)
----CLOUDFLARE是否Success状态,点击Next按钮

第三步:等待页面跳转,这里会有5S盾(自动或者需要手动点击过),
----判断是否在第二个页面,"This Claim Form Must Be Submitted Online Or Postmarked By"如果页面出现这个文本证明到达页面
----页面向下滑动,智能填表,根据list01.txt里的数据
----填写First Name,Last Name,Primary Address,City,Zip,Email Address
----State选择为全州的名称,需要创建映射表,因为数据是缩写的,要点选对应全称的州名
--Purchase Information:
----点击Yes
---随机数量定义:(第1个输入框:随机24,26,28,30,32)
---随机数量定义:(第2个输入框:随机6,7,8,9,10)
---随机数量定义:(第3个输入框:随机3,4,5,6,7,8)
---随机数量定义:(第4个输入框:随机2,3,4,5,6)
---随机数量定义:(第5个输入框:随机2,3,4,5,6)
----5个输入框里只选择一个,填写上面生成的随机数量
有证据模式:需要点击点选框(留位置,后面进行填写)
无证据模式:
----Poppi Product purchased的点选框,根据上面选择的输入框编号选择,(比如上面第一个输入框输入了随机数量,这个点选框就选择第一个)
----Approximate Month of Purchase(随机点选月份)
----Approximate Year of Purchase(随机点选年份)
----Place of Purchase(随机输入:convenience store,Online shop,supermarket,chain supermarket)
----Number of Units Purchased(上面的随机数量)
----Payment Options选择点击框Venmon,等待Email Address单选框出现,出现点击,输入Venmo Email Address和Confirm Email Address
----智能勾选选择框:
----点击"Agree and Submit"按钮
----这里可能需要过5S盾才能到最终页,需要获取VNB-信息,写入listsp_ok.txt里






第一个页面和第二个页面固定的CF盾,可以用"actions.move_to((x,y )).click()"这个方法进行固定点击,
