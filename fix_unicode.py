#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Unicode字符的脚本
"""

import os
import re

def fix_unicode_in_file(file_path):
    """修复文件中的Unicode字符"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 定义需要替换的Unicode字符映射
        unicode_replacements = {
            '🚀': '',
            '📁': '',
            '⚙️': '',
            '📝': '',
            '✅': '',
            '❌': '',
            '⚠️': '',
            '🔧': '',
            '🎬': '',
            '🧹': '',
            '🌍': '',
            '📦': '',
            '🔗': '',
            '🧪': '',
            '🤔': '',
            '🎯': '',
            '📋': '',
            '🔥': '',
            '💡': '',
            '🎉': '',
            '🎊': '',
            '🎈': '',
            '🎁': '',
            '🎀': '',
            '🎂': '',
            '🍰': '',
            '🧁': '',
            '🍪': '',
            '🍫': '',
            '🍬': '',
            '🍭': '',
            '🍮': '',
            '🍯': '',
            '🍼': '',
            '☕': '',
            '🍵': '',
            '🧃': '',
            '🥤': '',
            '🧋': '',
            '🍶': '',
            '🍾': '',
            '🍷': '',
            '🍸': '',
            '🍹': '',
            '🍺': '',
            '🍻': '',
            '🥂': '',
            '🥃': '',
            '🥄': '',
            '🍴': '',
            '🥢': '',
            '🔪': '',
            '🏺': '',
            '🌍': '',
            '🌎': '',
            '🌏': '',
            '🌐': '',
            '🗺️': '',
            '🗾': '',
            '🧭': '',
            '🏔️': '',
            '⛰️': '',
            '🌋': '',
            '🗻': '',
            '🏕️': '',
            '🏖️': '',
            '🏜️': '',
            '🏝️': '',
            '🏞️': '',
            '🏟️': '',
            '🏛️': '',
            '🏗️': '',
            '🧱': '',
            '🏘️': '',
            '🏚️': '',
            '🏠': '',
            '🏡': '',
            '🏢': '',
            '🏣': '',
            '🏤': '',
            '🏥': '',
            '🏦': '',
            '🏧': '',
            '🏨': '',
            '🏩': '',
            '🏪': '',
            '🏫': '',
            '🏬': '',
            '🏭': '',
            '🏯': '',
            '🏰': '',
            '🗼': '',
            '🗽': '',
            '⛪': '',
            '🕌': '',
            '🛕': '',
            '🕍': '',
            '⛩️': '',
            '🕋': '',
            '⛲': '',
            '⛺': '',
            '🌁': '',
            '🌃': '',
            '🏙️': '',
            '🌄': '',
            '🌅': '',
            '🌆': '',
            '🌇': '',
            '🌉': '',
            '♨️': '',
            '🎠': '',
            '🎡': '',
            '🎢': '',
            '💈': '',
            '🎪': '',
        }
        
        # 执行替换
        modified = False
        for unicode_char, replacement in unicode_replacements.items():
            if unicode_char in content:
                content = content.replace(unicode_char, replacement)
                modified = True
                print(f"替换了字符: {unicode_char}")
        
        # 如果有修改，写回文件
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"文件已更新: {file_path}")
            return True
        else:
            print(f"文件无需修改: {file_path}")
            return False
            
    except Exception as e:
        print(f"处理文件失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    files_to_fix = [
        "src/order4.py",
        "main.py",
        "tasks3.py"
    ]
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            print(f"\n处理文件: {file_path}")
            fix_unicode_in_file(file_path)
        else:
            print(f"文件不存在: {file_path}")

if __name__ == "__main__":
    main()
