"""
================================================================================
POPPI PREBIOTIC SODA Settlement 证据生成系统 - 基于模板生成
================================================================================

本程序用于生成POPPI PREBIOTIC SODA Settlement的Amazon订单HTML文件和JPG图片，支持多线程并发调用。

多线程调用方法：

1. 导入模块:
   from src.order4 import generate_poppi_evidence_for_thread

2. 单线程调用:
   generate_poppi_evidence_for_thread(
       row_data=row_data,         # 包含用户信息的行数据
       thread_name="thread_1",    # 可选，线程名称，不传则自动获取
       output_html_dir="generated_orders",  # 可选，HTML输出基础目录
       output_image_dir="generated_images"  # 可选，图片输出基础目录
   )

输出目录结构：
   generated_orders/
   └── thread_1/              # 线程1的HTML文件
       └── poppi_xxx.html
   
   generated_images/
   └── thread_1/              # 线程1的JPG文件
       └── poppi_xxx.jpg

依赖要求：
   - DrissionPage (用于HTML转JPG)
   - PIL/Pillow (图片处理)

注意事项：
   1. 每个线程会创建独立的输出文件夹，避免文件冲突
   2. 一张图片只包含一个产品，通过调整数量达到金额要求
   3. 根据州信息自动选择对应税率
   4. 生成完成后可以清理线程文件夹

================================================================================
"""

import random
from datetime import date, timedelta, datetime
import os
import threading
import shutil
import time
import tempfile
import sys
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 检查依赖
try:
    from DrissionPage import ChromiumPage, ChromiumOptions
    DRISSION_AVAILABLE = True
    print("DrissionPage 已安装并可用")
except ImportError:
    DRISSION_AVAILABLE = False
    print("DrissionPage 未安装，HTML转JPG功能不可用")

try:
    from PIL import Image
    PIL_AVAILABLE = True
    print("PIL/Pillow 已安装并可用")
except ImportError:
    PIL_AVAILABLE = False
    print("PIL/Pillow 未安装，JPG转换功能受限")

# ================================================================================
# POPPI PREBIOTIC SODA 商品信息
# ================================================================================

POPPI_PRODUCTS = [
    {
        "name": "POPPI PREBIOTIC SODA, BEACH PARTY – 12 FL OZ (PACK OF 12)",
        "short_name": "POPPI BEACH PARTY 12 FL OZ (PACK OF 12)",
        "description": "海滩派对益生元苏打水",
        "size": "12 FL OZ (PACK OF 12)",
        "base_price": 27.48
    },
    {
        "name": "POPPI PREBIOTIC SODA, CROWD PLEASERS – 12 FL OZ (PACK OF 12)",
        "short_name": "POPPI CROWD PLEASERS 12 FL OZ (PACK OF 12)",
        "description": "大众喜爱益生元苏打水",
        "size": "12 FL OZ (PACK OF 12)",
        "base_price": 27.48
    },
    {
        "name": "POPPI PREBIOTIC SODA, Variety – 12 Fl Oz (Pack of 12)",
        "short_name": "POPPI Variety 12 Fl Oz (Pack of 12)",
        "description": "混合口味益生元苏打水",
        "size": "12 Fl Oz (Pack of 12)",
        "base_price": 27.88
    }
]

# ================================================================================
# 州级税率数据 - 根据证据说明.txt更新
# ================================================================================

STATE_TAX_RATES = {
    'Alabama': 0.04, 'Alaska': 0.0, 'Arizona': 0.056, 'Arkansas': 0.065,
    'California': 0.0725, 'Colorado': 0.029, 'Connecticut': 0.0635, 'Delaware': 0.0,
    'Florida': 0.06, 'Georgia': 0.04, 'Hawaii': 0.04, 'Idaho': 0.06,
    'Illinois': 0.0625, 'Indiana': 0.07, 'Iowa': 0.06, 'Kansas': 0.065,
    'Kentucky': 0.06, 'Louisiana': 0.0445, 'Maine': 0.055, 'Maryland': 0.06,
    'Massachusetts': 0.0625, 'Michigan': 0.06, 'Minnesota': 0.0688, 'Mississippi': 0.07,
    'Missouri': 0.0423, 'Montana': 0.0, 'Nebraska': 0.055, 'Nevada': 0.0685,
    'New Hampshire': 0.0, 'New Jersey': 0.0663, 'New Mexico': 0.0513, 'New York': 0.04,
    'North Carolina': 0.0475, 'North Dakota': 0.05, 'Ohio': 0.0575, 'Oklahoma': 0.045,
    'Oregon': 0.0, 'Pennsylvania': 0.06, 'Rhode Island': 0.07, 'South Carolina': 0.06,
    'South Dakota': 0.045, 'Tennessee': 0.07, 'Texas': 0.0625, 'Utah': 0.0485,
    'Vermont': 0.06, 'Virginia': 0.043, 'Washington': 0.065, 'West Virginia': 0.06,
    'Wisconsin': 0.05, 'Wyoming': 0.04, 'District of Columbia': 0.06
}

# 州简称到全名的映射
STATE_SHORT_TO_FULL = {
    'AL': 'Alabama', 'AK': 'Alaska', 'AZ': 'Arizona', 'AR': 'Arkansas',
    'CA': 'California', 'CO': 'Colorado', 'CT': 'Connecticut', 'DE': 'Delaware',
    'FL': 'Florida', 'GA': 'Georgia', 'HI': 'Hawaii', 'ID': 'Idaho',
    'IL': 'Illinois', 'IN': 'Indiana', 'IA': 'Iowa', 'KS': 'Kansas',
    'KY': 'Kentucky', 'LA': 'Louisiana', 'ME': 'Maine', 'MD': 'Maryland',
    'MA': 'Massachusetts', 'MI': 'Michigan', 'MN': 'Minnesota', 'MS': 'Mississippi',
    'MO': 'Missouri', 'MT': 'Montana', 'NE': 'Nebraska', 'NV': 'Nevada',
    'NH': 'New Hampshire', 'NJ': 'New Jersey', 'NM': 'New Mexico', 'NY': 'New York',
    'NC': 'North Carolina', 'ND': 'North Dakota', 'OH': 'Ohio', 'OK': 'Oklahoma',
    'OR': 'Oregon', 'PA': 'Pennsylvania', 'RI': 'Rhode Island', 'SC': 'South Carolina',
    'SD': 'South Dakota', 'TN': 'Tennessee', 'TX': 'Texas', 'UT': 'Utah',
    'VT': 'Vermont', 'VA': 'Virginia', 'WA': 'Washington', 'WV': 'West Virginia',
    'WI': 'Wisconsin', 'WY': 'Wyoming', 'DC': 'District of Columbia'
}

# ================================================================================
# 核心功能函数
# ================================================================================

def get_state_tax_rate(state_name):
    """根据州名获取税率信息"""
    if not state_name:
        return None, None, None
    
    state_name = state_name.strip()
    
    # 尝试直接匹配全名
    if state_name in STATE_TAX_RATES:
        tax_rate = STATE_TAX_RATES[state_name]
        state_short = [k for k, v in STATE_SHORT_TO_FULL.items() if v == state_name][0]
        return state_name, state_short, tax_rate
    
    # 尝试匹配简称
    if state_name.upper() in STATE_SHORT_TO_FULL:
        state_full = STATE_SHORT_TO_FULL[state_name.upper()]
        tax_rate = STATE_TAX_RATES[state_full]
        return state_full, state_name.upper(), tax_rate
    
    # 模糊匹配
    for full_name in STATE_TAX_RATES.keys():
        if state_name.lower() in full_name.lower() or full_name.lower() in state_name.lower():
            tax_rate = STATE_TAX_RATES[full_name]
            state_short = [k for k, v in STATE_SHORT_TO_FULL.items() if v == full_name][0]
            return full_name, state_short, tax_rate
    
    return None, None, None

def get_lowest_tax_rate_state():
    """获取税率最低的州"""
    min_tax_state = min(STATE_TAX_RATES.items(), key=lambda x: x[1])
    state_name = min_tax_state[0]
    tax_rate = min_tax_state[1]
    state_short = [k for k, v in STATE_SHORT_TO_FULL.items() if v == state_name][0]
    return state_name, state_short, tax_rate

def calculate_poppi_receipt_item(target_amount_min, target_amount_max, state_name):
    """
    计算单个商品的收据项目，确保总金额在指定范围内
    一张图片只包含一个产品，通过调整数量达到金额要求
    """
    # 获取税率信息
    state_full, state_short, tax_rate = get_state_tax_rate(state_name)
    if tax_rate is None:
        # 如果找不到州信息，使用最低税率州
        state_full, state_short, tax_rate = get_lowest_tax_rate_state()
    
    # 随机选择一个商品
    product = random.choice(POPPI_PRODUCTS)
    
    # 计算需要的数量，确保总金额在目标范围内
    # 公式：(单价 * 数量) * (1 + 税率) = 总金额
    min_quantity = max(1, int(target_amount_min / (product["base_price"] * (1 + tax_rate))))
    max_quantity = int(target_amount_max / (product["base_price"] * (1 + tax_rate))) + 1
    
    # 确保数量在合理范围内
    min_quantity = max(1, min_quantity)
    max_quantity = min(20, max_quantity)  # 最多20个
    
    if min_quantity > max_quantity:
        min_quantity = max_quantity = 10  # 默认数量
    
    quantity = random.randint(min_quantity, max_quantity)
    
    # 计算价格
    unit_price = product["base_price"]
    subtotal = unit_price * quantity
    tax_amount = subtotal * tax_rate
    total_amount = subtotal + tax_amount
    
    # 生成购买日期（2020年1月1日-2024年12月31日）
    start_date = datetime(2020, 1, 1)
    end_date = datetime(2024, 12, 31)
    random_date = start_date + timedelta(
        seconds=random.randint(0, int((end_date - start_date).total_seconds()))
    )
    
    receipt_data = {
        "state": state_full,
        "state_short": state_short,
        "tax_rate": tax_rate,
        "tax_rate_percent": f"{tax_rate * 100:.1f}%",
        "product": product,
        "quantity": quantity,
        "unit_price": unit_price,
        "subtotal": subtotal,
        "tax_amount": tax_amount,
        "total_amount": total_amount,
        "purchase_date": random_date.strftime("%Y-%m-%d"),
        "purchase_date_formatted": random_date.strftime("%B %d, %Y"),
        "store_name": "Amazon",
        "in_target_range": target_amount_min <= total_amount <= target_amount_max
    }
    
    return receipt_data

def generate_multiple_poppi_orders(user_data, num_orders=None):
    """
    生成多个POPPI订单
    
    Args:
        user_data: 用户数据，包含地址等信息
        num_orders: 订单数量，如果为None则随机3-7张
        
    Returns:
        tuple: (订单列表, 总包数)
    """
    if num_orders is None:
        num_orders = random.randint(3, 7)
    
    orders = []
    total_packages = 0  # 总包数
    
    # 获取州信息
    state_name = None
    if user_data and len(user_data) > 6:
        state_raw = str(user_data[6]).strip()
        if state_raw:
            state_name = state_raw
    
    if not state_name:
        state_name = "California"  # 默认州
    
    # 生成第一个订单的日期
    start_date = datetime(2020, 1, 1)
    end_date = datetime(2024, 12, 31)
    first_order_date = start_date + timedelta(
        seconds=random.randint(0, int((end_date - start_date).total_seconds()))
    )
    
    current_date = first_order_date
    
    for i in range(num_orders):
        # 每张订单随机1~3种口味
        product_types = random.randint(1, 3)
        
        order_products = []
        order_total_packages = 0  # 当前订单的总包数
        
        # 为每个订单生成1~3种产品
        used_products = []  # 记录已使用的产品，避免重复
        
        for j in range(product_types):
            # 从未使用的产品中随机选择
            available_products = [p for p in POPPI_PRODUCTS if p not in used_products]
            if not available_products:  # 如果所有产品都用完了，重新开始
                available_products = POPPI_PRODUCTS
                used_products = []
            
            product = random.choice(available_products)
            used_products.append(product)  # 记录已使用的产品
            
            # 每种口味固定1包
            packages = 1
            
            order_products.append({
                "product": product,
                "quantity": packages  # 这里的quantity实际上是包数
            })
            order_total_packages += packages
        
        # 计算订单总金额
        subtotal = sum(item["product"]["base_price"] * item["quantity"] for item in order_products)
        tax_rate = get_state_tax_rate(state_name)[2] or 0.0725  # 默认加州税率
        tax_amount = subtotal * tax_rate
        total_amount = subtotal + tax_amount
        
        # 生成订单数据
        order_data = {
            "order_id": f"113-{random.randint(1000000, 9999999)}-{random.randint(1000000, 9999999)}",
            "order_date": current_date.strftime("%Y-%m-%d"),
            "order_date_formatted": current_date.strftime("%B %d, %Y"),
            "state": state_name,
            "products": order_products,
            "subtotal": subtotal,
            "tax_amount": tax_amount,
            "total_amount": total_amount,
            "total_packages": order_total_packages  # 当前订单的总包数
        }
        
        orders.append(order_data)
        total_packages += order_total_packages
        
        # 计算下一个订单的日期间隔
        # 1包=12瓶，按1天喝1瓶计算，再加上随机天数
        total_bottles = order_total_packages * 12  # 总瓶数
        days_interval = total_bottles + random.randint(1, 10)  # 喝完这些瓶数需要的天数 + 随机天数
        current_date += timedelta(days=days_interval)
        
        # 确保不超过2024年底
        if current_date > end_date:
            current_date = end_date - timedelta(days=random.randint(1, 30))
    
    return orders, total_packages

def generate_random_address():
    """生成随机地址信息"""
    first_names = ["John", "Jane", "Michael", "Sarah", "David", "Emily", "Robert", "Lisa", "William", "Jessica"]
    last_names = ["Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez", "Martinez"]
    
    streets = [
        "123 Main Street", "456 Oak Avenue", "789 Pine Road", "321 Elm Street", "654 Maple Drive",
        "987 Cedar Lane", "147 Birch Way", "258 Willow Street", "369 Cherry Avenue", "741 Ash Road"
    ]
    
    cities = [
        "Springfield", "Franklin", "Georgetown", "Madison", "Clinton", "Arlington", "Centerville",
        "Salem", "Fairview", "Riverside", "Austin", "Burlington", "Dayton", "Oxford", "Newton"
    ]
    
    return {
        "name": f"{random.choice(first_names)} {random.choice(last_names)}",
        "street": random.choice(streets),
        "city": random.choice(cities),
        "state": random.choice(list(STATE_TAX_RATES.keys())),
        "zip": f"{random.randint(10000, 99999)}"
    }

def generate_poppi_html_from_template(order_data, custom_address=None, output_dir=None, thread_name=None):
    """
    基于模板生成POPPI收据HTML文件
    """
    # 确保输出目录存在
    if output_dir is None:
        output_dir = os.path.join(os.getcwd(), "generated_orders")
    
    # 创建线程专用目录
    if thread_name:
        thread_dir = os.path.join(output_dir, thread_name)
    else:
        thread_id = threading.get_ident()
        thread_dir = os.path.join(output_dir, f"thread_{thread_id}")
    
    os.makedirs(thread_dir, exist_ok=True)
    
    # 读取模板文件
    template_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "templates", "order_home.html")
    if not os.path.exists(template_path):
        print(f" 模板文件不存在: {template_path}")
        return None
    
    with open(template_path, 'r', encoding='utf-8') as f:
        template_content = f.read()
    
    # 使用自定义地址或生成随机地址
    if custom_address:
        address = custom_address
    else:
        address = generate_random_address()
    
    # 生成信用卡末四位
    card_last_four = f"{random.randint(1000, 9999)}"
    
    # 准备替换内容
    replacements = {
        # 订单基本信息
        "113-7584150-0938120": order_data["order_id"],
        "Jan 05, 2024": order_data["order_date_formatted"],
        
        # 地址信息
        "AN TUTU": address['name'].split()[0] if ' ' in address['name'] else address['name'],
        "ZHI MA JIE": address['street'],
        "SUGAR LAND, IL 12211": f"{address['city']}, {address.get('state', 'CA')} {address.get('zip', '12345')}",
        
        # 信用卡信息
        "ending in 6523": f"ending in {card_last_four}",
        
        # 供应商信息
        "Supplied by: Other": "Supplied by: Amazon.com",
        
                    # 时间戳 - 使用订单日期，时间随机在11:00~16:00之间
            "1/5/24, 2:30 AM": datetime.strptime(order_data["order_date"], "%Y-%m-%d").strftime("%m/%d/%y, ") + f"{random.randint(11, 16):02d}:{random.randint(0, 59):02d} {'AM' if random.randint(11, 16) < 12 else 'PM'}"
    }
    
    # 执行替换
    modified_content = template_content
    for old, new in replacements.items():
        modified_content = modified_content.replace(old, new)
    
    # 处理商品信息 - 支持多个商品
    # 替换第一个商品
    modified_content = modified_content.replace(
        "<!-- quantity_1 -->1 of: ",
        f"<!-- quantity_1 -->{order_data['products'][0]['quantity']} of: "
    )
    modified_content = modified_content.replace(
        "<!-- product_name_1 -->DEEP RIVER SNACKS Sweet Maui Onion Potato Chips, 2 OZ",
        f"<!-- product_name_1 -->{order_data['products'][0]['product']['name']}"
    )
    # 计算第一个商品的价格
    first_product_price = order_data['products'][0]['product']['base_price'] * order_data['products'][0]['quantity']
    modified_content = modified_content.replace(
        "<!-- price_1 -->$99.48",
        f"<!-- price_1 -->${first_product_price:.2f}"
    )
    
    # 替换第二个商品（如果存在）
    if len(order_data['products']) > 1:
        modified_content = modified_content.replace(
            "<!-- quantity_2 -->",
            f"<!-- quantity_2 -->{order_data['products'][1]['quantity']} of: "
        )
        modified_content = modified_content.replace(
            "<!-- product_name_2 -->",
            f"<!-- product_name_2 -->{order_data['products'][1]['product']['name']}"
        )
        # 计算第二个商品的价格
        second_product_price = order_data['products'][1]['product']['base_price'] * order_data['products'][1]['quantity']
        modified_content = modified_content.replace(
            "<!-- price_2 -->",
            f"<!-- price_2 -->${second_product_price:.2f}"
        )
    else:
        modified_content = modified_content.replace("<!-- quantity_2 -->", "")
        modified_content = modified_content.replace("<!-- product_name_2 -->", "")
        modified_content = modified_content.replace("<!-- price_2 -->", "")
    
    # 替换第三个商品（如果存在）
    if len(order_data['products']) > 2:
        modified_content = modified_content.replace(
            "<!-- quantity_3 -->",
            f"<!-- quantity_3 -->{order_data['products'][2]['quantity']} of: "
        )
        modified_content = modified_content.replace(
            "<!-- product_name_3 -->",
            f"<!-- product_name_3 -->{order_data['products'][2]['product']['name']}"
        )
        # 计算第三个商品的价格
        third_product_price = order_data['products'][2]['product']['base_price'] * order_data['products'][2]['quantity']
        modified_content = modified_content.replace(
            "<!-- price_3 -->",
            f"<!-- price_3 -->${third_product_price:.2f}"
        )
    else:
        modified_content = modified_content.replace("<!-- quantity_3 -->", "")
        modified_content = modified_content.replace("<!-- product_name_3 -->", "")
        modified_content = modified_content.replace("<!-- price_3 -->", "")
    
    # 清空其他商品行
    for i in range(4, 8):
        modified_content = modified_content.replace(f"<!-- quantity_{i} -->", "")
        modified_content = modified_content.replace(f"<!-- product_name_{i} -->", "")
        modified_content = modified_content.replace(f"<!-- price_{i} -->", "")
    
    # 替换金额信息
    modified_content = modified_content.replace(
        '<p class="ft00" style="position:absolute;top:715px;left:803px;white-space:nowrap">$99.48</p>',
        f'<p class="ft00" style="position:absolute;top:715px;left:803px;white-space:nowrap">${order_data["subtotal"]:.2f}</p>'
    )
    modified_content = modified_content.replace(
        '<p class="ft00" style="position:absolute;top:765px;left:803px;white-space:nowrap">$99.48</p>',
        f'<p class="ft00" style="position:absolute;top:765px;left:803px;white-space:nowrap">${order_data["subtotal"]:.2f}</p>'
    )
    modified_content = modified_content.replace(
        '<p class="ft00" style="position:absolute;top:782px;left:803px;white-space:nowrap">$0.00</p>',
        f'<p class="ft00" style="position:absolute;top:782px;left:803px;white-space:nowrap">${order_data["tax_amount"]:.2f}</p>'
    )
    modified_content = modified_content.replace(
        '<p class="ft02" style="position:absolute;top:815px;left:800px;white-space:nowrap">$99.48</p>',
        f'<p class="ft02" style="position:absolute;top:815px;left:800px;white-space:nowrap">${order_data["total_amount"]:.2f}</p>'
    )
    modified_content = modified_content.replace(
        'Order Total: $99.48',
        f'Order Total: ${order_data["total_amount"]:.2f}'
    )
    
    # 移除toolbar脚本和相关代码
    modified_content = modified_content.replace('<script src="https://cdn.jsdelivr.net/npm/@stagewise/toolbar@latest/dist/toolbar.min.js"></script>', '')
    toolbar_script = '''<script>
  // For local file viewing, the toolbar will always be initialized.
  // In a true development environment served by a web server, you would
  // typically use a check like `if (process.env.NODE_ENV === 'development')`.
  window.initToolbar({
      plugins: []
  });
</script>'''
    modified_content = modified_content.replace(toolbar_script, '')
    modified_content = modified_content.replace('<div id="stagewise-toolbar"></div>', '')
    
    #  修复背景图片路径 - 复制背景图片到线程目录并使用相对路径
    background_image_src = os.path.join(os.path.dirname(os.path.dirname(__file__)), "templates", "order_page1.png")
    if os.path.exists(background_image_src):
        # 复制背景图片到线程目录
        dest_image_path = os.path.join(thread_dir, "order_page1.png")
        try:
            shutil.copy2(background_image_src, dest_image_path)
            print(f" 背景图片已复制到线程目录: {dest_image_path}")
            # 使用同目录下的图片
            modified_content = modified_content.replace('src="order_page1.png"', 'src="order_page1.png"')
        except Exception as copy_error:
            print(f" 复制背景图片失败: {copy_error}")
            # 回退到相对路径
            modified_content = modified_content.replace('src="order_page1.png"', 'src="../../templates/order_page1.png"')
    else:
        print(f" 背景图片不存在: {background_image_src}")
        # 使用相对路径作为回退
        modified_content = modified_content.replace('src="order_page1.png"', 'src="../../templates/order_page1.png"')
    
    # 生成文件名
    filename = f"{order_data['order_id']}.html"
    output_path = os.path.join(thread_dir, filename)
    
    # 写入文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print(f" HTML文件生成成功: {output_path}")
    return output_path

def html_to_jpg_with_drission(html_file_path, output_dir=None, jpg_quality=90):
    """
    使用DrissionPage将HTML文件转换为JPG图片 - 多线程安全版本
    采用完全重新创建浏览器实例的策略来解决连接断开问题
    """
    if not DRISSION_AVAILABLE:
        print(" DrissionPage不可用，无法转换为JPG")
        return None

    import threading
    thread_id = threading.current_thread().ident
    print(f"🔄 线程 {thread_id} 开始HTML转JPG: {os.path.basename(html_file_path)}")

    # 🔧 新策略：完整的浏览器实例重试机制
    max_full_retries = 3  # 最多完整重试3次

    for full_retry in range(max_full_retries):
        print(f"🚀 线程 {thread_id} 完整重试 {full_retry + 1}/{max_full_retries}")

        page = None
        temp_dir = None

        try:
            # 🔧 每次重试都创建全新的浏览器配置
            # 设置Chrome选项 - 适配新版本API并禁用所有插件
            options = ChromiumOptions()
            options.headless()
            # 使用新版本的API方法
            options.set_argument('--no-sandbox')
            options.set_argument('--disable-dev-shm-usage')
            options.set_argument('--disable-gpu')
            options.set_argument('--window-size=972,1405')  #  调整为更适合Amazon收据的尺寸
            options.set_argument('--disable-web-security')
            options.set_argument('--allow-running-insecure-content')

            #  禁用所有插件和扩展
            options.set_argument('--disable-extensions')
            options.set_argument('--disable-plugins')
            options.set_argument('--disable-plugins-discovery')
            options.set_argument('--disable-extensions-except')
            options.set_argument('--disable-component-extensions-with-background-pages')
            options.set_argument('--disable-default-apps')
            options.set_argument('--disable-background-timer-throttling')
            options.set_argument('--disable-renderer-backgrounding')
            options.set_argument('--disable-backgrounding-occluded-windows')
            options.set_argument('--disable-ipc-flooding-protection')
            options.set_argument('--disable-client-side-phishing-detection')
            options.set_argument('--disable-popup-blocking')
            options.set_argument('--disable-prompt-on-repost')
            options.set_argument('--disable-sync')
            options.set_argument('--disable-translate')
            options.set_argument('--disable-features=VizDisplayCompositor,TranslateUI')
            options.set_argument('--no-first-run')
            options.set_argument('--no-default-browser-check')
            options.set_argument('--disable-infobars')
            options.set_argument('--disable-notifications')
            options.set_argument('--disable-password-generation')
            options.set_argument('--disable-save-password-bubble')
            options.set_argument('--disable-single-click-autofill')
            options.set_argument('--disable-autofill-keyboard-accessory-view')
            options.set_argument('--disable-full-form-autofill-ios')
            options.set_argument('--disable-autofill-assistant')
            options.set_argument('--disable-autofill-server-communication')

            # 设置用户数据目录为临时目录，确保干净的浏览器环境
            # 🔧 修改：为每个重试创建唯一的临时目录，避免冲突
            temp_dir = tempfile.mkdtemp(prefix=f'drission_thread_{thread_id}_retry_{full_retry}_')
            options.set_argument(f'--user-data-dir={temp_dir}')
            options.set_argument('--disable-extensions-file-access-check')
            options.set_argument('--disable-extensions-http-throttling')
            options.set_argument('--disable-extension-content-verification')

            # 🔧 修改：增加更多稳定性参数
            options.set_argument('--disable-blink-features=AutomationControlled')
            options.set_argument('--disable-features=VizDisplayCompositor')
            options.set_argument('--disable-ipc-flooding-protection')
            options.set_argument('--disable-renderer-backgrounding')
            options.set_argument('--disable-backgrounding-occluded-windows')
            options.set_argument('--disable-background-timer-throttling')
            options.set_argument('--force-color-profile=srgb')
            options.set_argument('--metrics-recording-only')
            options.set_argument('--disable-background-networking')

        # 启动浏览器 - 使用线程锁确保安全启动
        page = None
        max_retries = 3

        with html_to_jpg_with_drission._lock:
            print(f"🔒 线程 {thread_id} 获得浏览器启动锁")

            for attempt in range(max_retries):
                try:
                    print(f"🚀 线程 {thread_id} 启动浏览器 (尝试 {attempt + 1}/{max_retries})...")
                    page = ChromiumPage(options)
                    print(f"✅ 线程 {thread_id} 浏览器启动成功")
                    break
                except Exception as e:
                    print(f"❌ 线程 {thread_id} 浏览器启动失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    if attempt == max_retries - 1:
                        raise e
                    time.sleep(3)  # 增加等待时间到3秒

            # 启动成功后等待一下再释放锁，避免资源竞争
            time.sleep(1)
            print(f"🔓 线程 {thread_id} 释放浏览器启动锁")
        
        # 打开HTML文件 - 增加重试机制
        file_url = f"file:///{html_file_path.replace(os.sep, '/')}"
        print(f"📄 线程 {thread_id} 加载HTML文件: {os.path.basename(html_file_path)}")

        # 页面加载重试机制
        page_load_success = False
        max_load_retries = 3

        for load_attempt in range(max_load_retries):
            try:
                print(f"🔄 线程 {thread_id} 加载页面 (尝试 {load_attempt + 1}/{max_load_retries})...")
                page.get(file_url)

                # 等待页面加载并验证
                time.sleep(3)

                # 验证页面是否正确加载
                current_url = page.url
                if current_url and "file:///" in current_url:
                    print(f"✅ 线程 {thread_id} 页面加载成功")
                    page_load_success = True
                    break
                else:
                    raise Exception(f"页面URL异常: {current_url}")

            except Exception as load_error:
                print(f"❌ 线程 {thread_id} 页面加载失败 (尝试 {load_attempt + 1}/{max_load_retries}): {load_error}")
                if load_attempt < max_load_retries - 1:
                    time.sleep(2)
                else:
                    raise Exception(f"页面加载完全失败: {load_error}")

        if not page_load_success:
            raise Exception("页面加载验证失败")
        
        # 确定输出路径
        if output_dir is None:
            output_dir = os.path.dirname(html_file_path).replace("generated_orders", "generated_images")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成JPG文件名 -  使用订单号码命名
        html_filename = os.path.basename(html_file_path)
        jpg_filename = html_filename.replace('.html', '.jpg')
        jpg_path = os.path.join(output_dir, jpg_filename)
        
        # 截图 - 使用新版本API，增加重试机制
        screenshot_success = False
        max_screenshot_retries = 3

        for screenshot_attempt in range(max_screenshot_retries):
            try:
                print(f"📸 线程 {thread_id} 尝试截图 (第 {screenshot_attempt + 1}/{max_screenshot_retries} 次)...")

                # 强化页面连接检查
                connection_ok = False
                connection_retries = 2

                for conn_attempt in range(connection_retries):
                    try:
                        current_url = page.url
                        page_title = page.title  # 额外检查页面标题
                        print(f"✅ 线程 {thread_id} 页面连接正常，URL: {current_url[:50]}...")
                        connection_ok = True
                        break
                    except Exception as connection_error:
                        print(f"⚠️ 线程 {thread_id} 页面连接异常 (尝试 {conn_attempt + 1}/{connection_retries}): {connection_error}")

                        if conn_attempt < connection_retries - 1:
                            print(f"🔄 线程 {thread_id} 尝试重新建立页面连接...")
                            try:
                                # 重新加载页面
                                page.get(file_url)
                                time.sleep(3)  # 增加等待时间
                            except Exception as reload_error:
                                print(f"❌ 线程 {thread_id} 重新加载页面失败: {reload_error}")

                if not connection_ok:
                    raise Exception("页面连接无法恢复")

                # 尝试截图
                print(f"📷 线程 {thread_id} 开始截图...")
                page.get_screenshot(path=jpg_path, full_page=True)
                screenshot_success = True
                print(f"✅ 线程 {thread_id} 截图成功！")
                break

            except Exception as screenshot_error:
                print(f"❌ 线程 {thread_id} 截图失败 (尝试 {screenshot_attempt + 1}/{max_screenshot_retries}): {screenshot_error}")

                if screenshot_attempt < max_screenshot_retries - 1:
                    print(f"⏳ 线程 {thread_id} 等待5秒后重试...")
                    time.sleep(5)  # 增加等待时间到5秒

                    # 尝试完全重新加载页面
                    try:
                        print(f"🔄 线程 {thread_id} 完全重新加载页面...")
                        page.get(file_url)
                        time.sleep(4)  # 增加页面加载等待时间
                    except Exception as reload_error:
                        print(f"❌ 线程 {thread_id} 重新加载页面失败: {reload_error}")
                else:
                    print(f"💥 线程 {thread_id} 所有截图尝试都失败了")

        if not screenshot_success:
            print(f"💥 线程 {thread_id} 截图完全失败，无法生成JPG文件")
        
        # 关闭浏览器 - 增加安全关闭机制
        try:
            if page:
                print(f"🔒 线程 {thread_id} 关闭浏览器...")
                page.quit()
                time.sleep(2)  # 增加等待时间确保浏览器完全关闭
                print(f"✅ 线程 {thread_id} 浏览器关闭完成")
        except Exception as quit_error:
            print(f"⚠️ 线程 {thread_id} 关闭浏览器时出错: {quit_error}")

        # 清理临时目录 - 增加重试机制
        cleanup_attempts = 3
        for cleanup_attempt in range(cleanup_attempts):
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir, ignore_errors=True)
                    print(f"🧹 线程 {thread_id} 临时目录清理完成: {os.path.basename(temp_dir)}")
                break
            except Exception as cleanup_error:
                print(f"⚠️ 线程 {thread_id} 清理临时目录失败 (尝试 {cleanup_attempt + 1}/{cleanup_attempts}): {cleanup_error}")
                if cleanup_attempt < cleanup_attempts - 1:
                    time.sleep(2)  # 增加等待时间
        
        # 检查文件是否生成成功
        if os.path.exists(jpg_path) and os.path.getsize(jpg_path) > 0:
            file_size = os.path.getsize(jpg_path)
            print(f"✅ 线程 {thread_id} JPG文件生成成功: {os.path.basename(jpg_path)} ({file_size} bytes)")
            return jpg_path
        else:
            print(f"❌ 线程 {thread_id} JPG文件生成失败: {os.path.basename(jpg_path)}")
            return None

    except Exception as e:
        print(f"💥 线程 {thread_id} HTML转JPG失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

        # 确保浏览器被关闭
        try:
            if 'page' in locals() and page:
                page.quit()
        except:
            pass

        # 确保临时目录被清理
        try:
            if 'temp_dir' in locals() and os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)
        except:
            pass

    return None

def generate_poppi_evidence_for_thread(row_data, thread_name=None, output_html_dir=None, output_image_dir=None):
    """
    为当前线程生成POPPI证据
    
    Args:
        row_data: 行数据，包含用户信息
        thread_name: 线程名称
        output_html_dir: HTML输出目录
        output_image_dir: 图片输出目录
        
    Returns:
        dict: 生成的文件信息和总数量
    """
    try:
        # 设置默认目录
        if output_html_dir is None:
            output_html_dir = os.path.join(os.getcwd(), "generated_orders")
        if output_image_dir is None:
            output_image_dir = os.path.join(os.getcwd(), "generated_images")
        
        # 获取或生成线程名称
        if thread_name is None:
            thread_id = threading.get_ident()
            thread_name = f"thread_{thread_id}_{int(time.time())}"
        
        print(f" 开始为线程 {thread_name} 生成POPPI证据")
        
        #  重要：在生成证据前清除线程文件夹，避免证据混乱
        thread_html_dir = os.path.join(output_html_dir, thread_name)
        thread_image_dir = os.path.join(output_image_dir, thread_name)
        
        # 清除HTML文件夹
        if os.path.exists(thread_html_dir):
            try:
                shutil.rmtree(thread_html_dir)
                print(f" 清除HTML文件夹: {thread_html_dir}")
            except Exception as e:
                print(f" 清除HTML文件夹失败: {e}")
        
        # 清除图片文件夹
        if os.path.exists(thread_image_dir):
            try:
                shutil.rmtree(thread_image_dir)
                print(f" 清除图片文件夹: {thread_image_dir}")
            except Exception as e:
                print(f" 清除图片文件夹失败: {e}")
        
        # 从row_data提取州信息
        state_name = None
        if row_data and len(row_data) > 6:
            state_raw = str(row_data[6]).strip()
            if state_raw:
                state_name = state_raw
        
        if not state_name:
            state_name = "California"  # 默认州
        
        # 提取地址信息
        custom_address = None
        if row_data and len(row_data) > 5:
            try:
                custom_address = {
                    'name': f"{row_data[0]} {row_data[1]}".strip() if len(row_data) > 1 else "John Doe",
                    'street': str(row_data[2]).strip() if len(row_data) > 2 else "123 Main St",
                    'city': str(row_data[3]).strip() if len(row_data) > 3 else "City",
                    'state': str(row_data[4]).strip() if len(row_data) > 4 else state_name,
                    'zip': str(row_data[5]).strip() if len(row_data) > 5 else "12345"
                }
            except:
                custom_address = None
        
        print(f" 使用州: {state_name}")
        
        # 生成多个订单
        orders, total_packages = generate_multiple_poppi_orders(row_data)
        
        print(f" 总包数: {total_packages}")
        
        # 生成HTML文件
        html_files = []
        for i, order_data in enumerate(orders):
            print(f" 生成订单 {i+1}/{len(orders)}")
            html_file = generate_poppi_html_from_template(
                order_data=order_data,
                custom_address=custom_address,
                output_dir=output_html_dir,
                thread_name=thread_name
            )
            if not html_file:
                print(f" 订单 {i+1} HTML文件生成失败")
                return None
            html_files.append(html_file)
        
        # 转换为JPG
        thread_image_dir = os.path.join(output_image_dir, thread_name)
        jpg_files = []
        for html_file in html_files:
            jpg_file = html_to_jpg_with_drission(html_file, thread_image_dir)
            if not jpg_file:
                print(f" 订单 HTML文件 {html_file} 转JPG失败")
                return None
            jpg_files.append(jpg_file)
        
        # 返回结果
        result = {
            "thread_name": thread_name,
            "state": state_name,
            "orders": orders,
            "total_packages": total_packages,  # 返回总包数给tasks3.py
            "files": {
                "html_files": html_files,
                "jpg_files": jpg_files
            }
        }
        
        print(f" POPPI证据生成完成! 总包数: {total_packages}")
        return result
            
    except Exception as e:
        print(f" 生成POPPI证据时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def cleanup_thread_evidence_files(thread_name, output_html_dir=None, output_image_dir=None):
    """
    清理指定线程的证据文件
    
    Args:
        thread_name: 线程名称
        output_html_dir: HTML目录
        output_image_dir: 图片目录
    """
    try:
        if output_html_dir is None:
            output_html_dir = os.path.join(os.getcwd(), "generated_orders")
        if output_image_dir is None:
            output_image_dir = os.path.join(os.getcwd(), "generated_images")
        
        # 清理HTML文件夹
        html_thread_dir = os.path.join(output_html_dir, thread_name)
        if os.path.exists(html_thread_dir):
            shutil.rmtree(html_thread_dir)
            print(f" 已清理HTML文件夹: {html_thread_dir}")
        
        # 清理图片文件夹
        image_thread_dir = os.path.join(output_image_dir, thread_name)
        if os.path.exists(image_thread_dir):
            shutil.rmtree(image_thread_dir)
            print(f" 已清理图片文件夹: {image_thread_dir}")
        
        print(f" 线程 {thread_name} 的证据文件已清理完成")
        
    except Exception as e:
        print(f" 清理线程证据文件失败: {e}")

def test_poppi_evidence_generation():
    """
    测试POPPI证据生成功能
    """
    print(" 开始测试POPPI证据生成功能...")
    
    # 使用证据说明.txt中的测试数据
    test_row_data = [
        "ANDREW",      # A列 - 名
        "MARIAUX",     # B列 - 姓
        "6000 ELDORADO PKWY APT 1722", # C列 - 街道
        "FRISCO",      # D列 - 城市
        "TX",          # E列 - 州简称
        "75033",       # F列 - 邮编
        "Texas",       # 第7列 - 完整州名
        "11/8/1984",   # 生日
        "451836266",   # SSN
        "7407980753",  # 电话
        "<EMAIL>"  # 邮箱
    ]
    
    # 生成证据
    result = generate_poppi_evidence_for_thread(
        row_data=test_row_data,
        thread_name="test_thread"
    )
    
    if result:
        print(" 测试成功!")
        print(f"   总包数: {result['total_packages']}")
        print(f"   订单数量: {len(result['orders'])}")
        print(f"   HTML文件: {result['files']['html_files']}")
        print(f"   JPG文件: {result['files']['jpg_files']}")
        
        # 显示每个订单的详细信息
        for i, order in enumerate(result['orders']):
            print(f"   订单 {i+1}: {order['order_date']} - ${order['total_amount']:.2f} - {order['total_packages']}包")
        
        # 询问是否清理测试文件
        cleanup_choice = input(" 是否清理测试文件? (y/n): ").lower()
        if cleanup_choice == 'y':
            cleanup_thread_evidence_files("test_thread")
    else:
        print(" 测试失败!")

if __name__ == "__main__":
    # 运行测试
    test_poppi_evidence_generation()