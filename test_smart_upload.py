#!/usr/bin/env python3
"""
测试智能化上传功能
"""

import os
import time
import glob

def test_smart_upload_logic():
    """测试智能化上传逻辑"""
    print("测试智能化上传逻辑...")
    
    # 模拟证据文件
    evidence_dir = "generated_images/thread_1"
    jpg_files = glob.glob(os.path.join(evidence_dir, "*.jpg"))
    
    if not jpg_files:
        print(f"错误: 在 {evidence_dir} 中找不到JPG文件")
        return False
    
    print(f"找到 {len(jpg_files)} 个证据文件:")
    for i, file in enumerate(jpg_files, 1):
        print(f"  {i}. {os.path.basename(file)}")
    
    # 模拟上传任务创建
    available_inputs = list(range(1, 11))  # file1 到 file10
    files_to_upload = jpg_files[:min(len(jpg_files), len(available_inputs))]
    
    upload_tasks = []
    for file_index, jpg_file in enumerate(files_to_upload):
        input_number = available_inputs[file_index]
        upload_tasks.append({
            'file_path': jpg_file,
            'input_number': input_number,
            'file_id': f"file{input_number}",
            'uploaded': False,
            'attempts': 0
        })
    
    print(f"\n创建了 {len(upload_tasks)} 个上传任务:")
    for task in upload_tasks:
        print(f"  {task['file_id']}: {os.path.basename(task['file_path'])}")
    
    # 模拟智能上传循环
    max_rounds = 3
    for round_num in range(1, max_rounds + 1):
        print(f"\n=== 模拟第 {round_num} 轮上传 ===")
        
        # 找出未成功上传的任务
        pending_tasks = [task for task in upload_tasks if not task['uploaded']]
        if not pending_tasks:
            print("所有文件已成功上传！")
            break
            
        print(f"需要上传 {len(pending_tasks)} 个文件")
        
        # 模拟上传过程
        for task in pending_tasks:
            task['attempts'] += 1
            print(f"  模拟上传 {task['file_id']}: {os.path.basename(task['file_path'])} (第 {task['attempts']} 次尝试)")
            
            # 模拟上传成功率（第一轮70%，第二轮90%，第三轮100%）
            success_rate = 0.7 + (round_num - 1) * 0.15
            import random
            if random.random() < success_rate:
                task['uploaded'] = True
                print(f"    上传成功")
            else:
                print(f"    上传失败，将在下一轮重试")
        
        # 统计结果
        successful_count = sum(1 for task in upload_tasks if task['uploaded'])
        print(f"第 {round_num} 轮完成: {successful_count}/{len(upload_tasks)} 个文件成功上传")
        
        if successful_count == len(upload_tasks):
            break
    
    # 最终结果
    final_successful = sum(1 for task in upload_tasks if task['uploaded'])
    print(f"\n最终上传结果: {final_successful}/{len(upload_tasks)} 个文件成功上传")
    
    # 显示失败的文件
    failed_tasks = [task for task in upload_tasks if not task['uploaded']]
    if failed_tasks:
        print("上传失败的文件:")
        for task in failed_tasks:
            print(f"  {task['file_id']}: {os.path.basename(task['file_path'])} (尝试 {task['attempts']} 次)")
    
    return final_successful > 0

if __name__ == "__main__":
    test_smart_upload_logic()
