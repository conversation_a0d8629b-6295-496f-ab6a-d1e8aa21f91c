"""
================================================================================
Wheat Thins Settlement 证据生成系统 - 基于模板生成
================================================================================

本程序用于生成Wheat Thins Settlement的Amazon订单HTML文件和JPG图片，支持多线程并发调用。

🚀 多线程调用方法：

1. 导入模块:
   from src.order4 import generate_wheat_thins_evidence_for_thread

2. 单线程调用:
   generate_wheat_thins_evidence_for_thread(
       row_data=row_data,         # 包含用户信息的行数据
       thread_name="thread_1",    # 可选，线程名称，不传则自动获取
       output_html_dir="generated_orders",  # 可选，HTML输出基础目录
       output_image_dir="generated_images"  # 可选，图片输出基础目录
   )

📁 输出目录结构：
   generated_orders/
   └── thread_1/              # 线程1的HTML文件
       └── wheat_thins_xxx.html
   
   generated_images/
   └── thread_1/              # 线程1的JPG文件
       └── wheat_thins_xxx.jpg

⚙️ 依赖要求：
   - DrissionPage (用于HTML转JPG)
   - PIL/Pillow (图片处理)

📝 注意事项：
   1. 每个线程会创建独立的输出文件夹，避免文件冲突
   2. 一张图片只包含一个产品，通过调整数量达到金额要求
   3. 根据州信息自动选择对应税率
   4. 生成完成后可以清理线程文件夹

================================================================================
"""

import random
from datetime import date, timedelta, datetime
import os
import threading
import shutil
import time
import tempfile
import sys
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 检查依赖
try:
    from DrissionPage import ChromiumPage, ChromiumOptions
    DRISSION_AVAILABLE = True
    print("✅ DrissionPage 已安装并可用")
except ImportError:
    DRISSION_AVAILABLE = False
    print("❌ DrissionPage 未安装，HTML转JPG功能不可用")

try:
    from PIL import Image
    PIL_AVAILABLE = True
    print("✅ PIL/Pillow 已安装并可用")
except ImportError:
    PIL_AVAILABLE = False
    print("❌ PIL/Pillow 未安装，JPG转换功能受限")

# ================================================================================
# Wheat Thins 商品信息
# ================================================================================

WHEAT_THINS_PRODUCTS = [
    {
        "name": "Wheat Thins Original Snacks, Whole Grain Wheat Crackers, Snack Crackers, 8.5 oz",
        "short_name": "Wheat Thins Original Whole Grain 8.5 oz",
        "description": "原味全谷物薄饼干",
        "size": "8.5 oz",
        "base_price": 11.99
    },
    {
        "name": "Wheat Thins BIG Snacks, Whole Grain Wheat Crackers, Snack Crackers, 8 oz",
        "short_name": "Wheat Thins BIG Snacks 8 oz",
        "description": "大块全谷物薄饼干",
        "size": "8 oz",
        "base_price": 11.99
    },
    {
        "name": "Wheat Thins Hint of Salt Low Sodium Snacks, Whole Grain Wheat Crackers, 8.5 oz",
        "short_name": "Wheat Thins Hint of Salt 8.5 oz",
        "description": "低钠全谷物薄饼干",
        "size": "8.5 oz",
        "base_price": 9.89
    },
    {
        "name": "Wheat Thins Sundried Tomato & Basil Snacks, Whole Grain Wheat Crackers, Bulk Snack Crackers, 8.5 oz",
        "short_name": "Wheat Thins Sundried Tomato & Basil 8.5 oz",
        "description": "番茄罗勒全谷物薄饼干",
        "size": "8.5 oz",
        "base_price": 12.63
    },
    {
        "name": "Wheat Thins Reduced Fat Snacks, Whole Grain Wheat Crackers, Bulk Snack Crackers, 8 oz",
        "short_name": "Wheat Thins Spicy Sweet Chili 8.5 oz",
        "description": "香辣甜椒全谷物薄饼干",
        "size": "8.5 oz",
        "base_price": 2.97
    }
]

# ================================================================================
# 州级税率数据
# ================================================================================

STATE_TAX_RATES = {
    'Alabama': 0.11, 'Alaska': 0.075, 'Arizona': 0.112, 'Arkansas': 0.115,
    'California': 0.09, 'Colorado': 0.112, 'Connecticut': 0.0635, 'Delaware': 0.0,
    'Florida': 0.07, 'Georgia': 0.08, 'Hawaii': 0.045, 'Idaho': 0.06,
    'Illinois': 0.0825, 'Indiana': 0.07, 'Iowa': 0.06, 'Kansas': 0.065,
    'Kentucky': 0.06, 'Louisiana': 0.0945, 'Maine': 0.055, 'Maryland': 0.06,
    'Massachusetts': 0.0625, 'Michigan': 0.06, 'Minnesota': 0.07625, 'Mississippi': 0.07,
    'Missouri': 0.0823, 'Montana': 0.0, 'Nebraska': 0.055, 'Nevada': 0.0825,
    'New Hampshire': 0.0, 'New Jersey': 0.06625, 'New Mexico': 0.0513, 'New York': 0.08,
    'North Carolina': 0.0475, 'North Dakota': 0.05, 'Ohio': 0.0575, 'Oklahoma': 0.045,
    'Oregon': 0.0, 'Pennsylvania': 0.06, 'Rhode Island': 0.07, 'South Carolina': 0.06,
    'South Dakota': 0.045, 'Tennessee': 0.07, 'Texas': 0.0625, 'Utah': 0.061,
    'Vermont': 0.06, 'Virginia': 0.053, 'Washington': 0.065, 'West Virginia': 0.06,
    'Wisconsin': 0.05, 'Wyoming': 0.04
}

# 州简称到全名的映射
STATE_SHORT_TO_FULL = {
    'AL': 'Alabama', 'AK': 'Alaska', 'AZ': 'Arizona', 'AR': 'Arkansas',
    'CA': 'California', 'CO': 'Colorado', 'CT': 'Connecticut', 'DE': 'Delaware',
    'FL': 'Florida', 'GA': 'Georgia', 'HI': 'Hawaii', 'ID': 'Idaho',
    'IL': 'Illinois', 'IN': 'Indiana', 'IA': 'Iowa', 'KS': 'Kansas',
    'KY': 'Kentucky', 'LA': 'Louisiana', 'ME': 'Maine', 'MD': 'Maryland',
    'MA': 'Massachusetts', 'MI': 'Michigan', 'MN': 'Minnesota', 'MS': 'Mississippi',
    'MO': 'Missouri', 'MT': 'Montana', 'NE': 'Nebraska', 'NV': 'Nevada',
    'NH': 'New Hampshire', 'NJ': 'New Jersey', 'NM': 'New Mexico', 'NY': 'New York',
    'NC': 'North Carolina', 'ND': 'North Dakota', 'OH': 'Ohio', 'OK': 'Oklahoma',
    'OR': 'Oregon', 'PA': 'Pennsylvania', 'RI': 'Rhode Island', 'SC': 'South Carolina',
    'SD': 'South Dakota', 'TN': 'Tennessee', 'TX': 'Texas', 'UT': 'Utah',
    'VT': 'Vermont', 'VA': 'Virginia', 'WA': 'Washington', 'WV': 'West Virginia',
    'WI': 'Wisconsin', 'WY': 'Wyoming'
}

# ================================================================================
# 核心功能函数
# ================================================================================

def get_state_tax_rate(state_name):
    """根据州名获取税率信息"""
    if not state_name:
        return None, None, None
    
    state_name = state_name.strip()
    
    # 尝试直接匹配全名
    if state_name in STATE_TAX_RATES:
        tax_rate = STATE_TAX_RATES[state_name]
        state_short = [k for k, v in STATE_SHORT_TO_FULL.items() if v == state_name][0]
        return state_name, state_short, tax_rate
    
    # 尝试匹配简称
    if state_name.upper() in STATE_SHORT_TO_FULL:
        state_full = STATE_SHORT_TO_FULL[state_name.upper()]
        tax_rate = STATE_TAX_RATES[state_full]
        return state_full, state_name.upper(), tax_rate
    
    # 模糊匹配
    for full_name in STATE_TAX_RATES.keys():
        if state_name.lower() in full_name.lower() or full_name.lower() in state_name.lower():
            tax_rate = STATE_TAX_RATES[full_name]
            state_short = [k for k, v in STATE_SHORT_TO_FULL.items() if v == full_name][0]
            return full_name, state_short, tax_rate
    
    return None, None, None

def get_lowest_tax_rate_state():
    """获取税率最低的州"""
    min_tax_state = min(STATE_TAX_RATES.items(), key=lambda x: x[1])
    state_name = min_tax_state[0]
    tax_rate = min_tax_state[1]
    state_short = [k for k, v in STATE_SHORT_TO_FULL.items() if v == state_name][0]
    return state_name, state_short, tax_rate

def calculate_wheat_thins_receipt_item(target_amount_min, target_amount_max, state_name):
    """
    计算单个商品的收据项目，确保总金额在指定范围内
    一张图片只包含一个产品，通过调整数量达到金额要求
    """
    # 获取税率信息
    state_full, state_short, tax_rate = get_state_tax_rate(state_name)
    if tax_rate is None:
        # 如果找不到州信息，使用最低税率州
        state_full, state_short, tax_rate = get_lowest_tax_rate_state()
    
    # 随机选择一个商品
    product = random.choice(WHEAT_THINS_PRODUCTS)
    
    # 计算需要的数量，确保总金额在目标范围内
    # 公式：(单价 * 数量) * (1 + 税率) = 总金额
    min_quantity = max(1, int(target_amount_min / (product["base_price"] * (1 + tax_rate))))
    max_quantity = int(target_amount_max / (product["base_price"] * (1 + tax_rate))) + 1
    
    # 确保数量在合理范围内
    min_quantity = max(1, min_quantity)
    max_quantity = min(20, max_quantity)  # 最多20个
    
    if min_quantity > max_quantity:
        min_quantity = max_quantity = 10  # 默认数量
    
    quantity = random.randint(min_quantity, max_quantity)
    
    # 计算价格
    unit_price = product["base_price"]
    subtotal = unit_price * quantity
    tax_amount = subtotal * tax_rate
    total_amount = subtotal + tax_amount
    
    # 生成购买日期（2018年10月14日-2025年5月1日）
    start_date = datetime(2018, 10, 14)
    end_date = datetime(2025, 5, 1)
    random_date = start_date + timedelta(
        seconds=random.randint(0, int((end_date - start_date).total_seconds()))
    )
    
    receipt_data = {
        "state": state_full,
        "state_short": state_short,
        "tax_rate": tax_rate,
        "tax_rate_percent": f"{tax_rate * 100:.1f}%",
        "product": product,
        "quantity": quantity,
        "unit_price": unit_price,
        "subtotal": subtotal,
        "tax_amount": tax_amount,
        "total_amount": total_amount,
        "purchase_date": random_date.strftime("%Y-%m-%d"),
        "purchase_date_formatted": random_date.strftime("%B %d, %Y"),
        "store_name": "Amazon",
        "in_target_range": target_amount_min <= total_amount <= target_amount_max
    }
    
    return receipt_data

def generate_random_address():
    """生成随机地址信息"""
    first_names = ["John", "Jane", "Michael", "Sarah", "David", "Emily", "Robert", "Lisa", "William", "Jessica"]
    last_names = ["Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez", "Martinez"]
    
    streets = [
        "123 Main Street", "456 Oak Avenue", "789 Pine Road", "321 Elm Street", "654 Maple Drive",
        "987 Cedar Lane", "147 Birch Way", "258 Willow Street", "369 Cherry Avenue", "741 Ash Road"
    ]
    
    cities = [
        "Springfield", "Franklin", "Georgetown", "Madison", "Clinton", "Arlington", "Centerville",
        "Salem", "Fairview", "Riverside", "Austin", "Burlington", "Dayton", "Oxford", "Newton"
    ]
    
    return {
        "name": f"{random.choice(first_names)} {random.choice(last_names)}",
        "street": random.choice(streets),
        "city": random.choice(cities),
        "state": random.choice(list(STATE_TAX_RATES.keys())),
        "zip": f"{random.randint(10000, 99999)}"
    }

def generate_wheat_thins_html_from_template(receipt_data, custom_address=None, output_dir=None, thread_name=None):
    """
    基于模板生成Wheat Thins收据HTML文件
    """
    # 确保输出目录存在
    if output_dir is None:
        output_dir = os.path.join(os.getcwd(), "generated_orders")
    
    # 创建线程专用目录
    if thread_name:
        thread_dir = os.path.join(output_dir, thread_name)
    else:
        thread_id = threading.get_ident()
        thread_dir = os.path.join(output_dir, f"thread_{thread_id}")
    
    os.makedirs(thread_dir, exist_ok=True)
    
    # 读取模板文件
    template_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "templates", "order_home.html")
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return None
    
    with open(template_path, 'r', encoding='utf-8') as f:
        template_content = f.read()
    
    # 生成订单ID
    order_id = f"113-{random.randint(1000000, 9999999)}-{random.randint(1000000, 9999999)}"
    
    # 🔧 将订单ID添加到receipt_data中，以便后续使用
    receipt_data['order_id'] = order_id
    
    # 使用自定义地址或生成随机地址
    if custom_address:
        address = custom_address
    else:
        address = generate_random_address()
    
    # 生成信用卡末四位
    card_last_four = f"{random.randint(1000, 9999)}"
    
    # 准备替换内容
    replacements = {
        # 订单基本信息
        "113-7584150-0938120": order_id,
        "Jan 05, 2024": receipt_data["purchase_date_formatted"],
        # "$99.48": f"${receipt_data['total_amount']:.2f}",  # 🔧 注释掉通用替换，使用精确位置替换
        
        # 商品信息 - 只有一个商品
        "<!-- quantity_1 -->1 of: ": f"{receipt_data['quantity']} of: ",
        "<!-- product_name_1 -->DEEP RIVER SNACKS Sweet Maui Onion Potato Chips, 2 OZ": receipt_data['product']['name'],
        "<!-- price_1 -->$99.48": f"${receipt_data['subtotal']:.2f}",  # 🔧 修复：这里应该是商品小计金额，不是总金额
        
        # 清空其他商品行
        "<!-- quantity_2 -->": "",
        "<!-- product_name_2 -->": "",
        "<!-- price_2 -->": "",
        "<!-- quantity_3 -->": "",
        "<!-- product_name_3 -->": "",
        "<!-- price_3 -->": "",
        "<!-- quantity_4 -->": "",
        "<!-- product_name_4 -->": "",
        "<!-- price_4 -->": "",
        "<!-- quantity_5 -->": "",
        "<!-- product_name_5 -->": "",
        "<!-- price_5 -->": "",
        "<!-- quantity_6 -->": "",
        "<!-- product_name_6 -->": "",
        "<!-- price_6 -->": "",
        "<!-- quantity_7 -->": "",
        "<!-- product_name_7 -->": "",
        "<!-- price_7 -->": "",
        
        # 地址信息
        "AN TUTU": address['name'].split()[0] if ' ' in address['name'] else address['name'],
        "ZHI MA JIE": address['street'],
        "SUGAR LAND, IL 12211": f"{address['city']}, {address.get('state', 'CA')} {address.get('zip', '12345')}",
        
        # 金额信息 - 明确指定每个金额的替换
        "Item(s) Subtotal:": "Item(s) Subtotal:",
        "Total before tax:": "Total before tax:",
        "Estimated tax to be collected:": "Estimated tax to be collected:",
        "Grand Total:": "Grand Total:",
        
        # 信用卡信息
        "ending in 6523": f"ending in {card_last_four}",
        
        # 时间戳
        "1/5/24, 2:30 AM": datetime.now().strftime("%m/%d/%y, %I:%M %p")
    }
    
    # 执行替换
    modified_content = template_content
    for old, new in replacements.items():
        modified_content = modified_content.replace(old, new)
    
    # 🔧 修复金额显示 - 分步精确替换，避免重复替换
    print(f"🔍 税务计算验证:")
    print(f"   税前金额: ${receipt_data['subtotal']:.2f}")
    print(f"   税金: ${receipt_data['tax_amount']:.2f}")
    print(f"   总金额: ${receipt_data['total_amount']:.2f}")
    print(f"   验证: {receipt_data['subtotal']:.2f} + {receipt_data['tax_amount']:.2f} = {receipt_data['subtotal'] + receipt_data['tax_amount']:.2f}")
    
    # 🔧 正确的替换顺序：先替换税前金额，再替换总金额，最后替换税金
    
    # 1. 首先替换需要显示税前金额的位置（避免被后续的总金额替换覆盖）
    print("🔧 开始按正确顺序替换金额...")
    
    # Item(s) Subtotal (税前金额) - 添加调试
    target_1 = '<p class="ft00" style="position:absolute;top:715px;left:803px;white-space:nowrap">$99.48</p>'
    replacement_1 = f'<p class="ft00" style="position:absolute;top:715px;left:803px;white-space:nowrap">${receipt_data["subtotal"]:.2f}</p>'
    if target_1 in modified_content:
        modified_content = modified_content.replace(target_1, replacement_1)
        print(f"✅ Item(s) Subtotal: $99.48 -> ${receipt_data['subtotal']:.2f} (成功替换)")
    else:
        print(f"❌ Item(s) Subtotal: 找不到目标字符串")
        print(f"🔍 在content中搜索top:715px:")
        lines = modified_content.split('\n')
        for i, line in enumerate(lines):
            if 'top:715px' in line:
                print(f"  第{i+1}行: {line.strip()}")
    
    # Total before tax (税前金额) - 添加调试
    target_2 = '<p class="ft00" style="position:absolute;top:765px;left:803px;white-space:nowrap">$99.48</p>'
    replacement_2 = f'<p class="ft00" style="position:absolute;top:765px;left:803px;white-space:nowrap">${receipt_data["subtotal"]:.2f}</p>'
    if target_2 in modified_content:
        modified_content = modified_content.replace(target_2, replacement_2)
        print(f"✅ Total before tax: $99.48 -> ${receipt_data['subtotal']:.2f} (成功替换)")
    else:
        print(f"❌ Total before tax: 找不到目标字符串")
        print(f"🔍 在content中搜索top:765px:")
        lines = modified_content.split('\n')
        for i, line in enumerate(lines):
            if 'top:765px' in line:
                print(f"  第{i+1}行: {line.strip()}")
    
    # 2. 然后替换税金 ($0.00 -> 实际税金)
    modified_content = modified_content.replace(
        '<p class="ft00" style="position:absolute;top:782px;left:803px;white-space:nowrap">$0.00</p>',
        f'<p class="ft00" style="position:absolute;top:782px;left:803px;white-space:nowrap">${receipt_data["tax_amount"]:.2f}</p>'
    )
    print(f"✅ Estimated tax: $0.00 -> ${receipt_data['tax_amount']:.2f}")
    
    # 3. 最后替换需要显示总金额的位置（现在只剩下需要显示总金额的$99.48了）
    # Grand Total (总金额)
    modified_content = modified_content.replace(
        '<p class="ft02" style="position:absolute;top:815px;left:800px;white-space:nowrap">$99.48</p>',
        f'<p class="ft02" style="position:absolute;top:815px;left:800px;white-space:nowrap">${receipt_data["total_amount"]:.2f}</p>'
    )
    print(f"✅ Grand Total: $99.48 -> ${receipt_data['total_amount']:.2f}")
    
    # Order Total (总金额)
    modified_content = modified_content.replace(
        'Order Total: $99.48',
        f'Order Total: ${receipt_data["total_amount"]:.2f}'
    )
    print(f"✅ Order Total: $99.48 -> ${receipt_data['total_amount']:.2f}")
    
    # 商品价格 (总金额)
    modified_content = modified_content.replace(
        '<!-- price_1 -->$99.48',
        f'<!-- price_1 -->${receipt_data["total_amount"]:.2f}'
    )
    print(f"✅ 商品价格: $99.48 -> ${receipt_data['total_amount']:.2f}")
    
    print(f"✅ 金额替换完成")
    print(f"   Item(s) Subtotal: ${receipt_data['subtotal']:.2f}")
    print(f"   Total before tax: ${receipt_data['subtotal']:.2f}")
    print(f"   Estimated tax: ${receipt_data['tax_amount']:.2f}")
    print(f"   Grand Total: ${receipt_data['total_amount']:.2f}")
    print(f"   商品价格: ${receipt_data['total_amount']:.2f}")
    
    # 🔧 修复背景图片路径 - 复制背景图片到线程目录并使用相对路径
    background_image_src = os.path.join("templates", "order_page1.png")
    if os.path.exists(background_image_src):
        # 复制背景图片到线程目录
        dest_image_path = os.path.join(thread_dir, "order_page1.png")
        try:
            shutil.copy2(background_image_src, dest_image_path)
            print(f"✅ 背景图片已复制到线程目录: {dest_image_path}")
            # 使用同目录下的图片
            modified_content = modified_content.replace('src="order_page1.png"', 'src="order_page1.png"')
        except Exception as copy_error:
            print(f"⚠️ 复制背景图片失败: {copy_error}")
            # 回退到相对路径
            modified_content = modified_content.replace('src="order_page1.png"', 'src="../../templates/order_page1.png"')
    else:
        print(f"⚠️ 背景图片不存在: {background_image_src}")
        # 使用相对路径作为回退
        modified_content = modified_content.replace('src="order_page1.png"', 'src="../../templates/order_page1.png"')
    
    # 🔧 移除toolbar脚本和相关代码，防止显示插件图标
    modified_content = modified_content.replace('<script src="https://cdn.jsdelivr.net/npm/@stagewise/toolbar@latest/dist/toolbar.min.js"></script>', '')
    # 移除toolbar初始化脚本
    toolbar_script = '''<script>
  // For local file viewing, the toolbar will always be initialized.
  // In a true development environment served by a web server, you would
  // typically use a check like `if (process.env.NODE_ENV === 'development')`.
  window.initToolbar({
      plugins: []
  });
</script>'''
    modified_content = modified_content.replace(toolbar_script, '')
    # 移除toolbar div
    modified_content = modified_content.replace('<div id="stagewise-toolbar"></div>', '')
    
    # 生成文件名 - 🔧 直接使用订单号码命名
    filename = f"{receipt_data['order_id']}.html"
    output_path = os.path.join(thread_dir, filename)
    
    # 写入文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print(f"✅ HTML文件生成成功: {output_path}")
    return output_path

def html_to_jpg_with_drission(html_file_path, output_dir=None, jpg_quality=90):
    """
    使用DrissionPage将HTML文件转换为JPG图片
    """
    if not DRISSION_AVAILABLE:
        print("❌ DrissionPage不可用，无法转换为JPG")
        return None
    
    try:
        # 设置Chrome选项 - 适配新版本API并禁用所有插件
        options = ChromiumOptions()
        options.headless()
        # 使用新版本的API方法
        options.set_argument('--no-sandbox')
        options.set_argument('--disable-dev-shm-usage')
        options.set_argument('--disable-gpu')
        options.set_argument('--window-size=972,1405')  # 🔧 调整为更适合Amazon收据的尺寸
        options.set_argument('--disable-web-security')
        options.set_argument('--allow-running-insecure-content')
        
        # 🔧 禁用所有插件和扩展
        options.set_argument('--disable-extensions')
        options.set_argument('--disable-plugins')
        options.set_argument('--disable-plugins-discovery')
        options.set_argument('--disable-extensions-except')
        options.set_argument('--disable-component-extensions-with-background-pages')
        options.set_argument('--disable-default-apps')
        options.set_argument('--disable-background-timer-throttling')
        options.set_argument('--disable-renderer-backgrounding')
        options.set_argument('--disable-backgrounding-occluded-windows')
        options.set_argument('--disable-ipc-flooding-protection')
        options.set_argument('--disable-client-side-phishing-detection')
        options.set_argument('--disable-popup-blocking')
        options.set_argument('--disable-prompt-on-repost')
        options.set_argument('--disable-sync')
        options.set_argument('--disable-translate')
        options.set_argument('--disable-features=VizDisplayCompositor,TranslateUI')
        options.set_argument('--no-first-run')
        options.set_argument('--no-default-browser-check')
        options.set_argument('--disable-infobars')
        options.set_argument('--disable-notifications')
        options.set_argument('--disable-password-generation')
        options.set_argument('--disable-save-password-bubble')
        options.set_argument('--disable-single-click-autofill')
        options.set_argument('--disable-autofill-keyboard-accessory-view')
        options.set_argument('--disable-full-form-autofill-ios')
        options.set_argument('--disable-autofill-assistant')
        options.set_argument('--disable-autofill-server-communication')
        
        # 设置用户数据目录为临时目录，确保干净的浏览器环境
        temp_dir = tempfile.mkdtemp()
        options.set_argument(f'--user-data-dir={temp_dir}')
        options.set_argument('--disable-extensions-file-access-check')
        options.set_argument('--disable-extensions-http-throttling')
        options.set_argument('--disable-extension-content-verification')
        
        # 启动浏览器
        page = ChromiumPage(options)
        
        # 打开HTML文件
        file_url = f"file:///{html_file_path.replace(os.sep, '/')}"
        page.get(file_url)
        
        # 等待页面加载
        time.sleep(3)
        
        # 确定输出路径
        if output_dir is None:
            output_dir = os.path.dirname(html_file_path).replace("generated_orders", "generated_images")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成JPG文件名 - 🔧 使用订单号码命名
        html_filename = os.path.basename(html_file_path)
        jpg_filename = html_filename.replace('.html', '.jpg')
        jpg_path = os.path.join(output_dir, jpg_filename)
        
        # 截图 - 使用新版本API
        try:
            page.get_screenshot(path=jpg_path, full_page=True)
        except Exception as screenshot_error:
            # 尝试使用备用方法
            print(f"⚠️ 尝试备用截图方法...")
            page.get_screenshot(jpg_path, full_page=True)
        
        # 关闭浏览器
        page.quit()
        
        # 清理临时目录
        try:
            shutil.rmtree(temp_dir, ignore_errors=True)
        except:
            pass
        
        # 检查文件是否生成成功
        if os.path.exists(jpg_path) and os.path.getsize(jpg_path) > 0:
            print(f"✅ JPG文件生成成功: {jpg_path}")
            return jpg_path
        else:
            print(f"❌ JPG文件生成失败: {jpg_path}")
            return None
            
    except Exception as e:
        print(f"❌ HTML转JPG失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
    return None

def generate_wheat_thins_evidence_for_thread(row_data, thread_name=None, output_html_dir=None, output_image_dir=None):
    """
    为当前线程生成Wheat Thins证据
    
    Args:
        row_data: 行数据，包含用户信息
        thread_name: 线程名称
        output_html_dir: HTML输出目录
        output_image_dir: 图片输出目录
        
    Returns:
        dict: 生成的文件信息
    """
    try:
        # 设置默认目录
        if output_html_dir is None:
            output_html_dir = os.path.join(os.getcwd(), "generated_orders")
        if output_image_dir is None:
            output_image_dir = os.path.join(os.getcwd(), "generated_images")
        
        # 获取或生成线程名称
        if thread_name is None:
            thread_id = threading.get_ident()
            thread_name = f"thread_{thread_id}_{int(time.time())}"
        
        print(f"🎬 开始为线程 {thread_name} 生成Wheat Thins证据")
        
        # 从row_data提取州信息
        state_name = None
        if row_data and len(row_data) > 6:
            state_raw = str(row_data[6]).strip()
            if state_raw:
                state_name = state_raw
        
        if not state_name:
            state_name = "California"  # 默认州
        
        # 提取地址信息
        custom_address = None
        if row_data and len(row_data) > 5:
            try:
                custom_address = {
                    'name': f"{row_data[0]} {row_data[1]}".strip() if len(row_data) > 1 else "John Doe",
                    'street': str(row_data[2]).strip() if len(row_data) > 2 else "123 Main St",
                    'city': str(row_data[3]).strip() if len(row_data) > 3 else "City",
                    'state': str(row_data[4]).strip() if len(row_data) > 4 else state_name,
                    'zip': str(row_data[5]).strip() if len(row_data) > 5 else "12345"
                }
            except:
                custom_address = None
        
        print(f"🌍 使用州: {state_name}")
        
        # 计算收据数据（一个产品，调整数量）
        receipt_data = calculate_wheat_thins_receipt_item(
            target_amount_min=110, 
            target_amount_max=150, 
            state_name=state_name
        )
        
        print(f"📦 商品: {receipt_data['product']['name']}")
        print(f"🔢 数量: {receipt_data['quantity']}")
        print(f"💰 总金额: ${receipt_data['total_amount']:.2f}")
        print(f"📊 税率: {receipt_data['tax_rate_percent']}")
        
        # 生成HTML文件
        html_file = generate_wheat_thins_html_from_template(
            receipt_data=receipt_data,
            custom_address=custom_address,
            output_dir=output_html_dir,
            thread_name=thread_name
        )
        
        if not html_file:
            print("❌ HTML文件生成失败")
            return None
        
        # 转换为JPG
        thread_image_dir = os.path.join(output_image_dir, thread_name)
        jpg_file = html_to_jpg_with_drission(html_file, thread_image_dir)
        
        # 返回结果
        result = {
            "thread_name": thread_name,
            "state": state_name,
            "receipt_data": receipt_data,
            "files": {
                "html_file": html_file,
                "jpg_file": jpg_file
            }
        }
        
        print(f"✅ Wheat Thins证据生成完成!")
        return result
            
    except Exception as e:
        print(f"❌ 生成Wheat Thins证据时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def cleanup_thread_evidence_files(thread_name, output_html_dir=None, output_image_dir=None):
    """
    清理指定线程的证据文件
    
    Args:
        thread_name: 线程名称
        output_html_dir: HTML目录
        output_image_dir: 图片目录
    """
    try:
        if output_html_dir is None:
            output_html_dir = os.path.join(os.getcwd(), "generated_orders")
        if output_image_dir is None:
            output_image_dir = os.path.join(os.getcwd(), "generated_images")
        
        # 清理HTML文件夹
        html_thread_dir = os.path.join(output_html_dir, thread_name)
        if os.path.exists(html_thread_dir):
            shutil.rmtree(html_thread_dir)
            print(f"🧹 已清理HTML文件夹: {html_thread_dir}")
        
        # 清理图片文件夹
        image_thread_dir = os.path.join(output_image_dir, thread_name)
        if os.path.exists(image_thread_dir):
            shutil.rmtree(image_thread_dir)
            print(f"🧹 已清理图片文件夹: {image_thread_dir}")
        
        print(f"✅ 线程 {thread_name} 的证据文件已清理完成")
        
    except Exception as e:
        print(f"❌ 清理线程证据文件失败: {e}")

def test_wheat_thins_evidence_generation():
    """
    测试Wheat Thins证据生成功能
    """
    print("🧪 开始测试Wheat Thins证据生成功能...")
    
    # 模拟row_data
    test_row_data = [
        "John",        # A列 - 名
        "Doe",         # B列 - 姓
        "123 Main St", # C列 - 街道
        "Los Angeles", # D列 - 城市
        "CA",          # E列 - 州简称
        "90210",       # F列 - 邮编
        "California",  # 第7列 - 完整州名
        "<EMAIL>"  # I列 - 邮箱
    ]
    
    # 生成证据
    result = generate_wheat_thins_evidence_for_thread(
        row_data=test_row_data,
        thread_name="test_thread"
    )
    
    if result:
        print("✅ 测试成功!")
        print(f"   HTML文件: {result['files']['html_file']}")
        print(f"   JPG文件: {result['files']['jpg_file']}")
        
        # 询问是否清理测试文件
        cleanup_choice = input("🤔 是否清理测试文件? (y/n): ").lower()
        if cleanup_choice == 'y':
            cleanup_thread_evidence_files("test_thread")
    else:
        print("❌ 测试失败!")

if __name__ == "__main__":
    # 运行测试
    test_wheat_thins_evidence_generation()