#!/usr/bin/env python3
"""
专门测试file1上传问题的修复
"""

import os
import time
import glob

# 模拟DrissionPage的上传测试
def test_file1_upload_fix():
    """测试file1上传修复"""
    print("测试file1上传修复...")
    
    # 查找证据文件
    evidence_dir = "generated_images/thread_1"
    jpg_files = glob.glob(os.path.join(evidence_dir, "*.jpg"))
    
    if not jpg_files:
        print(f"错误: 在 {evidence_dir} 中找不到JPG文件")
        return False
    
    print(f"找到 {len(jpg_files)} 个证据文件")
    
    # 模拟上传任务
    upload_tasks = []
    for i, jpg_file in enumerate(jpg_files[:5]):  # 测试前5个文件
        upload_tasks.append({
            'file_path': jpg_file,
            'input_number': i + 1,
            'file_id': f"file{i + 1}",
            'uploaded': False,
            'attempts': 0
        })
    
    print("\n模拟上传过程:")
    for task in upload_tasks:
        task['attempts'] += 1
        file_id = task['file_id']
        input_number = task['input_number']
        jpg_file = task['file_path']
        
        print(f"  正在上传 {file_id}: {os.path.basename(jpg_file)} (第 {task['attempts']} 次尝试)")
        
        # 模拟file1的特殊处理
        if input_number == 1:
            print(f"    file1需要特殊处理...")
            print(f"    先清空file1的值")
            time.sleep(1)
            print(f"    额外延时2秒")
            time.sleep(2)
        
        # 模拟滚动和延时
        print(f"    滚动到输入框")
        time.sleep(1)
        
        # 模拟上传
        print(f"    使用input方法上传文件")
        print(f"    上传完成: {os.path.basename(jpg_file)}")
        time.sleep(2)
        
        # 模拟成功
        task['uploaded'] = True
        print(f"    {file_id} 上传成功")
    
    # 统计结果
    successful_count = sum(1 for task in upload_tasks if task['uploaded'])
    print(f"\n测试结果: {successful_count}/{len(upload_tasks)} 个文件成功上传")
    
    return successful_count == len(upload_tasks)

if __name__ == "__main__":
    success = test_file1_upload_fix()
    if success:
        print("\n✅ file1上传修复测试通过！")
    else:
        print("\n❌ file1上传修复测试失败！")
