"""
比特浏览器管理模块
提供比特浏览器的创建、打开、关闭等功能
"""

import requests
import json
import time
import logging
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)


class BitBrowserManager:
    """比特浏览器管理类"""

    def __init__(self, api_host: str = "127.0.0.1", api_port: int = 54345):
        """
        初始化比特浏览器管理器

        Args:
            api_host: API服务器地址
            api_port: API服务器端口
        """
        self.api_host = api_host
        self.api_port = api_port
        self.base_url = f"http://{api_host}:{api_port}"
        self.last_request_time = 0
        self.min_request_interval = 1.0  # 最小请求间隔（秒）
        self.session = requests.Session()
        self.session.timeout = 30

        # 设置正确的请求头
        self.headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
    def _make_request(self, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        发送API请求（比特浏览器API都使用POST方法）- 带频率限制

        Args:
            endpoint: API端点
            data: 请求数据

        Returns:
            Dict[str, Any]: 响应结果
        """
        try:
            # 频率限制：确保请求间隔不少于最小间隔
            current_time = time.time()
            time_since_last_request = current_time - self.last_request_time

            if time_since_last_request < self.min_request_interval:
                sleep_time = self.min_request_interval - time_since_last_request
                logger.debug(f"API频率限制，等待 {sleep_time:.2f} 秒")
                time.sleep(sleep_time)

            self.last_request_time = time.time()

            url = f"{self.base_url}{endpoint}"

            # 比特浏览器API使用POST方法，数据以JSON字符串形式发送
            if data is None:
                data = {}

            logger.info(f"发送API请求: {url}, 数据: {data}")

            response = self.session.post(
                url,
                data=json.dumps(data),
                headers=self.headers,
                timeout=30  # 增加超时时间到30秒
            )

            response.raise_for_status()
            result = response.json()

            logger.info(f"API响应: {result}")

            # 检查API响应格式
            if isinstance(result, dict) and 'success' in result:
                return result
            elif isinstance(result, dict) and 'data' in result:
                # 比特浏览器API成功响应格式
                return {"success": True, "data": result.get('data'), "msg": result.get('msg', '')}
            else:
                return {"success": True, "data": result}

        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败: {endpoint}, 错误: {e}")
            return {"success": False, "error": str(e)}
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return {"success": False, "error": "响应格式错误"}
    
    def get_browser_list(self) -> Dict[str, Any]:
        """获取浏览器列表"""
        # 比特浏览器API需要分页参数
        data = {"page": 0, "pageSize": 100}
        return self._make_request("/browser/list", data)

    def create_browser(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """创建浏览器"""
        return self._make_request("/browser/update", config)

    def open_browser(self, browser_id: str, max_retries: int = 3) -> Dict[str, Any]:
        """
        打开浏览器 - 带重试机制

        Args:
            browser_id: 浏览器ID
            max_retries: 最大重试次数

        Returns:
            Dict[str, Any]: 响应结果
        """
        data = {"id": browser_id}

        for attempt in range(max_retries):
            try:
                result = self._make_request("/browser/open", data)

                # 检查特定错误并处理
                if not result.get("success"):
                    error_msg = result.get("msg", "")

                    if "内核更新失败" in error_msg:
                        logger.warning(f"浏览器 {browser_id} 内核更新失败，尝试使用现有浏览器")
                        return {"success": False, "error": "内核更新失败，请使用现有浏览器"}

                    elif "浏览器正在打开中" in error_msg:
                        if attempt < max_retries - 1:
                            logger.info(f"浏览器正在打开中，等待 3 秒后重试 (尝试 {attempt + 1}/{max_retries})")
                            time.sleep(3)
                            continue
                        else:
                            return {"success": False, "error": "浏览器打开超时"}

                    elif "Too Many Requests" in str(result):
                        if attempt < max_retries - 1:
                            wait_time = (attempt + 1) * 2  # 递增等待时间
                            logger.info(f"API请求过于频繁，等待 {wait_time} 秒后重试 (尝试 {attempt + 1}/{max_retries})")
                            time.sleep(wait_time)
                            continue
                        else:
                            return {"success": False, "error": "API请求频率限制"}

                return result

            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"打开浏览器失败，重试 {attempt + 1}/{max_retries}: {e}")
                    time.sleep(2)
                    continue
                else:
                    logger.error(f"打开浏览器最终失败: {e}")
                    return {"success": False, "error": str(e)}

        return {"success": False, "error": "重试次数已用完"}

    def close_browser(self, browser_id: str) -> Dict[str, Any]:
        """关闭浏览器"""
        data = {"id": browser_id}
        return self._make_request("/browser/close", data)

    def delete_browser(self, browser_id: str) -> Dict[str, Any]:
        """删除浏览器"""
        data = {"id": browser_id}
        return self._make_request("/browser/delete", data)
    
    def create_default_config(self, name: str) -> Dict[str, Any]:
        """
        创建默认浏览器配置（基于1.84版本格式）

        Args:
            name: 浏览器名称

        Returns:
            Dict[str, Any]: 浏览器配置
        """
        return {
            "name": name,
            "remark": "自动化测试浏览器",
            "proxyMethod": 2,  # 不使用代理
            "proxyType": "noproxy",
            "host": "",
            "port": "",
            "proxyUserName": "",
            "proxyPassword": "",
            "uaType": 0,  # 自定义UA
            "ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "securityType": 0,
            "args": [],
            "loadExtensions": False,
            "loadPlugins": True,
            "username": "",
            "password": "",
            "cookie": "",
            "masterPassword": "",
            # 添加其他必要字段
            "browserFingerPrint": {
                "coreVersion": "120",
                "ostype": "PC",
                "os": "Windows",
                "osVersion": "10"
            }
        }
    
    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            result = self.get_browser_list()
            # 检查响应是否成功
            if isinstance(result, dict):
                if result.get("success") is True:
                    return True
                elif "data" in result:  # 比特浏览器API成功响应格式
                    return True
            return False
        except Exception as e:
            logger.error(f"测试连接失败: {e}")
            return False
    
    def create_and_open_browser(self, name: str) -> Dict[str, Any]:
        """
        创建并打开浏览器

        Args:
            name: 浏览器名称

        Returns:
            Dict[str, Any]: 操作结果，包含browser_id和debug_port
        """
        try:
            # 创建浏览器配置
            config = self.create_default_config(name)

            # 创建浏览器（使用update接口）
            create_result = self.create_browser(config)
            if not create_result.get("success"):
                return {"success": False, "error": f"创建浏览器失败: {create_result.get('msg', '未知错误')}"}

            # 从响应中获取浏览器ID
            browser_data = create_result.get("data")
            if isinstance(browser_data, dict):
                browser_id = browser_data.get("id")
            elif isinstance(browser_data, str):
                browser_id = browser_data
            else:
                return {"success": False, "error": "未获取到浏览器ID"}

            if not browser_id:
                return {"success": False, "error": "浏览器ID为空"}

            logger.info(f"浏览器创建成功，ID: {browser_id}")

            # 打开浏览器
            open_result = self.open_browser(browser_id)
            if not open_result.get("success"):
                return {"success": False, "error": f"打开浏览器失败: {open_result.get('msg', '未知错误')}"}

            # 提取调试端口和URL信息
            open_data = open_result.get("data", {})
            http_url = open_data.get("http", "")
            ws_url = open_data.get("ws", "")

            debug_port = None
            if http_url and ":" in http_url:
                try:
                    debug_port = http_url.split(":")[-1]
                except:
                    debug_port = "9222"  # 默认端口

            logger.info(f"浏览器打开成功，调试端口: {debug_port}")

            return {
                "success": True,
                "browser_id": browser_id,
                "debug_port": debug_port,
                "ws_url": ws_url,
                "http_url": http_url
            }

        except Exception as e:
            logger.error(f"创建并打开浏览器失败: {e}")
            return {"success": False, "error": str(e)}
    
    def close_and_delete_browser(self, browser_id: str) -> Dict[str, Any]:
        """
        关闭并删除浏览器

        Args:
            browser_id: 浏览器ID

        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            # 关闭浏览器
            close_result = self.close_browser(browser_id)

            # 删除浏览器
            delete_result = self.delete_browser(browser_id)

            return {
                "success": True,
                "close_result": close_result,
                "delete_result": delete_result
            }

        except Exception as e:
            logger.error(f"关闭并删除浏览器失败: {e}")
            return {"success": False, "error": str(e)}

    def get_available_browsers(self) -> List[Dict[str, Any]]:
        """
        获取可用的浏览器列表

        Returns:
            List[Dict[str, Any]]: 可用浏览器列表
        """
        try:
            result = self.get_browser_list()
            if result.get("success"):
                data = result.get("data", {})
                browsers = data.get("list", []) if isinstance(data, dict) else []
                return browsers
            return []
        except Exception as e:
            logger.error(f"获取可用浏览器失败: {e}")
            return []

    def open_browser_by_index(self, browser_index: int) -> Dict[str, Any]:
        """
        根据索引打开浏览器

        Args:
            browser_index: 浏览器索引（从0开始）

        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            browsers = self.get_available_browsers()

            if browser_index >= len(browsers):
                return {"success": False, "error": f"浏览器索引 {browser_index} 超出范围，总数: {len(browsers)}"}

            browser = browsers[browser_index]
            browser_id = browser.get("id")
            browser_name = browser.get("name", "未命名")

            logger.info(f"打开浏览器 {browser_index}: {browser_name} (ID: {browser_id})")

            open_result = self.open_browser(browser_id)

            if open_result.get("success"):
                open_data = open_result.get("data", {})
                http_url = open_data.get("http", "")
                ws_url = open_data.get("ws", "")

                debug_port = "9222"
                if http_url and ":" in http_url:
                    try:
                        debug_port = http_url.split(":")[-1]
                    except:
                        pass

                return {
                    "success": True,
                    "browser_id": browser_id,
                    "browser_name": browser_name,
                    "browser_index": browser_index,
                    "debug_port": debug_port,
                    "ws_url": ws_url,
                    "http_url": http_url
                }
            else:
                return {"success": False, "error": f"打开浏览器失败: {open_result.get('msg')}"}

        except Exception as e:
            logger.error(f"根据索引打开浏览器失败: {e}")
            return {"success": False, "error": str(e)}
