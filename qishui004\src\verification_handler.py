"""
验证处理模块
处理邮件验证码、手机验证码等验证流程
"""

import re
import time
import logging
from typing import Optional, Dict, Any, List
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class VerificationHandler(ABC):
    """验证处理基类"""
    
    @abstractmethod
    def get_verification_code(self, identifier: str, timeout: int = 300) -> Optional[str]:
        """
        获取验证码
        
        Args:
            identifier: 标识符（邮箱或手机号）
            timeout: 超时时间（秒）
            
        Returns:
            Optional[str]: 验证码，获取失败返回None
        """
        pass


class EmailVerificationHandler(VerificationHandler):
    """邮件验证码处理类"""
    
    def __init__(self, email_config: Dict[str, Any]):
        """
        初始化邮件验证处理器
        
        Args:
            email_config: 邮件配置
        """
        self.email_config = email_config
        self.imap_server = email_config.get("imap_server")
        self.username = email_config.get("username")
        self.password = email_config.get("password")
        self.port = email_config.get("port", 993)
        
    def get_verification_code(self, email: str, timeout: int = 300) -> Optional[str]:
        """
        从邮件中获取验证码
        
        Args:
            email: 邮箱地址
            timeout: 超时时间（秒）
            
        Returns:
            Optional[str]: 验证码
        """
        try:
            import imaplib
            import email as email_lib
            from email.header import decode_header
            
            # 连接到IMAP服务器
            mail = imaplib.IMAP4_SSL(self.imap_server, self.port)
            mail.login(self.username, self.password)
            mail.select("inbox")
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    # 搜索最新的邮件
                    status, messages = mail.search(None, 'UNSEEN')
                    if status == "OK" and messages[0]:
                        # 获取最新邮件
                        latest_email_id = messages[0].split()[-1]
                        status, msg_data = mail.fetch(latest_email_id, "(RFC822)")
                        
                        if status == "OK":
                            email_body = msg_data[0][1]
                            email_message = email_lib.message_from_bytes(email_body)
                            
                            # 提取邮件内容
                            content = self._extract_email_content(email_message)
                            
                            # 查找验证码
                            verification_code = self._extract_verification_code(content)
                            if verification_code:
                                logger.info(f"从邮件中获取到验证码: {verification_code}")
                                mail.close()
                                mail.logout()
                                return verification_code
                    
                    time.sleep(5)  # 等待5秒后重试
                    
                except Exception as e:
                    logger.error(f"检查邮件时出错: {e}")
                    time.sleep(5)
            
            mail.close()
            mail.logout()
            logger.error("获取邮件验证码超时")
            return None
            
        except Exception as e:
            logger.error(f"邮件验证码获取失败: {e}")
            return None
    
    def _extract_email_content(self, email_message) -> str:
        """
        提取邮件内容
        
        Args:
            email_message: 邮件消息对象
            
        Returns:
            str: 邮件内容
        """
        content = ""
        
        if email_message.is_multipart():
            for part in email_message.walk():
                content_type = part.get_content_type()
                if content_type == "text/plain" or content_type == "text/html":
                    try:
                        payload = part.get_payload(decode=True)
                        if payload:
                            content += payload.decode('utf-8', errors='ignore')
                    except Exception:
                        pass
        else:
            try:
                payload = email_message.get_payload(decode=True)
                if payload:
                    content = payload.decode('utf-8', errors='ignore')
            except Exception:
                pass
        
        return content
    
    def _extract_verification_code(self, content: str) -> Optional[str]:
        """
        从邮件内容中提取验证码
        
        Args:
            content: 邮件内容
            
        Returns:
            Optional[str]: 验证码
        """
        # 常见的验证码模式
        patterns = [
            r'验证码[：:]\s*([A-Za-z0-9]{4,8})',
            r'verification code[：:]\s*([A-Za-z0-9]{4,8})',
            r'code[：:]\s*([A-Za-z0-9]{4,8})',
            r'([A-Za-z0-9]{6})',  # 6位数字或字母
            r'([0-9]{4,8})',      # 4-8位数字
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                return matches[0]
        
        return None


class SMSVerificationHandler(VerificationHandler):
    """短信验证码处理类"""
    
    def __init__(self, sms_config: Dict[str, Any]):
        """
        初始化短信验证处理器
        
        Args:
            sms_config: 短信配置
        """
        self.sms_config = sms_config
        self.api_url = sms_config.get("api_url")
        self.api_key = sms_config.get("api_key")
        
    def get_verification_code(self, phone: str, timeout: int = 300) -> Optional[str]:
        """
        从短信中获取验证码
        
        Args:
            phone: 手机号
            timeout: 超时时间（秒）
            
        Returns:
            Optional[str]: 验证码
        """
        try:
            import requests
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    # 调用短信API获取最新短信
                    response = requests.get(
                        self.api_url,
                        params={"phone": phone, "api_key": self.api_key},
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        if data.get("success") and data.get("messages"):
                            # 获取最新短信
                            latest_message = data["messages"][0]
                            content = latest_message.get("content", "")
                            
                            # 提取验证码
                            verification_code = self._extract_verification_code(content)
                            if verification_code:
                                logger.info(f"从短信中获取到验证码: {verification_code}")
                                return verification_code
                    
                    time.sleep(5)  # 等待5秒后重试
                    
                except Exception as e:
                    logger.error(f"检查短信时出错: {e}")
                    time.sleep(5)
            
            logger.error("获取短信验证码超时")
            return None
            
        except Exception as e:
            logger.error(f"短信验证码获取失败: {e}")
            return None
    
    def _extract_verification_code(self, content: str) -> Optional[str]:
        """
        从短信内容中提取验证码
        
        Args:
            content: 短信内容
            
        Returns:
            Optional[str]: 验证码
        """
        # 常见的验证码模式
        patterns = [
            r'验证码[：:]\s*([0-9]{4,8})',
            r'verification code[：:]\s*([0-9]{4,8})',
            r'code[：:]\s*([0-9]{4,8})',
            r'([0-9]{6})',  # 6位数字
            r'([0-9]{4})',  # 4位数字
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                return matches[0]
        
        return None


class VerificationManager:
    """验证管理器"""
    
    def __init__(self):
        """初始化验证管理器"""
        self.handlers = {}
        
    def register_handler(self, handler_type: str, handler: VerificationHandler):
        """
        注册验证处理器
        
        Args:
            handler_type: 处理器类型（email, sms等）
            handler: 验证处理器实例
        """
        self.handlers[handler_type] = handler
        logger.info(f"注册验证处理器: {handler_type}")
    
    def get_verification_code(self, handler_type: str, identifier: str, timeout: int = 300) -> Optional[str]:
        """
        获取验证码
        
        Args:
            handler_type: 处理器类型
            identifier: 标识符
            timeout: 超时时间
            
        Returns:
            Optional[str]: 验证码
        """
        if handler_type not in self.handlers:
            logger.error(f"未找到验证处理器: {handler_type}")
            return None
        
        handler = self.handlers[handler_type]
        return handler.get_verification_code(identifier, timeout)
    
    def get_available_handlers(self) -> List[str]:
        """获取可用的处理器列表"""
        return list(self.handlers.keys())
