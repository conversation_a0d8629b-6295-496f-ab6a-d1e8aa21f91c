
产品种类+价格
POPPI PREBIOTIC SODA, BEACH PARTY – 12 FL OZ (PACK OF 12)		$27.48
POPPI PREBIOTIC SODA, CROWD PLEASERS – 12 FL OZ (PACK OF 12)	$27.48
POPPI PREBIOTIC SODA, Variety – 12 Fl Oz (Pack of 12)		$27.88

州名称	缩写	固定州税率（%）
Alabama	AL	4
Alaska	AK	0
Arizona	AZ	5.6
Arkansas	AR	6.5
California	CA	7.25
Colorado	CO	2.9
Connecticut	CT	6.35
Delaware	DE	0
Florida	FL	6
Georgia	GA	4
Hawaii	HI	4
Idaho	ID	6
Illinois	IL	6.25
Indiana	IN	7
Iowa	IA	6
Kansas	KS	6.5
Kentucky	KY	6
Louisiana	LA	4.45
Maine	ME	5.5
Maryland	MD	6
Massachusetts	MA	6.25
Michigan	MI	6
Minnesota	MN	6.88
Mississippi	MS	7
Missouri	MO	4.23
Montana	MT	0
Nebraska	NE	5.5
Nevada	NV	6.85
New Hampshire	NH	0
New Jersey	NJ	6.63
New Mexico	NM	5.13
New York	NY	4
North Carolina	NC	4.75
North Dakota	ND	5
Ohio	OH	5.75
Oklahoma	OK	4.5
Oregon	OR	0
Pennsylvania	PA	6
Rhode Island	RI	7
South Carolina	SC	6
South Dakota	SD	4.5
Tennessee	TN	7
Texas	TX	6.25
Utah	UT	4.85
Vermont	VT	6
Virginia	VA	4.3
Washington	WA	6.5
West Virginia	WV	6
Wisconsin	WI	5
Wyoming	WY	4
District of Columbia	DC	6


生成证据的逻辑:
使用order4.py里的代码进行修改
模板在文件夹内:\qishui005\templates\
订单时间2020年~2024年
订单数量:随机3~7张订单
每张订单:产品种类(上面的3种)
每张订单产品数量:随机2到6
每张订单的时间间隔:根据总瓶数+随机(1~10),例如买了种类1,12瓶买了2个,随机5,那订单间隔就是29天
因为是tasks3.py里面的有证据模式,订单需要其他的信息,比如地址等,由tasks3.py传参数进来,
测试数据:ANDREW----MARIAUX----6000 ELDORADO PKWY APT 1722----FRISCO----TX----75033----11/8/<EMAIL>
每张订单的税率根据上面的列表生成硬编的映射表,在订单上需要体现税率(原程序内可能已经有了,在次优化)
要返回给tasks3.py,所有订单的总数量,(订单1,2种,每种各1个,订单2,1种,2个,那么返回的数量就是4个,用于在申请页面第4个输入框输入的数量)

修改逻辑:
每张订单随机1~3种口味
每张订单每种口味1包

