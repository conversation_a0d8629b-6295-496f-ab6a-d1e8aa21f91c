#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import requests
import json
import random
import threading
import os
import sys
from DrissionPage import ChromiumOptions, Chromium

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 导入证据生成模块
try:
    from src.order4 import generate_poppi_evidence_for_thread
    EVIDENCE_MODULE_AVAILABLE = True
    print(" 证据生成模块已导入")
except ImportError as e:
    EVIDENCE_MODULE_AVAILABLE = False
    print(f" 证据生成模块导入失败: {e}")

# 州简写到全称的映射表
STATE_MAPPING = {
    'AL': 'Alabama',
    'AK': 'Alaska',
    'AZ': 'Arizona',
    'AR': 'Arkansas',
    'CA': 'California',
    'CO': 'Colorado',
    'CT': 'Connecticut',
    'DE': 'Delaware',
    'DC': 'District Of Columbia',
    'FL': 'Florida',
    'GA': 'Georgia',
    'HI': 'Hawaii',
    'ID': 'Idaho',
    'IL': 'Illinois',
    'IN': 'Indiana',
    'IA': 'Iowa',
    'KS': 'Kansas',
    'KY': 'Kentucky',
    'LA': 'Louisiana',
    'ME': 'Maine',
    'MD': 'Maryland',
    'MA': 'Massachusetts',
    'MI': 'Michigan',
    'MN': 'Minnesota',
    'MS': 'Mississippi',
    'MO': 'Missouri',
    'MT': 'Montana',
    'NE': 'Nebraska',
    'NV': 'Nevada',
    'NH': 'New Hampshire',
    'NJ': 'New Jersey',
    'NM': 'New Mexico',
    'NY': 'New York',
    'NC': 'North Carolina',
    'ND': 'North Dakota',
    'OH': 'Ohio',
    'OK': 'Oklahoma',
    'OR': 'Oregon',
    'PA': 'Pennsylvania',
    'RI': 'Rhode Island',
    'SC': 'South Carolina',
    'SD': 'South Dakota',
    'TN': 'Tennessee',
    'TX': 'Texas',
    'UT': 'Utah',
    'VT': 'Vermont',
    'VA': 'Virginia',
    'WA': 'Washington',
    'WV': 'West Virginia',
    'WI': 'Wisconsin',
    'WY': 'Wyoming'
}

def connect_bit_browser():
    """连接比特浏览器并获取调试端口信息"""
    try:
        # 检查比特浏览器服务
        print("检查比特浏览器服务...")
        headers = {'Content-Type': 'application/json'}
        response = requests.post("http://127.0.0.1:54345/browser/list", headers=headers, timeout=5)
        print("比特浏览器服务连接正常")
        
        # 获取浏览器列表
        list_data = {"page": 0, "pageSize": 100}
        response = requests.post("http://127.0.0.1:54345/browser/list", 
                               data=json.dumps(list_data), 
                               headers=headers)
        
        response_json = response.json()
        if 'data' in response_json:
            browsers = response_json['data']['list']
        else:
            browsers = response_json
            
        if not browsers:
            print("没有找到浏览器，请先创建浏览器")
            return None, None
        
        # 使用第一个浏览器
        browser_id = browsers[0]['id']
        print(f"使用浏览器ID: {browser_id}")
        
        # 打开浏览器
        open_data = {"id": browser_id}
        open_response = requests.post(f"http://127.0.0.1:54345/browser/open", 
                                    data=json.dumps(open_data), 
                                    headers=headers)
        
        if open_response.status_code != 200:
            print(f"打开浏览器失败: {open_response.text}")
            return None, None
        
        open_response_json = open_response.json()
        if 'data' not in open_response_json:
            print("打开浏览器失败: 响应中没有data字段")
            return None, None
        
        driver = open_response_json['data']['driver']
        http = open_response_json['data']['http']
        print(f"获取到调试端口信息: {http}")
        
        # 等待浏览器启动
        time.sleep(3)
        
        # 使用DrissionPage连接
        co = ChromiumOptions()
        co.set_browser_path(driver)
        co.set_address(http)
        
        browser = Chromium(co)
        tab = browser.latest_tab
        
        return browser, tab
        
    except Exception as e:
        print(f"连接比特浏览器失败: {e}")
        return None, None

class CloudflareChecker:
    """Cloudflare盾检测与自动点击类"""
    def __init__(self):
        pass

    def analyze_cf_status_with_js(self, tab):
        """使用JavaScript深度分析CF验证状态和Shadow DOM"""
        try:
            print("使用JavaScript深度分析CF验证状态...")

            js_code = """
            // 深度分析CF验证状态和Shadow DOM
            function analyzeCFStatus() {
                let results = {
                    pageInfo: {
                        title: document.title,
                        url: window.location.href,
                        readyState: document.readyState
                    },
                    cfElements: {
                        shadowRoots: [],
                        cfDivs: [],
                        turnstileDivs: [],
                        challengeDivs: [],
                        successElements: [],
                        checkboxes: [],
                        iframes: [],
                        buttons: [],
                        clickableElements: []
                    },
                    pageContent: {
                        bodyText: document.body ? document.body.innerText.substring(0, 200) : '',
                        hasRadioButtons: document.querySelectorAll('input[type="radio"]').length > 0,
                        hasFormElements: document.querySelectorAll('form').length > 0
                    }
                };

                // 查找Shadow DOM
                function findShadowRoots(root, depth = 0) {
                    if (depth > 5) return; // 限制深度

                    const elements = root.querySelectorAll('*');
                    elements.forEach(elem => {
                        if (elem.shadowRoot) {
                            let shadowInfo = {
                                hostTag: elem.tagName,
                                hostId: elem.id || '',
                                hostClass: elem.className || '',
                                depth: depth,
                                children: []
                            };

                            // 在shadow root中查找CF相关元素
                            const shadowElements = elem.shadowRoot.querySelectorAll('*');
                            shadowElements.forEach(shadowElem => {
                                if (shadowElem.className &&
                                    (shadowElem.className.includes('cf-') ||
                                     shadowElem.className.includes('turnstile') ||
                                     shadowElem.className.includes('challenge'))) {
                                    shadowInfo.children.push({
                                        tag: shadowElem.tagName,
                                        className: shadowElem.className,
                                        text: shadowElem.textContent ? shadowElem.textContent.trim().substring(0, 50) : '',
                                        rect: shadowElem.getBoundingClientRect()
                                    });
                                }
                            });

                            results.cfElements.shadowRoots.push(shadowInfo);

                            // 递归查找嵌套的shadow root
                            findShadowRoots(elem.shadowRoot, depth + 1);
                        }
                    });
                }

                // 从document开始查找
                findShadowRoots(document);

                // 查找CF相关的div元素
                document.querySelectorAll('div[class*="cf-"], div[id*="cf-"]').forEach(elem => {
                    let rect = elem.getBoundingClientRect();
                    results.cfElements.cfDivs.push({
                        tag: elem.tagName,
                        id: elem.id || '',
                        className: elem.className || '',
                        text: elem.textContent ? elem.textContent.trim().substring(0, 50) : '',
                        visible: rect.width > 0 && rect.height > 0,
                        position: {x: rect.x, y: rect.y, width: rect.width, height: rect.height}
                    });
                });

                // 查找Turnstile元素
                document.querySelectorAll('div[class*="turnstile"], *[class*="turnstile"]').forEach(elem => {
                    let rect = elem.getBoundingClientRect();
                    results.cfElements.turnstileDivs.push({
                        tag: elem.tagName,
                        id: elem.id || '',
                        className: elem.className || '',
                        text: elem.textContent ? elem.textContent.trim().substring(0, 50) : '',
                        visible: rect.width > 0 && rect.height > 0,
                        position: {x: rect.x, y: rect.y, width: rect.width, height: rect.height}
                    });
                });

                // 查找Challenge元素
                document.querySelectorAll('div[class*="challenge"], *[class*="challenge"]').forEach(elem => {
                    let rect = elem.getBoundingClientRect();
                    results.cfElements.challengeDivs.push({
                        tag: elem.tagName,
                        id: elem.id || '',
                        className: elem.className || '',
                        text: elem.textContent ? elem.textContent.trim().substring(0, 50) : '',
                        visible: rect.width > 0 && rect.height > 0,
                        position: {x: rect.x, y: rect.y, width: rect.width, height: rect.height}
                    });
                });

                // 查找Success元素
                document.querySelectorAll('*').forEach(elem => {
                    if (elem.textContent && elem.textContent.toLowerCase().includes('success')) {
                        let rect = elem.getBoundingClientRect();
                        results.cfElements.successElements.push({
                            tag: elem.tagName,
                            text: elem.textContent.trim().substring(0, 50),
                            visible: rect.width > 0 && rect.height > 0,
                            position: {x: rect.x, y: rect.y}
                        });
                    }
                });

                // 查找复选框
                document.querySelectorAll('input[type="checkbox"]').forEach(elem => {
                    let rect = elem.getBoundingClientRect();
                    let checkboxInfo = {
                        id: elem.id || '',
                        name: elem.name || '',
                        className: elem.className || '',
                        checked: elem.checked,
                        visible: rect.width > 0 && rect.height > 0,
                        position: {x: rect.x, y: rect.y, width: rect.width, height: rect.height}
                    };

                    results.cfElements.checkboxes.push(checkboxInfo);

                    // 如果是可见的CF相关复选框，添加到可点击元素
                    if (checkboxInfo.visible &&
                        (checkboxInfo.className.includes('cf-') ||
                         checkboxInfo.className.includes('turnstile') ||
                         checkboxInfo.className.includes('challenge'))) {
                        results.cfElements.clickableElements.push({
                            type: 'checkbox',
                            element: checkboxInfo,
                            clickPosition: {
                                x: rect.x + rect.width / 2,
                                y: rect.y + rect.height / 2
                            }
                        });
                    }
                });

                // 查找iframe
                document.querySelectorAll('iframe').forEach(elem => {
                    let rect = elem.getBoundingClientRect();
                    results.cfElements.iframes.push({
                        src: elem.src || '',
                        title: elem.title || '',
                        id: elem.id || '',
                        visible: rect.width > 0 && rect.height > 0,
                        position: {x: rect.x, y: rect.y, width: rect.width, height: rect.height}
                    });
                });

                // 查找按钮
                document.querySelectorAll('button, input[type="button"], *[role="button"]').forEach(elem => {
                    let rect = elem.getBoundingClientRect();
                    if (rect.width > 0 && rect.height > 0) {
                        results.cfElements.buttons.push({
                            tag: elem.tagName,
                            text: elem.textContent ? elem.textContent.trim().substring(0, 30) : '',
                            id: elem.id || '',
                            className: elem.className || '',
                            position: {x: rect.x, y: rect.y, width: rect.width, height: rect.height}
                        });
                    }
                });

                return results;
            }

            return analyzeCFStatus();
            """

            result = tab.run_js(js_code)
            return result

        except Exception as e:
            print(f"JavaScript分析CF状态失败: {e}")
            return None

    def find_and_click_cf_checkbox(self, tab):
        """查找并点击CF验证框"""
        try:
            print("查找并点击CF验证框...")

            # 先使用JavaScript分析
            cf_analysis = self.analyze_cf_status_with_js(tab)

            if cf_analysis:
                print("CF状态分析结果:")
                print(f"  页面标题: {cf_analysis.get('pageInfo', {}).get('title', '无')}")
                print(f"  CF div数量: {len(cf_analysis.get('cfElements', {}).get('cfDivs', []))}")
                print(f"  Turnstile div数量: {len(cf_analysis.get('cfElements', {}).get('turnstileDivs', []))}")
                print(f"  复选框数量: {len(cf_analysis.get('cfElements', {}).get('checkboxes', []))}")
                print(f"  可点击CF元素数量: {len(cf_analysis.get('cfElements', {}).get('clickableElements', []))}")

                # 查找可点击的CF元素
                clickable_elements = cf_analysis.get('cfElements', {}).get('clickableElements', [])

                if clickable_elements:
                    for i, elem in enumerate(clickable_elements):
                        click_pos = elem.get('clickPosition', {})
                        x = click_pos.get('x', 0)
                        y = click_pos.get('y', 0)

                        if x > 0 and y > 0:
                            print(f"找到可点击CF元素 {i+1}，位置: ({x}, {y})")
                            print(f"尝试点击位置 ({x}, {y})...")

                            # 使用actions.move_to((x, y)).click()点击
                            tab.actions.move_to((x, y)).click()
                            time.sleep(2)

                            print(f" 成功点击CF验证框位置 ({x}, {y})")
                            return True

                # 如果没有找到可点击元素，但有CF相关div，尝试点击div中心
                cf_divs = cf_analysis.get('cfElements', {}).get('cfDivs', [])
                turnstile_divs = cf_analysis.get('cfElements', {}).get('turnstileDivs', [])

                all_cf_divs = cf_divs + turnstile_divs

                for i, div in enumerate(all_cf_divs):
                    if div.get('visible', False):
                        pos = div.get('position', {})
                        x = pos.get('x', 0) + pos.get('width', 0) / 2
                        y = pos.get('y', 0) + pos.get('height', 0) / 2

                        if x > 0 and y > 0:
                            print(f"找到CF div {i+1}，尝试点击中心位置: ({x}, {y})")

                            # 使用actions.move_to((x, y)).click()点击
                            tab.actions.move_to((x, y)).click()
                            time.sleep(2)

                            print(f" 成功点击CF div中心位置 ({x}, {y})")
                            return True

                # 如果有复选框，尝试点击复选框
                checkboxes = cf_analysis.get('cfElements', {}).get('checkboxes', [])

                for i, checkbox in enumerate(checkboxes):
                    if checkbox.get('visible', False):
                        pos = checkbox.get('position', {})
                        x = pos.get('x', 0) + pos.get('width', 0) / 2
                        y = pos.get('y', 0) + pos.get('height', 0) / 2

                        if x > 0 and y > 0:
                            print(f"找到复选框 {i+1}，尝试点击位置: ({x}, {y})")

                            # 使用actions.move_to((x, y)).click()点击
                            tab.actions.move_to((x, y)).click()
                            time.sleep(2)

                            print(f" 成功点击复选框位置 ({x}, {y})")
                            return True

            print(" 未找到可点击的CF验证元素")
            return False

        except Exception as e:
            print(f"查找并点击CF验证框失败: {e}")
            return False

    def pass_cf_check_v2(self, tab, attempt_num, timeout=3):
        """处理Cloudflare 5秒盾 - 集成JavaScript分析"""
        thread_id = threading.get_ident()
        try:
            print(f"线程 {thread_id} - 第 {attempt_num + 1} 次尝试：智能查找CF盾元素")

            # 如果是第1次尝试，使用JavaScript深度分析
            if attempt_num == 0:
                print(f"线程 {thread_id} - 第一次尝试，使用JavaScript深度分析CF状态")
                cf_analysis = self.analyze_cf_status_with_js(tab)

                if cf_analysis:
                    # 检查页面状态
                    page_info = cf_analysis.get('pageInfo', {})
                    cf_elements = cf_analysis.get('cfElements', {})

                    print(f"线程 {thread_id} - 页面分析结果:")
                    print(f"  标题: {page_info.get('title', '无')}")
                    print(f"  CF div: {len(cf_elements.get('cfDivs', []))} 个")
                    print(f"  Turnstile div: {len(cf_elements.get('turnstileDivs', []))} 个")
                    print(f"  复选框: {len(cf_elements.get('checkboxes', []))} 个")

                    # 如果发现单选框或表单，说明可能已经通过验证
                    page_content = cf_analysis.get('pageContent', {})
                    if page_content.get('hasRadioButtons', False) or page_content.get('hasFormElements', False):
                        print(f"线程 {thread_id} -  检测到页面已有表单元素，可能已通过CF验证")
                        return True

                # 第一次只分析，不点击
                print(f"线程 {thread_id} - 第一次分析完成，等待下次尝试")
                return False

            # 第2次及以后的尝试，使用智能点击
            elif attempt_num >= 1:
                print(f"线程 {thread_id} - 第 {attempt_num + 1} 次尝试，开始智能点击CF验证")

                # 尝试使用JavaScript分析结果进行精确点击
                click_success = self.find_and_click_cf_checkbox(tab)

                if not click_success:
                    # 如果智能点击失败，使用传统方法
                    print(f"线程 {thread_id} - 智能点击失败，使用传统坐标点击 (208, 289)")
                    tab.actions.move_to((208, 289)).click()
                    time.sleep(2)

                    # 尝试传统xpath方法
                    div_xpaths = [
                        'x://div[contains(@class, "cf-challenge")]',
                        'x://div[contains(@class, "turnstile")]',
                        'x://div[contains(@class, "cf-")]',
                        'x://div[@id="cf-stage"]'
                    ]

                    for xpath in div_xpaths:
                        try:
                            div_ele = tab.ele(xpath, timeout=0.5)
                            if div_ele:
                                print(f"线程 {thread_id} - 找到CF元素，尝试点击: {xpath}")
                                tab.actions.move_to(div_ele).click()
                                time.sleep(1)
                                break
                        except:
                            continue

                # 等待并检查结果
                time.sleep(3)

                # 再次使用JavaScript分析验证结果
                verification_result = self.analyze_cf_status_with_js(tab)

                if verification_result:
                    page_content = verification_result.get('pageContent', {})
                    cf_elements = verification_result.get('cfElements', {})

                    # 检查是否有表单元素出现（说明验证成功）
                    if page_content.get('hasRadioButtons', False) or page_content.get('hasFormElements', False):
                        print(f"线程 {thread_id} -  CF验证成功，检测到表单元素")
                        return True

                    # 检查CF元素是否减少（说明验证进行中）
                    cf_div_count = len(cf_elements.get('cfDivs', []))
                    turnstile_count = len(cf_elements.get('turnstileDivs', []))

                    if cf_div_count == 0 and turnstile_count == 0:
                        print(f"线程 {thread_id} -  CF验证元素消失，验证可能成功")
                        return True

                # 检查页面HTML内容
                current_page = tab.html.lower()
                cf_indicators = ['cloudflare', 'checking your browser', 'please wait', 'verifying you are human', 'just a moment']
                has_cf = any(indicator in current_page for indicator in cf_indicators)

                if not has_cf:
                    print(f"线程 {thread_id} -  CF盾处理成功，页面已跳转")
                    return True
                else:
                    print(f"线程 {thread_id} - ⏳ CF盾仍在处理中")
                    return False

        except Exception as e:
            print(f"线程 {thread_id} - 第 {attempt_num + 1} 次尝试：CF盾处理异常: {e}")
            return False

def check_next_step_elements(tab, expected_elements):
    """检查下一步的元素是否已经存在"""
    try:
        print("检查下一步元素是否已存在...")

        for element_desc, selectors in expected_elements.items():
            for selector in selectors:
                try:
                    element = tab.ele(selector, timeout=1)
                    if element:
                        print(f" 检测到下一步元素已存在: {element_desc}")
                        return True
                except:
                    continue

        print("下一步元素尚未出现，需要处理CF盾")
        return False

    except Exception as e:
        print(f"检查下一步元素时出错: {e}")
        return False

def handle_cloudflare_shield(tab, expected_elements=None, max_attempts=25):
    """
    处理Cloudflare 5秒盾 - 使用新的智能检测方法

    Args:
        tab: DrissionPage tab对象
        expected_elements: 期望的下一步元素字典，格式为 {"描述": ["选择器1", "选择器2"]}
        max_attempts: 最大尝试次数，默认25次
    """
    print("开始处理Cloudflare 5秒盾...")

    # 首先检查下一步元素是否已经存在
    if expected_elements:
        if check_next_step_elements(tab, expected_elements):
            print(" 下一步元素已存在，跳过CF盾处理")
            return True

    # 导入CF检查器
    try:
        from src.cf_check import CloudflareChecker
        cf_checker = CloudflareChecker(logger=None)  # 可以传入logger

        # 使用新的5秒盾检测方法
        print("使用智能5秒盾检测方法...")
        result = cf_checker.pass_cf_check_with_5s_shield(tab, max_attempts=max_attempts, click_interval=5)

        if result:
            print(" ✅ CF盾处理成功")

            # 最终检查下一步元素是否出现
            if expected_elements:
                if check_next_step_elements(tab, expected_elements):
                    print(" ✅ 下一步元素已出现，确认CF盾处理成功")
                    return True
                else:
                    print(" ⚠️ CF盾处理完成但下一步元素未出现，可能需要额外等待")
                    time.sleep(5)
                    return check_next_step_elements(tab, expected_elements)

            return True
        else:
            print(" ❌ CF盾处理失败")
            return False

    except ImportError as e:
        print(f"导入CF检查器失败: {e}")
        # 回退到原有方法
        return handle_cloudflare_shield_fallback(tab, expected_elements, max_attempts)
    except Exception as e:
        print(f"CF盾处理异常: {e}")
        return False

def handle_cloudflare_shield_fallback(tab, expected_elements=None, max_attempts=5):
    """
    CF盾处理的回退方法（原有逻辑）
    """
    print("使用回退方法处理CF盾...")

    cf_checker = CloudflareChecker()

    for attempt in range(max_attempts):
        print(f"CF盾处理尝试 {attempt + 1}/{max_attempts}")

        # 再次检查下一步元素是否已经存在（每次尝试前都检查）
        if expected_elements:
            if check_next_step_elements(tab, expected_elements):
                print(" 下一步元素已出现，CF盾处理成功")
                return True

        # 检查页面是否有CF盾标识
        try:
            current_page = tab.html.lower()
            cf_indicators = ['cloudflare', 'checking your browser', 'please wait', 'verifying you are human', 'just a moment']
            has_cf = any(indicator in current_page for indicator in cf_indicators)

            if not has_cf:
                print(" 页面已通过CF盾验证")
                return True

        except Exception as e:
            print(f"检查页面状态失败: {e}")

        # 尝试处理CF盾
        result = cf_checker.pass_cf_check_v2(tab, attempt)

        if result:
            print(" CF盾处理成功")
            return True

        # 等待一段时间再次尝试
        if attempt < max_attempts - 1:
            wait_time = 3 + attempt  # 递增等待时间
            print(f"等待 {wait_time} 秒后重试...")
            time.sleep(wait_time)

    print(" CF盾处理达到最大尝试次数")
    return False

def check_cf_verification_status(tab):
    """智能检查CF验证状态 - 优化版"""
    try:
        js_code = """
        function checkCFStatus() {
            const cfTurnstile = document.querySelector('.cf-turnstile');
            if (!cfTurnstile) {
                return {exists: false, needsClick: false, isSuccess: false};
            }

            // 检查文本内容
            const cfText = (cfTurnstile.textContent || cfTurnstile.innerText || '').toLowerCase();
            const hasSuccessText = cfText.includes('success') || cfText.includes('✓') ||
                                  cfText.includes('verified') || cfText.includes('complete');

            // 检查CSS类
            const hasSuccessClass = cfTurnstile.classList.contains('success') ||
                                   cfTurnstile.classList.contains('verified') ||
                                   cfTurnstile.classList.contains('complete');

            // 检查是否有CF token（最重要的判断标准）
            const hiddenInput = cfTurnstile.querySelector('input[type="hidden"][name="cf-turnstile-response"]');
            const hasValidToken = hiddenInput && hiddenInput.value && hiddenInput.value.length > 100;

            // 检查子元素是否有成功标识
            const childElements = cfTurnstile.querySelectorAll('*');
            let hasSuccessChild = false;
            for (let el of childElements) {
                const elText = (el.textContent || el.innerText || '').toLowerCase();
                const elClass = el.className || '';
                if (elText.includes('success') || elText.includes('verified') ||
                    elClass.includes('success') || elClass.includes('verified')) {
                    hasSuccessChild = true;
                    break;
                }
            }

            // 综合判断是否成功
            const isSuccess = hasSuccessText || hasSuccessClass || hasValidToken || hasSuccessChild;

            const rect = cfTurnstile.getBoundingClientRect();
            return {
                exists: true,
                hasSuccess: isSuccess,
                needsClick: !isSuccess,
                isSuccess: isSuccess,
                text: cfTurnstile.textContent.trim(),
                hasToken: hasValidToken,
                tokenLength: hiddenInput ? hiddenInput.value.length : 0,
                position: {
                    x: Math.round(rect.x + rect.width / 2),
                    y: Math.round(rect.y + rect.height / 2)
                },
                debug: {
                    hasSuccessText: hasSuccessText,
                    hasSuccessClass: hasSuccessClass,
                    hasValidToken: hasValidToken,
                    hasSuccessChild: hasSuccessChild
                }
            };
        }
        return checkCFStatus();
        """

        result = tab.run_js(js_code)
        return result

    except Exception as e:
        print(f"检查CF验证状态失败: {e}")
        return {"exists": False, "needsClick": False, "isSuccess": False}

def smart_fill_form_v2(tab, data):
    """智能填表函数 - 增强版本"""
    try:
        print("=" * 60)
        print("开始智能填表 v2")
        print("=" * 60)

        # 检查当前页面状态
        current_url = tab.url
        print(f"当前URL: {current_url}")

        if "/Claimant/Unknown" in current_url:
            print(" 已在第二个页面（表单页面），开始填写表单")
            return fill_second_page_form(tab, data)
        else:
            print("📄 在第一个页面，需要先处理第一个页面")
            return handle_first_page_and_navigate(tab, data, mode="no_evidence")

    except Exception as e:
        print(f"智能填表失败: {e}")
        return False

def smart_fill_form_v2_with_evidence(tab, data, total_packages, jpg_files):
    """有证据模式的智能填表函数 - 增强版本"""
    try:
        print("=" * 60)
        print("开始有证据模式智能填表 v2")
        print("=" * 60)

        # 检查当前页面状态
        current_url = tab.url
        print(f"当前URL: {current_url}")

        if "/Claimant/Unknown" in current_url:
            print(" 已在第二个页面（表单页面），开始填写表单")
            return fill_second_page_form_with_evidence(tab, data, total_packages, jpg_files)
        else:
            print("📄 在第一个页面，需要先处理第一个页面")
            return handle_first_page_and_navigate(tab, data, mode="with_evidence", total_packages=total_packages, jpg_files=jpg_files)

    except Exception as e:
        print(f"有证据模式智能填表失败: {e}")
        return False

def smart_first_page_handler(tab, max_attempts=25, refresh_interval=5):
    """
    智能处理第一个页面 - 包含CF验证检测和自动操作

    Args:
        tab: DrissionPage tab对象
        max_attempts: 最大尝试次数，默认25次
        refresh_interval: 刷新间隔，每几次检测刷新一次，默认5次

    Returns:
        bool: 是否成功处理第一页面
    """
    print("开始智能处理第一个页面...")
    thread_id = threading.get_ident()

    for attempt in range(max_attempts):
        print(f"线程 {thread_id} - 第 {attempt + 1}/{max_attempts} 次检测页面状态")

        try:
            # 检查CF验证状态
            cf_status = check_cf_verification_status(tab)

            if cf_status.get('exists', False):
                if cf_status.get('isSuccess', False):
                    print(f"线程 {thread_id} - ✅ CF验证已完成，可以继续操作")

                    # CF验证成功，直接点击第3个单选框
                    print(f"线程 {thread_id} - 点击第3个单选框（不需要Claim ID的在线申请）")

                    # 查找第3个单选框（索引为2）
                    radio_buttons = tab.eles('x://input[@type="radio"]')
                    if len(radio_buttons) >= 3:
                        third_radio = radio_buttons[2]  # 索引2是第3个
                        third_radio.click()
                        print(f"线程 {thread_id} - ✅ 成功点击第3个单选框")
                        time.sleep(2)

                        # 点击Next按钮
                        next_button = tab.ele('x://button[@type="submit"]', timeout=5)
                        if next_button:
                            next_button.click()
                            print(f"线程 {thread_id} - ✅ 成功点击Next按钮")
                            time.sleep(5)

                            # 检查是否跳转到下一页
                            current_url = tab.url
                            if "Unknown" in current_url or "claim" in current_url.lower():
                                print(f"线程 {thread_id} - ✅ 成功跳转到下一页: {current_url}")
                                return True
                            else:
                                print(f"线程 {thread_id} - ⚠️ 页面未跳转，当前URL: {current_url}")
                        else:
                            print(f"线程 {thread_id} - ❌ 未找到Next按钮")
                    else:
                        print(f"线程 {thread_id} - ❌ 单选框数量不足，当前数量: {len(radio_buttons)}")

                else:
                    print(f"线程 {thread_id} - ⏳ CF验证仍在进行中...")

                    # 每5次检测刷新一次页面
                    if (attempt + 1) % refresh_interval == 0:
                        print(f"线程 {thread_id} - 第 {attempt + 1} 次检测，执行页面刷新")
                        tab.refresh()
                        time.sleep(10)  # 🔧 修改：刷新后等待10秒给验证码更多时间

                        # 页面稳定后点击指定坐标
                        print(f"线程 {thread_id} - 页面稳定后点击坐标 (274, 536)")
                        tab.actions.move_to((274, 536)).click()
                        time.sleep(5)

                        # 再次检查CF状态
                        cf_status_after_click = check_cf_verification_status(tab)
                        if cf_status_after_click.get('isSuccess', False):
                            print(f"线程 {thread_id} - ✅ 点击后CF验证完成")
                            continue  # 下次循环会处理成功状态
                        else:
                            print(f"线程 {thread_id} - ⏳ 点击后CF验证仍在进行中")
            else:
                print(f"线程 {thread_id} - ❌ 未检测到CF验证框")

            # 等待5秒再进行下一次检测
            time.sleep(5)

        except Exception as e:
            print(f"线程 {thread_id} - 处理第一页面异常: {e}")
            time.sleep(5)

    print(f"线程 {thread_id} - ❌ 第一页面处理失败，已达到最大尝试次数")
    return False

def handle_first_page_and_navigate(tab, data, mode="no_evidence", total_packages=None, jpg_files=None):
    """处理第一个页面并导航到第二个页面"""
    try:
        print("处理第一个页面...")

        #  修复：等待第一个页面完全加载，包括CF验证处理
        print("等待第一个页面完全加载...")
        max_wait_time = 15  # 增加等待时间
        wait_interval = 3   # 增加检查间隔
        waited_time = 0
        page_loaded = False

        while waited_time < max_wait_time and not page_loaded:
            time.sleep(wait_interval)
            waited_time += wait_interval

            try:
                current_title = tab.title
                current_url = tab.url
                print(f"等待中... ({waited_time}s)")
                print(f"  标题: {current_title}")
                print(f"  URL: {current_url}")

                # 检查页面基本内容
                page_html = tab.html.lower()
                has_claim_content = "claim id" in page_html or "claim" in page_html or "poppi" in page_html

                # 检查单选框
                radio_buttons = tab.eles('x://input[@type="radio"]')
                radio_count = len(radio_buttons)
                has_enough_radios = radio_count >= 3

                #  重要：检查CF验证状态
                cf_status = check_cf_verification_status(tab)
                cf_completed = not cf_status.get('needsClick', True) if cf_status.get('exists', False) else True

                print(f"  页面内容检查: claim内容={has_claim_content}, 单选框数量={radio_count}")
                print(f"  CF验证状态: 存在={cf_status.get('exists', False)}, 需要点击={cf_status.get('needsClick', False)}")

                #  修复：只有当页面内容加载完成且CF验证完成时才认为页面加载完成
                if has_claim_content and has_enough_radios and cf_completed:
                    print(f" 第一个页面完全加载完成 (等待了{waited_time}秒)")
                    page_loaded = True
                    break
                elif has_claim_content and has_enough_radios and cf_status.get('exists', False):
                    print(f"⏳ 页面内容已加载，但CF验证仍在处理中...")
                else:
                    print(f"⏳ 页面仍在加载中...")

            except Exception as e:
                print(f"检查页面加载状态时出错: {e}")

        if not page_loaded:
            print(f" 页面加载超时，但继续尝试操作")

        #  额外等待确保页面完全稳定
        print("额外等待页面稳定...")
        time.sleep(5)

        #  修复：如果CF验证仍需要处理，先处理CF验证
        print("检查并处理CF验证...")
        cf_status = check_cf_verification_status(tab)
        if cf_status.get('exists', False) and cf_status.get('needsClick', True):
            print("🛡️ 检测到CF验证需要处理...")
            position = cf_status.get('position', {})
            if position.get('x') and position.get('y'):
                x, y = position['x'], position['y']
                print(f"点击CF验证框位置: ({x}, {y})")

                # 滚动到CF验证框
                try:
                    cf_element = tab.ele('x://*[contains(@class, "cf-turnstile")]')
                    cf_element.scroll.to_see()
                    time.sleep(2)
                except:
                    pass

                # 点击CF验证框
                tab.actions.move_to((x, y)).click()
                time.sleep(5)

                # 再次检查CF状态
                cf_status_after = check_cf_verification_status(tab)
                if cf_status_after.get('hasSuccess', False):
                    print(" CF验证处理成功")
                else:
                    print(" CF验证可能仍在处理中，继续操作")
        else:
            print(" CF验证已完成或不需要处理")

        #  修复：点击第3个单选框前再次确认页面状态
        print("确认页面状态并点击第3个单选框...")
        try:
            # 等待一下确保页面稳定
            time.sleep(3)

            radio_buttons = tab.eles('x://input[@type="radio"]')
            print(f"找到 {len(radio_buttons)} 个单选框")

            if len(radio_buttons) >= 3:
                # 点击第3个单选框（UnknownFileOnline）
                target_radio = radio_buttons[2]  # 第3个（索引2）
                target_radio.scroll.to_see()
                time.sleep(1)
                target_radio.click()
                print(" 成功点击第3个单选框")
                time.sleep(3)  # 等待页面响应
            else:
                print(" 单选框数量不足")
                return False
        except Exception as e:
            print(f"点击单选框失败: {e}")
            return False

        #  修复：查找并点击Next按钮，增加更好的等待和检查
        print("查找并点击Next按钮...")

        # 先等待Next按钮出现
        next_button_found = False
        max_wait_for_button = 10
        wait_count = 0

        while wait_count < max_wait_for_button and not next_button_found:
            try:
                next_button = tab.ele('x://button[contains(text(), "Next")]', timeout=1)
                if next_button:
                    next_button_found = True
                    print(" 找到Next按钮")
                    break
            except:
                wait_count += 1
                print(f"等待Next按钮出现... ({wait_count}/{max_wait_for_button})")
                time.sleep(1)

        if not next_button_found:
            print(" 未找到Next按钮")
            return False

        # 点击Next按钮
        try:
            next_button.scroll.to_see()
            time.sleep(2)
            next_button.click()
            print(" 成功点击Next按钮")
        except Exception as e:
            print(f"点击Next按钮失败: {e}")
            return False

        #  修复：等待页面跳转，处理中间页面
        print("等待页面跳转...")
        max_wait_for_navigation = 20
        wait_count = 0
        navigation_success = False
        refresh_count = 0  # 刷新计数器
        last_refresh_at = 0  # 上次刷新的时间点

        while wait_count < max_wait_for_navigation and not navigation_success:
            time.sleep(1)
            wait_count += 1

            try:
                current_url = tab.url
                current_title = tab.title
                print(f"跳转检查 ({wait_count}/{max_wait_for_navigation}): {current_url}")

                if "/Claimant/Unknown" in current_url:
                    print(" 成功跳转到第二个页面（表单页面）")
                    navigation_success = True
                    break
                elif "/Claimant/FilingOption" in current_url:
                    print("📄 检测到中间页面（FilingOption），需要继续处理...")

                    #  新逻辑：如果5次没跳转，刷新页面给验证码时间
                    if wait_count - last_refresh_at >= 5:
                        print(f"🔄 已等待{wait_count - last_refresh_at}次未跳转，刷新页面给验证码时间...")
                        tab.refresh()
                        refresh_count += 1
                        last_refresh_at = wait_count
                        print(f" 第{refresh_count}次刷新页面完成，等待页面重新加载...")
                        time.sleep(10)  # 🔧 修改：刷新后等待10秒给验证码更多时间
                        continue

                    #  修复：在第3次检查后，如果还在FilingOption页面，尝试查找并点击Next按钮
                    if wait_count >= 3 and (wait_count - last_refresh_at) >= 3:
                        print(" 尝试在FilingOption页面查找Next按钮...")
                        try:
                            # 查找Next按钮
                            next_button_filing = tab.ele('x://button[contains(text(), "Next")]', timeout=2)
                            if next_button_filing:
                                print(" 在FilingOption页面找到Next按钮")
                                next_button_filing.scroll.to_see()
                                time.sleep(1)
                                next_button_filing.click()
                                print(" 点击FilingOption页面的Next按钮")
                                time.sleep(3)  # 等待跳转
                            else:
                                print(" 在FilingOption页面未找到Next按钮")
                        except Exception as e:
                            print(f"在FilingOption页面查找Next按钮失败: {e}")
                elif "just a moment" in current_title.lower():
                    print("⏳ 检测到CF验证页面，继续等待...")

            except Exception as e:
                print(f"检查跳转状态失败: {e}")

        if navigation_success:
            # 额外等待确保第二个页面完全加载
            print("等待第二个页面完全加载...")
            print(f"📊 跳转统计: 总检查次数={wait_count}, 刷新次数={refresh_count}")
            time.sleep(5)
            
            # 根据模式调用不同的函数
            if mode == "with_evidence" and total_packages is not None and jpg_files is not None:
                print("🔍 使用有证据模式填写表单")
                return fill_second_page_form_with_evidence(tab, data, total_packages, jpg_files)
            else:
                print(" 使用无证据模式填写表单")
                return fill_second_page_form(tab, data)
        else:
            #  最后检查：如果还没有到达目标页面，检查当前页面状态
            final_url = tab.url
            print(f" 页面跳转未完成，当前URL: {final_url}")
            print(f"📊 跳转统计: 总检查次数={wait_count}, 刷新次数={refresh_count}")

            if "/Claimant/FilingOption" in final_url:
                print(" 仍在FilingOption页面，尝试最后一次处理...")
                try:
                    # 最后一次尝试点击Next按钮
                    next_button_final = tab.ele('x://button[contains(text(), "Next")]', timeout=3)
                    if next_button_final:
                        next_button_final.scroll.to_see()
                        time.sleep(1)
                        next_button_final.click()
                        print(" 最后一次点击Next按钮成功")
                        time.sleep(5)

                        # 检查是否成功跳转
                        final_check_url = tab.url
                        if "/Claimant/Unknown" in final_check_url:
                            print(" 最终成功跳转到表单页面")
                            # 根据模式调用不同的函数
                            if mode == "with_evidence" and total_packages is not None and jpg_files is not None:
                                print("🔍 使用有证据模式填写表单")
                                return fill_second_page_form_with_evidence(tab, data, total_packages, jpg_files)
                            else:
                                print(" 使用无证据模式填写表单")
                                return fill_second_page_form(tab, data)
                except Exception as e:
                    print(f"最后一次处理失败: {e}")

            print(" 页面跳转失败")
            return False

    except Exception as e:
        print(f"处理第一个页面失败: {e}")
        return False

def fill_second_page_form(tab, data):
    """填写第二个页面的表单"""
    try:
        print("=" * 50)
        print("开始填写第二个页面表单")
        print("=" * 50)

        # 等待页面完全加载
        time.sleep(3)

        # 初始化选择的输入框索引变量
        selected_input_index_for_product = None
        
        # 1. 填写个人信息
        print("1. 填写个人信息...")
        if not fill_personal_info(tab, data):
            return False

        # 2. 处理购买信息
        print("\n2. 处理购买信息...")

        try:
            # 滚动到购买信息部分
            purchase_yes = tab.ele('x://input[@name="PurchasedPoppiProduct"][@value="Yes"]')
            purchase_yes.scroll.to_see()
            time.sleep(1)

            # 点击Yes
            purchase_yes.click()
            print(" 点击购买确认: Yes")
            time.sleep(2)

            # 随机选择一个产品数量输入框并填写
            quantity_inputs = [
                ('UnitsOfSingleCan', [24, 26, 28, 30, 32]),
                ('UnitsOf4Pack', [6, 7, 8, 9, 10]),
                ('UnitsOf8Pack', [3, 4, 5, 6, 7]),
                ('UnitsOf12Pack', [2, 3, 4, 5, 6]),
                ('UnitsOf15Pack', [2, 3, 4, 5, 6])
            ]

            # 随机选择一个输入框
            selected_index = random.randint(0, 4)
            input_id, quantity_range = quantity_inputs[selected_index]
            selected_quantity = random.choice(quantity_range)

            print(f"选择第{selected_index + 1}个输入框: {input_id}, 数量: {selected_quantity}")

            # 填写数量
            quantity_input = tab.ele(f'x://input[@id="{input_id}"]')
            quantity_input.clear()
            quantity_input.input(str(selected_quantity))
            print(f" 填写数量: {selected_quantity}")

            #  重要：记录选择的输入框索引，用于后面选择对应的产品
            selected_input_index_for_product = selected_index

        except Exception as e:
            print(f"处理购买信息失败: {e}")
            return False

        # 3. 处理产品详细信息
        print("\n3. 处理产品详细信息...")

        try:
            # 滚动到产品选择部分
            product_select = tab.ele('x://select[@id="Products_1__Product"]')
            product_select.scroll.to_see()
            time.sleep(1)

            #  智能处理：根据选择的数量输入框自动选择对应的产品
            if selected_input_index_for_product is not None:
                #  修复：输入框与产品的智能对应关系（注意大小写）
                input_product_mapping = [
                    ('UnitsOfSingleCan', 'Single Can Unit'),
                    ('UnitsOf4Pack', '4-pack Unit'),      # 注意：实际页面是小写p
                    ('UnitsOf8Pack', '8-pack Unit'),      # 注意：实际页面是小写p
                    ('UnitsOf12Pack', '12-pack Unit'),    # 注意：实际页面是小写p
                    ('UnitsOf15Pack', '15-pack Unit')     # 注意：实际页面是小写p
                ]

                #  重要：先用JavaScript检查实际的产品选项
                js_check_options = """
                var select = document.getElementById('Products_1__Product');
                var options = [];
                if (select && select.options) {
                    for (var i = 0; i < select.options.length; i++) {
                        options.push({
                            index: i,
                            value: select.options[i].value,
                            text: select.options[i].text
                        });
                    }
                }
                return options;
                """

                try:
                    actual_options = tab.run_js(js_check_options)
                    print(f"  实际产品选项:")
                    for opt in actual_options:
                        print(f"    索引{opt['index']}: {opt['text']} (value: {opt['value']})")
                except Exception as e:
                    print(f"  无法获取实际选项: {e}")
                    actual_options = []

                #  重要修复：根据实际测试结果，输入框索引直接对应产品索引（不需要+1）
                # 因为select选项的索引0是空选项，索引1是第一个产品，以此类推
                # UnitsOf8Pack (索引2) 应该对应产品索引2（4-pack），但我们要的是索引3（8-pack）
                # 所以实际上应该是 selected_input_index_for_product + 1
                product_index = selected_input_index_for_product + 1

                # 获取对应的输入框名称和产品名称
                input_name = input_product_mapping[selected_input_index_for_product][0] if selected_input_index_for_product < len(input_product_mapping) else "未知输入框"
                expected_product = input_product_mapping[selected_input_index_for_product][1] if selected_input_index_for_product < len(input_product_mapping) else "未知产品"

                #  修复：使用实际选项或默认选项
                if actual_options and len(actual_options) > product_index:
                    expected_text = actual_options[product_index]['text']
                else:
                    expected_text = expected_product

                print(f" 智能产品选择:")
                print(f"  输入框: {input_name} (索引{selected_input_index_for_product})")
                print(f"  对应产品: {expected_product}")
                print(f"  产品索引: {product_index}")
                print(f"  预期选择: {expected_text}")

                #  修复：检查索引范围
                max_options = len(actual_options) if actual_options else 6  # 默认6个选项

                if product_index < max_options:
                    #  修复：使用正确的DrissionPage API
                    try:
                        # 先获取当前选择，用于调试
                        current_selection = product_select.value
                        print(f"  当前产品选择: {current_selection}")

                        #  调试：选择产品前后的详细检查
                        print(f"  准备选择产品索引: {product_index}")

                        # 选择产品
                        product_select.select.by_index(product_index)

                        # 验证选择结果
                        time.sleep(1)
                        new_selection = product_select.value

                        #  调试：使用JavaScript验证实际选择的索引
                        js_verify = f"""
                        var select = document.getElementById('Products_1__Product');
                        return {{
                            selectedIndex: select.selectedIndex,
                            selectedValue: select.value,
                            selectedText: select.options[select.selectedIndex].text,
                            targetIndex: {product_index},
                            targetText: select.options[{product_index}] ? select.options[{product_index}].text : '索引不存在'
                        }};
                        """

                        try:
                            verify_result = tab.run_js(js_verify)
                            print(f"   选择验证:")
                            print(f"    实际选择索引: {verify_result['selectedIndex']}")
                            print(f"    实际选择文本: {verify_result['selectedText']}")
                            print(f"    目标索引: {verify_result['targetIndex']}")
                            print(f"    目标文本: {verify_result['targetText']}")

                            # 如果选择的索引不正确，尝试修正
                            if verify_result['selectedIndex'] != product_index:
                                print(f"   索引选择不正确，尝试修正...")
                                # 直接使用JavaScript设置正确的索引
                                js_fix = f"""
                                var select = document.getElementById('Products_1__Product');
                                select.selectedIndex = {product_index};
                                select.dispatchEvent(new Event('change'));
                                return select.options[{product_index}].text;
                                """
                                fixed_text = tab.run_js(js_fix)
                                print(f"   修正后选择: {fixed_text}")

                        except Exception as e:
                            print(f"  验证过程出错: {e}")

                        #  修复：使用正确的方式获取选项文本
                        try:
                            # 使用JavaScript获取选项文本
                            js_code = f"""
                            var select = document.getElementById('Products_1__Product');
                            if (select && select.options && select.options[{product_index}]) {{
                                return select.options[{product_index}].text;
                            }}
                            return '未知产品';
                            """
                            selected_text = tab.run_js(js_code)
                        except:
                            selected_text = expected_product

                        # 验证选择是否正确（忽略大小写差异）
                        if selected_text.lower() == expected_product.lower():
                            print(f" 智能选择成功: {selected_text}")
                            print(f"  输入框{input_name} → 产品{selected_text} ✓")
                        else:
                            print(f" 选择结果: {selected_text} (期望: {expected_product})")
                        print(f"  验证选择结果: {new_selection}")

                    except Exception as e:
                        print(f"产品选择过程出错: {e}")
                        # 使用备用方法
                        product_select.select.by_index(product_index)
                        print(f" 选择产品: {expected_product} (索引{product_index}, 对应第{selected_input_index_for_product + 1}个输入框)")
                else:
                    # 如果索引超出范围，选择最后一个
                    last_index = max_options - 1
                    product_select.select.by_index(last_index)
                    print(f" 选择产品: 最后一个选项 (索引超出范围，选择索引{last_index})")
            else:
                # 如果变量为None，使用默认选择
                product_select.select.by_index(2)  # 默认选择第2个产品
                print(" 未找到输入框选择记录，使用默认产品选择")

            # 随机选择月份
            months = ['January', 'February', 'March', 'April', 'May', 'June',
                     'July', 'August', 'September', 'October', 'November', 'December']
            selected_month = random.choice(months)

            month_select = tab.ele('x://select[@id="Products_1__Month"]')
            month_select.select.by_text(selected_month)
            print(f" 选择月份: {selected_month}")

            # 随机选择年份
            years = ['2021', '2022', '2023', '2024']
            selected_year = random.choice(years)

            year_select = tab.ele('x://select[@id="Products_1__Year"]')
            year_select.select.by_text(selected_year)
            print(f" 选择年份: {selected_year}")

            # 填写购买地点
            places = ['Target Store', 'Walmart', 'Amazon', 'Whole Foods', 'CVS Pharmacy']
            selected_place = random.choice(places)

            place_input = tab.ele('x://input[@id="Products_1__Place"]')
            place_input.clear()
            place_input.input(selected_place)
            print(f" 购买地点: {selected_place}")

            # 填写购买数量
            units_input = tab.ele('x://input[@id="Products_1__NumberOfUnits"]')
            units_input.clear()
            units_input.input(str(selected_quantity))
            print(f" 购买数量: {selected_quantity}")

        except Exception as e:
            print(f"处理产品详细信息失败: {e}")
            return False

        # 4. 处理支付方式
        print("\n4. 处理支付方式...")

        try:
            # 滚动到支付方式部分
            venmo_radio = tab.ele('x://input[@name="PayBy"][@value="Venmo"]')
            venmo_radio.scroll.to_see()
            time.sleep(1)

            # 选择Venmo
            venmo_radio.click()
            print(" 选择支付方式: Venmo")
            time.sleep(2)

            # 选择Email Address
            email_radio = tab.ele('x://input[@name="VenmoType"][@value="Email Address"]')
            email_radio.click()
            print(" 选择Venmo类型: Email Address")
            time.sleep(2)

            # 填写Venmo邮箱
            venmo_email_input = tab.ele('x://input[@id="VenmoEmail"]')
            venmo_email_input.clear()
            venmo_email_input.input(data['email'])
            print(f" Venmo邮箱: {data['email']}")

            # 填写确认邮箱
            venmo_email2_input = tab.ele('x://input[@id="VenmoEmail2"]')
            venmo_email2_input.clear()
            venmo_email2_input.input(data['email'])
            print(" 确认Venmo邮箱")

        except Exception as e:
            print(f"处理支付方式失败: {e}")
            return False

        # 5. 处理CF验证
        print("\n5. 检查并处理CF验证...")

        try:
            cf_status = check_cf_verification_status(tab)

            if cf_status.get('exists', False):
                if cf_status.get('hasSuccess', False):
                    print(" CF验证已完成，显示Success状态")
                elif cf_status.get('needsClick', True):
                    print("🛡️ CF验证需要点击，开始处理...")

                    # 滚动到CF验证框
                    cf_element = tab.ele('x://*[contains(@class, "cf-turnstile")]')
                    cf_element.scroll.to_see()
                    time.sleep(2)

                    # 获取CF验证框坐标并点击
                    position = cf_status.get('position', {})
                    if position.get('x') and position.get('y'):
                        x, y = position['x'], position['y']
                        print(f"点击CF验证框位置: ({x}, {y})")
                        tab.actions.move_to((x, y)).click()
                        time.sleep(3)

                        # 再次检查状态
                        cf_status_after = check_cf_verification_status(tab)
                        if cf_status_after.get('hasSuccess', False):
                            print(" CF验证点击后成功")
                        else:
                            print(" CF验证可能仍在处理中")
                    else:
                        print(" 无法获取CF验证框坐标")
            else:
                print(" 未检测到CF验证框")

        except Exception as e:
            print(f"处理CF验证失败: {e}")

        # 6. 勾选同意条款并提交
        print("\n6. 勾选同意条款并提交...")

        try:
            # 滚动到同意条款
            agree_checkbox = tab.ele('x://input[@id="IAgree"]')
            agree_checkbox.scroll.to_see()
            time.sleep(1)

            #  修复：勾选同意条款，使用正确的API
            try:
                # 检查复选框是否已勾选
                is_checked = agree_checkbox.states.is_checked
                print(f"同意条款当前状态: {'已勾选' if is_checked else '未勾选'}")

                if not is_checked:
                    agree_checkbox.click()
                    time.sleep(1)
                    # 再次验证
                    is_checked_after = agree_checkbox.states.is_checked
                    if is_checked_after:
                        print(" 成功勾选同意条款")
                    else:
                        print(" 勾选同意条款可能失败，尝试再次点击")
                        agree_checkbox.click()
                        time.sleep(1)
                else:
                    print(" 同意条款已勾选")
            except Exception as e:
                print(f"检查同意条款状态失败: {e}")
                # 如果API调用失败，直接点击
                print("直接点击同意条款复选框...")
                agree_checkbox.click()
                time.sleep(1)
                print(" 已点击同意条款复选框")

            time.sleep(2)

            # 点击提交按钮
            submit_button = tab.ele('x://button[contains(text(), "Agree and Submit")]')
            submit_button.scroll.to_see()
            time.sleep(1)

            print("点击提交按钮...")
            submit_button.click()
            print(" 点击提交按钮")

            # 等待提交处理和页面跳转
            print("等待提交处理和页面跳转...")
            time.sleep(8)  # 增加等待时间

            # 检查页面是否跳转
            current_url = tab.url
            print(f"提交后页面URL: {current_url}")

            # 检查是否有CF验证需要处理
            cf_status_after_submit = check_cf_verification_status(tab)
            if cf_status_after_submit.get('exists', False) and cf_status_after_submit.get('needsClick', True):
                print("🛡️ 提交后检测到CF验证，开始处理...")
                position = cf_status_after_submit.get('position', {})
                if position.get('x') and position.get('y'):
                    x, y = position['x'], position['y']
                    print(f"点击提交后CF验证框位置: ({x}, {y})")
                    tab.actions.move_to((x, y)).click()
                    time.sleep(5)
                    print(" 提交后CF验证处理完成")

            # 再次等待页面完全加载
            time.sleep(5)

            # 尝试获取VNB信息
            print("尝试获取VNB信息...")
            try:
                # 检查页面内容
                page_text = tab.html.lower()
                print(f"页面内容包含关键词检查:")
                print(f"  包含'vnb': {'vnb' in page_text}")
                print(f"  包含'claim': {'claim' in page_text}")
                print(f"  包含'success': {'success' in page_text}")
                print(f"  包含'thank': {'thank' in page_text}")

                # 使用JavaScript获取页面所有文本内容
                js_get_all_text = """
                function getAllTextContent() {
                    var allText = document.body.innerText || document.body.textContent || '';
                    return allText;
                }
                return getAllTextContent();
                """

                try:
                    all_page_text = tab.run_js(js_get_all_text)
                    print(f"页面完整文本内容（前200字符）: {all_page_text[:200]}...")

                    # 使用正则表达式查找VNB号码
                    import re
                    vnb_pattern = r'VNB-\d{8,12}'  # 匹配VNB-后跟8-12位数字
                    vnb_matches = re.findall(vnb_pattern, all_page_text, re.IGNORECASE)

                    if vnb_matches:
                        vnb_info = vnb_matches[0]  # 取第一个匹配的VNB号码
                        print(f" 通过正则表达式获取到VNB号码: {vnb_info}")
                        return {"success": True, "vnb_info": vnb_info}
                    else:
                        print(" 正则表达式未找到VNB号码，尝试其他方法...")

                except Exception as e:
                    print(f"JavaScript获取页面文本失败: {e}")

                # 备用方法：使用选择器查找
                vnb_selectors = [
                    'x://*[contains(text(), "VNB-")]',
                    'x://*[contains(text(), "vnb-")]',
                    'x://*[contains(text(), "Claim ID")]',
                    'x://*[contains(text(), "Your Claim ID is")]'
                ]

                vnb_info = None
                for selector in vnb_selectors:
                    try:
                        vnb_elements = tab.eles(selector, timeout=3)
                        print(f"选择器 {selector} 找到 {len(vnb_elements)} 个元素")

                        for element in vnb_elements:
                            text = element.text.strip()
                            if text and len(text) > 3:
                                print(f"  找到文本: {text}")
                                # 使用正则表达式从文本中提取VNB号码
                                import re
                                vnb_match = re.search(r'VNB-\d{8,12}', text, re.IGNORECASE)
                                if vnb_match:
                                    vnb_info = vnb_match.group()
                                    print(f"  从文本中提取到VNB号码: {vnb_info}")
                                    break
                        if vnb_info:
                            break
                    except Exception as e:
                        print(f"选择器 {selector} 处理失败: {e}")
                        continue

                if vnb_info:
                    print(f" 获取到有效VNB信息: {vnb_info}")
                    return {"success": True, "vnb_info": vnb_info}
                else:
                    print(" 未获取到有效的VNB号码")
                    # 获取页面标题和URL作为调试信息
                    page_title = tab.title
                    page_url = tab.url
                    print(f"页面标题: {page_title}")
                    print(f"页面URL: {page_url}")
                    return {"success": False, "vnb_info": None, "page_title": page_title, "page_url": page_url, "error": "未获取到有效VNB号码"}

            except Exception as e:
                print(f"获取VNB信息失败: {e}")
                return {"success": False, "vnb_info": None, "error": f"获取VNB信息异常: {e}"}

        except Exception as e:
            print(f"提交表单失败: {e}")
            return False

    except Exception as e:
        print(f"填写第二个页面表单失败: {e}")
        return False

def fill_second_page_form_with_evidence(tab, data, total_packages, jpg_files):
    """有证据模式填写第二个页面的表单"""
    try:
        print("=" * 50)
        print("开始有证据模式填写第二个页面表单")
        print("=" * 50)

        # 等待页面完全加载
        time.sleep(3)

        # 1. 填写个人信息（与无证据模式相同）
        print("1. 填写个人信息...")
        if not fill_personal_info(tab, data):
            return False

        # 2. 处理购买信息（有证据模式）
        print("\n2. 处理购买信息（有证据模式）...")
        if not fill_purchase_info_with_evidence(tab, total_packages):
            return False

        # 3. 上传证据
        print("\n3. 上传证据...")
        if not upload_evidence_files(tab, jpg_files):
            return False

        # 4. 处理支付选项
        print("\n4. 处理支付选项...")
        if not handle_payment_options(tab, data):
            return False

        # 5. 提交表单
        print("\n5. 提交表单...")
        return submit_form_and_get_vnb(tab)

    except Exception as e:
        print(f"有证据模式填表失败: {e}")
        return False

def smart_fill_form(tab, data):
    """智能填表函数 - 兼容旧版本调用"""
    return smart_fill_form_v2(tab, data)

def smart_fill_form_with_evidence(tab, data, thread_num):
    """有证据模式的智能填表函数"""
    try:
        print("=" * 60)
        print("开始有证据模式智能填表")
        print("=" * 60)

        # 1. 生成证据
        print("1. 生成POPPI证据...")
        evidence_result = generate_poppi_evidence_for_thread(
            row_data=[
                data.get('first_name', ''),
                data.get('last_name', ''),
                data.get('address1', ''),
                data.get('city', ''),
                data.get('state', ''),
                data.get('zip', ''),
                data.get('state_full', ''),
                data.get('birth_date', ''),
                data.get('ssn', ''),
                data.get('phone1', ''),
                data.get('email', '')
            ],
            thread_name=f"thread_{thread_num}"
        )
        
        if not evidence_result:
            print(" 证据生成失败，回退到无证据模式")
            return smart_fill_form_v2(tab, data)
        
        total_packages = evidence_result.get('total_packages', 0)
        jpg_files = evidence_result.get('files', {}).get('jpg_files', [])
        
        print(f" 证据生成成功，总包数: {total_packages}")
        print(f" 生成JPG文件数量: {len(jpg_files)}")

        # 2. 执行填表
        print("2. 执行填表...")
        result = smart_fill_form_v2_with_evidence(tab, data, total_packages, jpg_files)
        
        # 3. 清理证据文件
        print("3. 清理证据文件...")
        try:
            from src.order4 import cleanup_thread_evidence_files
            cleanup_thread_evidence_files(f"thread_{thread_num}")
            print(" 证据文件清理完成")
        except Exception as e:
            print(f" 证据文件清理失败: {e}")
        
        return result

    except Exception as e:
        print(f"有证据模式填表失败: {e}")
        print("回退到无证据模式")
        return smart_fill_form_v2(tab, data)

def execute_single_claim_task3(browser_id, data, thread_num=1, mode="with_evidence"):
    """
    执行单个索赔任务 - tasks3版本
    与main.py配合使用的版本

    Args:
        browser_id: 比特浏览器ID
        data: 包含用户数据的字典
        thread_num: 线程编号
        mode: 运行模式，"no_evidence" 或 "with_evidence"

    Returns:
        dict: 执行结果
    """
    try:
        print(f"线程 {thread_num} 开始执行tasks3填表任务...")
        print(f"使用浏览器ID: {browser_id}")

        # 使用指定的浏览器ID连接比特浏览器
        browser, tab = connect_bit_browser_by_id(browser_id)
        if not browser or not tab:
            return {"success": False, "error": "连接比特浏览器失败"}

        print("成功连接比特浏览器")

        # 打开项目网址
        print("打开项目网址: https://veritaconnect.com/poppisettlement/Claimant")
        tab.get("https://veritaconnect.com/poppisettlement/Claimant")

        # 等待页面加载和5秒盾
        print("等待页面加载和5秒盾...")
        time.sleep(8)

        # 根据模式执行不同的填表逻辑
        if mode == "with_evidence":
            print("🔍 使用有证据模式")
            result = smart_fill_form_with_evidence(tab, data, thread_num)
        else:
            print(" 使用无证据模式")
            result = smart_fill_form(tab, data)

        if result and isinstance(result, dict):
            vnb_info = result.get("vnb_info")
            if vnb_info and vnb_info.strip() and "VNB" in vnb_info.upper():
                print(f" 智能填表任务完成，获取到有效VNB信息: {vnb_info}")
                return {"success": True, "claim_id": vnb_info, "message": "填表任务完成"}
            else:
                print(" 智能填表任务失败，未获取到有效VNB信息")
                error_msg = f"未获取到有效VNB号码，获取到的信息: {vnb_info or '无'}"
                return {"success": False, "error": error_msg}
        elif result:
            print(" 智能填表任务失败，未获取到VNB信息")
            return {"success": False, "error": "填表完成但未获取到VNB号码"}
        else:
            print(" 智能填表任务失败")
            return {"success": False, "error": "填表任务失败"}

    except Exception as e:
        print(f"执行任务异常: {e}")
        return {"success": False, "error": str(e)}
    finally:
        # 清理资源
        try:
            if 'browser' in locals() and browser:
                browser.quit()
        except:
            pass

def connect_bit_browser_by_id(browser_id):
    """使用指定的浏览器ID连接比特浏览器"""
    try:
        print(f"使用浏览器ID {browser_id} 连接...")
        headers = {'Content-Type': 'application/json'}

        # 打开指定的浏览器
        open_data = {"id": browser_id}
        open_response = requests.post(f"http://127.0.0.1:54345/browser/open",
                                    data=json.dumps(open_data),
                                    headers=headers)

        if open_response.status_code != 200:
            print(f"打开浏览器失败: {open_response.text}")
            return None, None

        open_response_json = open_response.json()
        if 'data' not in open_response_json:
            print("打开浏览器失败: 响应中没有data字段")
            return None, None

        driver = open_response_json['data']['driver']
        http = open_response_json['data']['http']
        print(f"获取到调试端口信息: {http}")

        # 等待浏览器启动
        time.sleep(3)

        # 使用DrissionPage连接
        co = ChromiumOptions()
        co.set_browser_path(driver)
        co.set_address(http)

        browser = Chromium(co)
        tab = browser.latest_tab

        return browser, tab

    except Exception as e:
        print(f"连接指定浏览器失败: {e}")
        return None, None

def test_smart_fill_form_v2_with_list_data():
    """使用list01.txt数据测试智能填表v2功能"""
    try:
        print("=" * 60)
        print("使用list01.txt数据测试智能填表v2功能")
        print("=" * 60)

        # 连接比特浏览器
        browser, tab = connect_bit_browser()
        if not browser or not tab:
            print(" 连接比特浏览器失败")
            return False

        print(" 成功连接比特浏览器")

        # 打开目标网址
        print("打开项目网址: https://veritaconnect.com/poppisettlement/Claimant")
        tab.get("https://veritaconnect.com/poppisettlement/Claimant")

        # 等待页面加载
        print("等待页面加载...")
        time.sleep(5)

        # 读取list01.txt第一条数据
        try:
            with open('list01.txt', 'r', encoding='utf-8') as f:
                lines = f.readlines()

            if not lines or not lines[0].strip():
                print(" list01.txt文件为空或第一行为空")
                return False

            # 解析第一条数据
            data_parts = lines[0].strip().split('----')
            if len(data_parts) < 10:
                print(f" 数据格式不正确，期望至少10个字段，实际{len(data_parts)}个")
                print(f"数据内容: {lines[0].strip()}")
                return False

            test_data = {
                'first_name': data_parts[0],
                'last_name': data_parts[1],
                'address1': data_parts[2],
                'city': data_parts[3],
                'state': data_parts[4],
                'zip': data_parts[5].zfill(5),  # 确保邮编是5位
                'phone1': data_parts[8] if len(data_parts) > 8 else '5551234567',
                'phone2': data_parts[9] if len(data_parts) > 9 else '5551234567',
                'email': data_parts[10] if len(data_parts) > 10 else data_parts[9]  # 最后一个字段是邮箱
            }

            print(" 使用的测试数据:")
            for key, value in test_data.items():
                print(f"  {key}: {value}")

        except Exception as e:
            print(f" 读取list01.txt失败: {e}")
            return False

        # 执行智能填表v2
        print("\n开始执行智能填表v2...")
        result = smart_fill_form_v2(tab, test_data)

        if result:
            if isinstance(result, dict):
                print(" 智能填表v2执行成功")
                vnb_info = result.get('vnb_info')
                if vnb_info:
                    print(f" 获取到VNB信息: {vnb_info}")

                    # 将成功的结果写入文件
                    try:
                        with open('listsp_ok.txt', 'a', encoding='utf-8') as f:
                            f.write(f"{lines[0].strip()}----{vnb_info}\n")
                        print(" 结果已写入listsp_ok.txt")
                    except Exception as e:
                        print(f" 写入结果文件失败: {e}")
                else:
                    print(" 未获取到VNB信息")
            else:
                print(" 智能填表v2执行成功（返回布尔值）")
        else:
            print(" 智能填表v2执行失败")

        # 保持浏览器打开以便观察
        print("\n保持浏览器打开30秒以便观察...")
        time.sleep(30)

        return True

    except Exception as e:
        print(f"测试智能填表v2功能异常: {e}")
        return False
    finally:
        # 清理资源
        try:
            if 'browser' in locals() and browser:
                browser.quit()
        except:
            pass

def test_smart_fill_form_v2():
    """测试智能填表v2功能"""
    try:
        print("=" * 60)
        print("测试智能填表v2功能")
        print("=" * 60)

        # 连接比特浏览器
        browser, tab = connect_bit_browser()
        if not browser or not tab:
            print(" 连接比特浏览器失败")
            return False

        print(" 成功连接比特浏览器")

        # 打开目标网址
        print("打开项目网址: https://veritaconnect.com/poppisettlement/Claimant")
        tab.get("https://veritaconnect.com/poppisettlement/Claimant")

        # 等待页面加载
        print("等待页面加载...")
        time.sleep(5)

        # 测试数据
        test_data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'address1': '123 Main Street',
            'city': 'New York',
            'state': 'NY',
            'zip': '10001',
            'email': '<EMAIL>'
        }

        # 执行智能填表v2
        print("开始执行智能填表v2...")
        result = smart_fill_form_v2(tab, test_data)

        if result:
            if isinstance(result, dict):
                print(" 智能填表v2执行成功")
                vnb_info = result.get('vnb_info')
                if vnb_info:
                    print(f" 获取到VNB信息: {vnb_info}")
                else:
                    print(" 未获取到VNB信息")
            else:
                print(" 智能填表v2执行成功（返回布尔值）")
        else:
            print(" 智能填表v2执行失败")

        # 保持浏览器打开以便观察
        print("\n保持浏览器打开60秒以便观察...")
        time.sleep(60)

        return True

    except Exception as e:
        print(f"测试智能填表v2功能异常: {e}")
        return False
    finally:
        # 清理资源
        try:
            if 'browser' in locals() and browser:
                browser.quit()
        except:
            pass

def test_cf_verification():
    """测试CF验证功能"""
    try:
        print("=" * 60)
        print("测试CF验证功能")
        print("=" * 60)

        # 连接比特浏览器
        browser, tab = connect_bit_browser()
        if not browser or not tab:
            print(" 连接比特浏览器失败")
            return False

        print(" 成功连接比特浏览器")

        # 打开项目网址
        print("打开项目网址: https://veritaconnect.com/poppisettlement/Claimant")
        tab.get("https://veritaconnect.com/poppisettlement/Claimant")

        # 等待页面加载
        print("等待页面加载...")
        time.sleep(5)

        # 创建CF检查器
        cf_checker = CloudflareChecker()

        # 使用JavaScript分析CF状态
        print("使用JavaScript分析CF状态...")
        cf_analysis = cf_checker.analyze_cf_status_with_js(tab)

        if cf_analysis:
            print("CF状态分析结果:")
            page_info = cf_analysis.get('pageInfo', {})
            cf_elements = cf_analysis.get('cfElements', {})
            page_content = cf_analysis.get('pageContent', {})

            print(f"  页面标题: {page_info.get('title', '无')}")
            print(f"  页面URL: {page_info.get('url', '无')}")
            print(f"  页面状态: {page_info.get('readyState', '无')}")
            print(f"  CF div数量: {len(cf_elements.get('cfDivs', []))}")
            print(f"  Turnstile div数量: {len(cf_elements.get('turnstileDivs', []))}")
            print(f"  复选框数量: {len(cf_elements.get('checkboxes', []))}")
            print(f"  iframe数量: {len(cf_elements.get('iframes', []))}")
            print(f"  按钮数量: {len(cf_elements.get('buttons', []))}")
            print(f"  可点击CF元素数量: {len(cf_elements.get('clickableElements', []))}")
            print(f"  有单选框: {page_content.get('hasRadioButtons', False)}")
            print(f"  有表单元素: {page_content.get('hasFormElements', False)}")

            # 显示详细的CF元素信息
            cf_divs = cf_elements.get('cfDivs', [])
            if cf_divs:
                print("\nCF div详情:")
                for i, div in enumerate(cf_divs):
                    pos = div.get('position', {})
                    print(f"  CF div {i+1}: {div.get('className', '无class')}")
                    print(f"    位置: x={pos.get('x', 0)}, y={pos.get('y', 0)}")
                    print(f"    大小: {pos.get('width', 0)}x{pos.get('height', 0)}")
                    print(f"    可见: {div.get('visible', False)}")

            turnstile_divs = cf_elements.get('turnstileDivs', [])
            if turnstile_divs:
                print("\nTurnstile div详情:")
                for i, div in enumerate(turnstile_divs):
                    pos = div.get('position', {})
                    print(f"  Turnstile div {i+1}: {div.get('className', '无class')}")
                    print(f"    位置: x={pos.get('x', 0)}, y={pos.get('y', 0)}")
                    print(f"    大小: {pos.get('width', 0)}x{pos.get('height', 0)}")
                    print(f"    可见: {div.get('visible', False)}")

            checkboxes = cf_elements.get('checkboxes', [])
            if checkboxes:
                print("\n复选框详情:")
                for i, checkbox in enumerate(checkboxes):
                    pos = checkbox.get('position', {})
                    print(f"  复选框 {i+1}: {checkbox.get('className', '无class')}")
                    print(f"    位置: x={pos.get('x', 0)}, y={pos.get('y', 0)}")
                    print(f"    大小: {pos.get('width', 0)}x{pos.get('height', 0)}")
                    print(f"    可见: {checkbox.get('visible', False)}")
                    print(f"    已选中: {checkbox.get('checked', False)}")

            # 如果检测到CF验证，尝试点击
            if cf_divs or turnstile_divs or checkboxes:
                print("\n检测到CF验证元素，尝试智能点击...")
                click_result = cf_checker.find_and_click_cf_checkbox(tab)

                if click_result:
                    print(" CF验证点击成功")

                    # 等待验证完成
                    print("等待验证完成...")
                    time.sleep(5)

                    # 再次分析状态
                    print("再次分析CF状态...")
                    cf_analysis_after = cf_checker.analyze_cf_status_with_js(tab)

                    if cf_analysis_after:
                        page_content_after = cf_analysis_after.get('pageContent', {})
                        print(f"验证后状态:")
                        print(f"  有单选框: {page_content_after.get('hasRadioButtons', False)}")
                        print(f"  有表单元素: {page_content_after.get('hasFormElements', False)}")

                        if page_content_after.get('hasRadioButtons', False):
                            print(" CF验证成功，页面已显示表单内容")
                        else:
                            print(" CF验证可能仍在进行中")
                else:
                    print(" CF验证点击失败")
            else:
                print(" 未检测到CF验证元素，页面可能已正常")

        else:
            print(" JavaScript分析失败")

        # 保持浏览器打开以便观察
        print("\n保持浏览器打开60秒以便观察...")
        time.sleep(60)

        return True

    except Exception as e:
        print(f"测试CF验证功能异常: {e}")
        return False
    finally:
        # 清理资源
        try:
            if 'browser' in locals() and browser:
                browser.quit()
        except:
            pass

def execute_single_claim_task3_standalone(mode="with_evidence"):
    """独立测试版本"""
    try:
        print("开始执行tasks3填表任务（独立测试）...")
        print(f"使用模式: {mode}")

        # 连接比特浏览器
        browser, tab = connect_bit_browser()
        if not browser or not tab:
            return {"success": False, "error": "连接比特浏览器失败"}

        print("成功连接比特浏览器")

        # 打开项目网址
        print("打开项目网址: https://veritaconnect.com/poppisettlement/Claimant")
        tab.get("https://veritaconnect.com/poppisettlement/Claimant")

        # 等待页面加载和5秒盾
        print("等待页面加载和5秒盾...")
        time.sleep(8)

        # 模拟测试数据
        test_data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'address1': '123 Main St',
            'city': 'New York',
            'state': 'NY',
            'zip': '10001',
            'email': '<EMAIL>'
        }

        # 根据模式执行不同的填表逻辑
        if mode == "with_evidence":
            print("🔍 使用有证据模式")
            result = smart_fill_form_with_evidence(tab, test_data, 1)  # thread_num=1
        else:
            print(" 使用无证据模式")
            result = smart_fill_form_v2(tab, test_data)

        if result:
            print(" 智能填表任务完成")
            return {"success": True, "message": "填表任务完成"}
        else:
            print(" 智能填表任务失败")
            return {"success": False, "error": "填表任务失败"}

    except Exception as e:
        print(f"执行任务异常: {e}")
        return {"success": False, "error": str(e)}
    finally:
        # 清理资源
        try:
            if 'browser' in locals() and browser:
                browser.quit()
        except:
            pass

def execute_single_claim_task3_with_data_management(browser_id, thread_num, mode="with_evidence"):
    """
    执行单个索赔任务 - 带数据管理的版本（读一条删除一条）
    与main.py的读一条删除一条模式配合使用

    Args:
        browser_id: 比特浏览器ID
        thread_num: 线程编号

    Returns:
        dict: 执行结果
    """
    try:
        print(f"线程 {thread_num} 开始执行tasks3填表任务（数据管理模式）...")

        # 从文件读取一条数据
        from src.file_handler import file_handler

        # 读取一条数据并立即删除
        data_line = file_handler.read_next_line()
        if not data_line:
            return {"success": False, "error": "没有更多数据"}

        print(f"线程 {thread_num} 读取到数据: {data_line[:50]}...")

        # 解析数据
        try:
            parsed_data = parse_data_line(data_line)
            print(f"线程 {thread_num} 申请人: {parsed_data['first_name']} {parsed_data['last_name']}")
        except Exception as e:
            error_msg = f"数据解析失败: {e}"
            print(f"线程 {thread_num} {error_msg}")
            file_handler.write_failed_record(data_line, error_msg)
            return {"success": False, "error": error_msg}

        # 执行填表任务
        result = execute_single_claim_task3(browser_id, parsed_data, thread_num, mode)

        # 记录结果 - 修改逻辑：只有获取到有效VNB号码才算成功，保留完整用户信息
        if result.get("success"):
            claim_id = result.get("claim_id", "")
            # 验证claim_id是否为有效的VNB号码
            if claim_id and claim_id.strip() and "VNB" in claim_id.upper():
                # 保留完整用户信息 + VNB信息
                success_record = f"{data_line}----{claim_id}"
                file_handler.write_success_record(success_record)
                print(f"线程 {thread_num} 任务完成: 成功 - Claim ID: {claim_id}")
            else:
                # 即使返回success=True，但没有有效VNB号码，仍然算失败
                error_msg = f"未获取到有效VNB号码，获取到的信息: {claim_id or '无'}"
                file_handler.write_failed_record(data_line, error_msg)
                print(f"线程 {thread_num} 任务完成: 失败 - {error_msg}")
        else:
            error_msg = result.get("error", "未知错误")
            file_handler.write_failed_record(data_line, error_msg)
            print(f"线程 {thread_num} 任务完成: 失败 - {error_msg}")

        return result

    except Exception as e:
        error_msg = f"执行任务异常: {e}"
        print(f"线程 {thread_num} {error_msg}")
        return {"success": False, "error": error_msg}

def parse_data_line(line):
    """解析数据行 - 与main.py兼容的版本"""
    try:
        parts = line.split('----')
        if len(parts) >= 10:  # 实际有10个字段
            # 邮编自动补零到5位
            zip_code = parts[5].strip()
            if zip_code.isdigit() and len(zip_code) < 5:
                zip_code = zip_code.zfill(5)

            return {
                'first_name': parts[0].strip(),
                'last_name': parts[1].strip(),
                'address1': parts[2].strip(),
                'city': parts[3].strip(),
                'state': parts[4].strip(),
                'zip': zip_code,
                'phone1': parts[6].strip(),  # 第一个电话号码
                'phone2': parts[7].strip(),  # 第二个电话号码
                'phone3': parts[8].strip(),  # 第三个电话号码（主要电话号码）
                'email': parts[9].strip()    # 邮箱地址
            }
        else:
            raise ValueError(f"数据格式不正确，期望10个字段，实际{len(parts)}个")
    except Exception as e:
        raise ValueError(f"解析数据失败: {e}")

def get_user_mode_choice():
    """获取用户选择的模式"""
    print("\n" + "="*60)
    print("请选择运行模式:")
    print("1. 无证据模式")
    print("2. 有证据模式 (默认)")
    print("="*60)
    
    while True:
        choice = input("请输入选择 (1 或 2，直接回车默认为2): ").strip()
        if not choice:
            return "with_evidence"
        elif choice == "1":
            return "no_evidence"
        elif choice == "2":
            if EVIDENCE_MODULE_AVAILABLE:
                return "with_evidence"
            else:
                print(" 证据生成模块不可用，请选择无证据模式")
                return "no_evidence"
        else:
            print(" 无效选择，请输入 1 或 2")

def fill_personal_info(tab, data):
    """填写个人信息"""
    try:
        # 滚动到FirstName字段
        first_name_input = tab.ele('x://input[@id="FirstName"]', timeout=5)
        first_name_input.scroll.to_see()
        time.sleep(1)

        # 填写姓名
        first_name_input.clear()
        first_name_input.input(data['first_name'])
        print(f" FirstName: {data['first_name']}")

        last_name_input = tab.ele('x://input[@id="LastName"]')
        last_name_input.clear()
        last_name_input.input(data['last_name'])
        print(f" LastName: {data['last_name']}")

        # 填写地址
        addr1_input = tab.ele('x://input[@id="Addr1"]')
        addr1_input.clear()
        addr1_input.input(data['address1'])
        print(f" Address: {data['address1']}")

        # 填写城市
        city_input = tab.ele('x://input[@id="City"]')
        city_input.clear()
        city_input.input(data['city'])
        print(f" City: {data['city']}")

        # 选择州
        state_select = tab.ele('x://select[@id="St"]')
        state_select.select.by_value(data['state'])
        print(f" State: {data['state']}")

        # 填写邮编
        zip_input = tab.ele('x://input[@id="Zip"]')
        zip_input.clear()
        zip_input.input(data['zip'])
        print(f" Zip: {data['zip']}")

        # 选择国家（默认美国）
        country_select = tab.ele('x://select[@id="Country"]')
        country_select.select.by_value('UNITED STATES')
        print(" Country: UNITED STATES")

        # 填写电话
        phone_input = tab.ele('x://input[@id="DaytimePhoneNumber"]')
        phone_input.clear()
        phone_input.input(data.get('phone1', '************'))
        print(" Daytime Phone")

        evening_phone_input = tab.ele('x://input[@id="EveningPhoneNumber"]')
        evening_phone_input.clear()
        evening_phone_input.input(data.get('phone2', '************'))
        print(" Evening Phone")

        # 填写邮箱
        email_input = tab.ele('x://input[@id="Email"]')
        email_input.clear()
        email_input.input(data['email'])
        print(f" Email: {data['email']}")

        return True

    except Exception as e:
        print(f"填写个人信息失败: {e}")
        return False

def fill_purchase_info_with_evidence(tab, total_packages):
    """有证据模式填写购买信息"""
    try:
        # 有证据模式：只需要点击Yes，不需要填写具体购买信息
        # 滚动到购买信息部分
        purchase_yes = tab.ele('x://input[@name="PurchasedPoppiProduct"][@value="Yes"]')
        purchase_yes.scroll.to_see()
        time.sleep(1)

        # 点击Yes
        purchase_yes.click()
        print(" 点击购买确认: Yes")
        time.sleep(2)

        # 有证据模式：固定选择第4个输入框（UnitsOf12Pack）
        units_12pack_input = tab.ele('x://input[@id="UnitsOf12Pack"]')
        units_12pack_input.scroll.to_see()
        time.sleep(1)
        
        units_12pack_input.clear()
        units_12pack_input.input(str(total_packages))
        print(f" 填写第4个输入框(12包)数量: {total_packages}")

        return True

    except Exception as e:
        print(f"填写购买信息失败: {e}")
        return False

def upload_evidence_files(tab, jpg_files):
    """
    智能化证据文件上传 - 优化版本

    特性：
    - 使用不同的上传方法避免弹窗问题（file1用set_files，其他用input）
    - 智能检查上传状态，自动重试失败的文件
    - 支持多轮验证和重新上传机制
    """
    try:
        print(f"准备上传 {len(jpg_files)} 个证据文件...")

        # 点击证据上传选择框
        evidence_checkbox = tab.ele('x://*[@id="ProofOfPurchase"]')
        if not evidence_checkbox:
            print(" 错误: 找不到证据上传选择框")
            return False

        evidence_checkbox.scroll.to_see()
        time.sleep(1)
        evidence_checkbox.click()
        print(" 点击证据上传选择框")
        time.sleep(2)

        # 验证文件输入框是否可见
        print(" 检查文件上传输入框状态...")
        available_inputs = []
        for i in range(1, 11):  # file1 到 file10
            try:
                file_input = tab.ele(f'x://*[@id="file{i}"]', timeout=1)
                if file_input:
                    available_inputs.append(i)
                    print(f"   file{i}: 可用")
            except:
                print(f"   file{i}: 不可用")

        if not available_inputs:
            print(" 错误: 没有找到可用的文件输入框")
            return False

        print(f" 找到 {len(available_inputs)} 个可用的文件输入框")

        # 准备上传任务
        files_to_upload = jpg_files[:min(len(jpg_files), len(available_inputs))]
        upload_tasks = []
        for file_index, jpg_file in enumerate(files_to_upload):
            input_number = available_inputs[file_index]
            upload_tasks.append({
                'file_path': jpg_file,
                'input_number': input_number,
                'file_id': f"file{input_number}",
                'uploaded': False,
                'attempts': 0
            })

        print(f" 创建了 {len(upload_tasks)} 个上传任务")

        # 智能上传循环 - 最多尝试3轮
        max_rounds = 3
        for round_num in range(1, max_rounds + 1):
            print(f"\n=== 第 {round_num} 轮上传 ===")

            # 找出未成功上传的任务
            pending_tasks = [task for task in upload_tasks if not task['uploaded']]
            if not pending_tasks:
                print(" 所有文件已成功上传！")
                break

            print(f" 需要上传 {len(pending_tasks)} 个文件")

            # 执行上传
            for task in pending_tasks:
                task['attempts'] += 1
                success = _upload_single_file(tab, task)
                if success:
                    task['uploaded'] = True

            # 验证上传结果
            time.sleep(2)
            _verify_uploads(tab, upload_tasks)

            # 统计结果
            successful_count = sum(1 for task in upload_tasks if task['uploaded'])
            print(f" 第 {round_num} 轮完成: {successful_count}/{len(upload_tasks)} 个文件成功上传")

            if successful_count == len(upload_tasks):
                break

        # 最终结果
        final_successful = sum(1 for task in upload_tasks if task['uploaded'])
        print(f"\n最终上传结果: {final_successful}/{len(upload_tasks)} 个文件成功上传")

        # 显示失败的文件
        failed_tasks = [task for task in upload_tasks if not task['uploaded']]
        if failed_tasks:
            print(" 上传失败的文件:")
            for task in failed_tasks:
                print(f"   {task['file_id']}: {os.path.basename(task['file_path'])} (尝试 {task['attempts']} 次)")

        return final_successful > 0

    except Exception as e:
        print(f"上传证据文件失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def _upload_single_file(tab, task):
    """上传单个文件"""
    try:
        file_id = task['file_id']
        jpg_file = task['file_path']
        input_number = task['input_number']

        print(f"   正在上传 {file_id}: {os.path.basename(jpg_file)} (第 {task['attempts']} 次尝试)")

        # 检查文件是否存在
        if not os.path.exists(jpg_file):
            print(f"     错误: 文件不存在 {jpg_file}")
            return False

        # 获取文件输入框
        file_input = tab.ele(f'x://*[@id="{file_id}"]', timeout=2)
        if not file_input:
            print(f"     警告: 找不到 {file_id} 输入框")
            return False

        # 滚动到输入框
        file_input.scroll.to_see()
        time.sleep(1)  # 增加延时确保元素可见

        # 对file1进行特殊处理
        if input_number == 1:
            print(f"     file1需要特殊处理...")
            # 先清空file1的值
            try:
                file_input.clear()
                time.sleep(1)
            except:
                pass
            time.sleep(2)

        # 使用优化的上传方法 - 统一使用input方法
        try:
            # 所有文件输入框都使用input方法
            file_input.input(jpg_file)
            print(f"     上传完成: {os.path.basename(jpg_file)}")
            time.sleep(2)  # 增加延时确保上传完成
            return True

        except Exception as upload_e:
            print(f"     上传失败: {upload_e}")
            return False

    except Exception as e:
        print(f"   处理文件时发生异常: {e}")
        return False

def _verify_uploads(tab, upload_tasks):
    """验证上传结果"""
    try:
        print("   验证上传结果...")

        # 使用JavaScript检查所有文件输入框状态
        upload_status = tab.run_js("""
            const results = [];
            for (let i = 1; i <= 10; i++) {
                const input = document.getElementById(`file${i}`);
                if (input && input.files.length > 0) {
                    results.push({
                        id: `file${i}`,
                        fileName: input.files[0].name,
                        fileSize: input.files[0].size
                    });
                }
            }
            return results;
        """)

        if upload_status:
            # 根据验证结果更新任务状态
            uploaded_files = {item['id']: item['fileName'] for item in upload_status}

            for task in upload_tasks:
                file_id = task['file_id']
                expected_filename = os.path.basename(task['file_path'])

                if file_id in uploaded_files:
                    uploaded_filename = uploaded_files[file_id]
                    if expected_filename.lower() in uploaded_filename.lower():
                        task['uploaded'] = True
                        print(f"     验证成功: {file_id} -> {uploaded_filename}")
                    else:
                        task['uploaded'] = False
                        print(f"     验证失败: {file_id} 文件名不匹配")
                else:
                    task['uploaded'] = False
                    print(f"     验证失败: {file_id} 未找到文件")
        else:
            print("     验证失败: 无法获取上传状态")

    except Exception as e:
        print(f"   验证上传结果时发生异常: {e}")



def handle_payment_options(tab, data):
    """处理支付选项 - 修复选择器"""
    try:
        print("开始处理支付选项...")

        # 选择Venmo支付方式 - 修复选择器
        print("1. 选择Venmo支付方式...")
        venmo_radio = tab.ele('x://input[@name="PayBy"][@value="Venmo"]')
        venmo_radio.scroll.to_see()
        time.sleep(1)
        venmo_radio.click()
        print("✅ 选择支付方式: Venmo")
        time.sleep(2)

        # 等待Email Address单选框出现并点击 - 修复选择器
        print("2. 选择Email Address选项...")
        email_radio = tab.ele('x://input[@name="VenmoType"][@value="Email Address"]')
        email_radio.scroll.to_see()
        time.sleep(1)
        email_radio.click()
        print("✅ 选择Venmo类型: Email Address")
        time.sleep(2)

        # 填写Venmo邮箱地址 - 修复选择器
        print("3. 填写Venmo邮箱地址...")
        venmo_email_input = tab.ele('x://input[@id="VenmoEmail"]')
        venmo_email_input.clear()
        venmo_email_input.input(data['email'])
        print(f"✅ Venmo邮箱地址: {data['email']}")

        # 填写确认邮箱地址 - 修复选择器
        print("4. 填写确认邮箱地址...")
        confirm_email_input = tab.ele('x://input[@id="VenmoEmail2"]')
        confirm_email_input.clear()
        confirm_email_input.input(data['email'])
        print(f"✅ 确认Venmo邮箱地址: {data['email']}")

        # 勾选同意条款复选框
        print("5. 勾选同意条款...")
        agree_checkbox = tab.ele('x://input[@id="IAgree"]')
        agree_checkbox.scroll.to_see()
        time.sleep(1)
        agree_checkbox.click()
        print("✅ 已勾选同意条款")

        return True

    except Exception as e:
        print(f"❌ 处理支付选项失败: {e}")
        return False

def submit_form_and_get_vnb(tab):
    """提交表单并获取VNB信息 - 增强版"""
    try:
        print("开始提交表单...")

        # 点击提交按钮
        print("1. 点击Agree and Submit按钮...")
        submit_button = tab.ele('x://button[@type="submit"]')
        submit_button.scroll.to_see()
        time.sleep(1)
        submit_button.click()
        print("✅ 点击提交按钮")

        # 等待页面跳转和可能的5秒盾处理 - 智能等待模式
        print("2. 等待页面跳转和5秒盾处理（智能等待模式）...")

        # 最多等待30秒，每10秒检查一次页面状态并重试提交
        max_wait_time = 30
        check_interval = 10
        total_waited = 0
        page_loaded = False

        while total_waited < max_wait_time:
            time.sleep(check_interval)
            total_waited += check_interval

            current_url = tab.url
            print(f"第{total_waited}秒检查 - 当前页面URL: {current_url}")

            if "ClaimSubmitted" in current_url:
                print(f"✅ 页面已跳转到成功页面！（等待了{total_waited}秒）")
                page_loaded = True
                break
            else:
                print(f"⚠️ 页面还在填表页面，尝试重新提交... (已等待{total_waited}/{max_wait_time}秒)")

                # 如果还在填表页面，下滑到页面最下方并重新点击提交按钮
                try:
                    print("   📄 下滑到页面最下方...")
                    tab.scroll.to_bottom()
                    time.sleep(2)

                    print("   🔄 重新查找并点击Agree and Submit按钮...")
                    submit_button = tab.ele('x://button[@type="submit"]', timeout=5)
                    if submit_button:
                        submit_button.scroll.to_see()
                        time.sleep(1)
                        submit_button.click()
                        print("   ✅ 重新点击提交按钮成功")
                    else:
                        print("   ❌ 未找到提交按钮")
                except Exception as e:
                    print(f"   ❌ 重新提交失败: {e}")

        if not page_loaded:
            print(f"⚠️ 等待{max_wait_time}秒后页面仍未跳转到成功页面")
            current_url = tab.url
            print(f"最终页面URL: {current_url}")

        if "ClaimSubmitted" in current_url:
            print("✅ 页面成功跳转到提交成功页面")

            # 获取VNB信息 - 多种方式尝试
            print("3. 获取VNB信息...")
            vnb_info = ""

            try:
                # 方式1: 查找包含VNB的元素
                vnb_element = tab.ele('x://*[contains(text(), "VNB")]', timeout=5)
                if vnb_element:
                    vnb_text = vnb_element.text
                    print(f"找到VNB元素文本: {vnb_text}")

                    # 使用正则表达式提取VNB号码
                    import re
                    vnb_match = re.search(r'VNB[- ]?(\d+)', vnb_text)
                    if vnb_match:
                        vnb_info = f"VNB-{vnb_match.group(1)}"
                    else:
                        vnb_info = vnb_text.strip()
            except:
                print("方式1失败，尝试方式2...")

            if not vnb_info:
                try:
                    # 方式2: 获取页面全部文本并搜索
                    page_text = tab.html
                    import re
                    vnb_matches = re.findall(r'VNB[- ]?\d+', page_text, re.IGNORECASE)
                    if vnb_matches:
                        vnb_info = vnb_matches[0]
                        print(f"通过页面文本找到VNB: {vnb_info}")
                except:
                    print("方式2也失败...")

            if vnb_info:
                print(f"✅ 成功获取VNB信息: {vnb_info}")

                # 🔧 修改：不在这里单独写入VNB，只返回信息给主程序处理完整记录
                # 主程序会写入完整的用户信息+VNB格式

                return {"success": True, "vnb_info": vnb_info}
            else:
                print("❌ 未找到VNB信息")
                return {"success": False, "vnb_info": ""}
        else:
            print(f"❌ 页面未跳转到成功页面，当前URL: {current_url}")
            return {"success": False, "vnb_info": ""}

    except Exception as e:
        print(f"❌ 提交表单失败: {e}")
        return {"success": False, "vnb_info": ""}
if __name__ == "__main__":
    import sys

    # 获取用户模式选择
    mode = get_user_mode_choice()
    print(f"选择的模式: {mode}")

    if len(sys.argv) > 1:
        if sys.argv[1] == "test_cf":
            # 测试CF验证功能
            print("启动CF验证功能测试...")
            test_cf_verification()
        elif sys.argv[1] == "test_fill":
            # 测试智能填表v2功能
            print("启动智能填表v2功能测试...")
            test_smart_fill_form_v2()
        elif sys.argv[1] == "test_list":
            # 使用list01.txt数据测试
            print("启动list01.txt数据测试...")
            test_smart_fill_form_v2_with_list_data()
        else:
            print("可用的测试选项:")
            print("  python tasks3.py test_cf    - 测试CF验证功能")
            print("  python tasks3.py test_fill  - 测试智能填表v2功能")
            print("  python tasks3.py test_list  - 使用list01.txt数据测试")
            print("  python tasks3.py           - 执行完整填表任务")
    else:
        # 测试执行填表功能
        result = execute_single_claim_task3_standalone(mode)
        print(f"任务执行结果: {result}")
